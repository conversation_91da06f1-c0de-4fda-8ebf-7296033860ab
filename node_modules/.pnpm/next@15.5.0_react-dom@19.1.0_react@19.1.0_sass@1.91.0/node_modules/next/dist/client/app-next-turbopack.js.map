{"version": 3, "sources": ["../../src/client/app-next-turbopack.ts"], "sourcesContent": ["import { appBootstrap } from './app-bootstrap'\nimport { isRecoverableError } from './react-client-callbacks/on-recoverable-error'\n\nwindow.next.turbopack = true\n;(self as any).__webpack_hash__ = ''\n\n// eslint-disable-next-line @next/internal/typechecked-require\nconst instrumentationHooks = require('../lib/require-instrumentation-client')\n\nappBootstrap(() => {\n  const { hydrate } = require('./app-index') as typeof import('./app-index')\n  try {\n    hydrate(instrumentationHooks)\n  } finally {\n    if (process.env.NODE_ENV !== 'production') {\n      const { getOwnerStack } =\n        require('../next-devtools/userspace/app/errors/stitched-error') as typeof import('../next-devtools/userspace/app/errors/stitched-error')\n      const { renderAppDevOverlay } =\n        require('next/dist/compiled/next-devtools') as typeof import('next/dist/compiled/next-devtools')\n      renderAppDevOverlay(getOwnerStack, isRecoverableError)\n    }\n  }\n})\n"], "names": ["window", "next", "turbopack", "self", "__webpack_hash__", "<PERSON><PERSON><PERSON><PERSON>", "require", "appBootstrap", "hydrate", "process", "env", "NODE_ENV", "getOwnerStack", "renderAppDevOverlay", "isRecoverableError"], "mappings": ";;;;8BAA6B;oCACM;AAEnCA,OAAOC,IAAI,CAACC,SAAS,GAAG;AACtBC,KAAaC,gBAAgB,GAAG;AAElC,8DAA8D;AAC9D,MAAMC,uBAAuBC,QAAQ;AAErCC,IAAAA,0BAAY,EAAC;IACX,MAAM,EAAEC,OAAO,EAAE,GAAGF,QAAQ;IAC5B,IAAI;QACFE,QAAQH;IACV,SAAU;QACR,IAAII,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;YACzC,MAAM,EAAEC,aAAa,EAAE,GACrBN,QAAQ;YACV,MAAM,EAAEO,mBAAmB,EAAE,GAC3BP,QAAQ;YACVO,oBAAoBD,eAAeE,sCAAkB;QACvD;IACF;AACF", "ignoreList": [0]}