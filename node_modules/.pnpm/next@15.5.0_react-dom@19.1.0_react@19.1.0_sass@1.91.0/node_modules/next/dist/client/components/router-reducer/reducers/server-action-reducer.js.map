{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/server-action-reducer.ts"], "sourcesContent": ["import type {\n  ActionFlightResponse,\n  ActionResult,\n} from '../../../../server/app-render/types'\nimport { callServer } from '../../../app-call-server'\nimport { findSourceMapURL } from '../../../app-find-source-map-url'\nimport {\n  ACTION_HEADER,\n  NEXT_ACTION_NOT_FOUND_HEADER,\n  NEXT_IS_PRERENDER_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  NEXT_URL,\n  RSC_CONTENT_TYPE_HEADER,\n} from '../../app-router-headers'\nimport { UnrecognizedActionError } from '../../unrecognized-action-error'\n\n// TODO: Explicitly import from client.browser\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport {\n  createFromFetch as createFromFetchBrowser,\n  createTemporaryReferenceSet,\n  encodeReply,\n} from 'react-server-dom-webpack/client'\n\nimport {\n  PrefetchKind,\n  type ReadonlyReducerState,\n  type ReducerState,\n  type ServerActionAction,\n  type ServerActionMutable,\n} from '../router-reducer-types'\nimport { assignLocation } from '../../../assign-location'\nimport { createHrefFromUrl } from '../create-href-from-url'\nimport { handleExternalUrl } from './navigate-reducer'\nimport { applyRouterStatePatchToTree } from '../apply-router-state-patch-to-tree'\nimport { isNavigatingToNewRootLayout } from '../is-navigating-to-new-root-layout'\nimport type { CacheNode } from '../../../../shared/lib/app-router-context.shared-runtime'\nimport { handleMutable } from '../handle-mutable'\nimport { fillLazyItemsTillLeafWithHead } from '../fill-lazy-items-till-leaf-with-head'\nimport { createEmptyCacheNode } from '../../app-router'\nimport { hasInterceptionRouteInCurrentTree } from './has-interception-route-in-current-tree'\nimport { handleSegmentMismatch } from '../handle-segment-mismatch'\nimport { refreshInactiveParallelSegments } from '../refetch-inactive-parallel-segments'\nimport {\n  normalizeFlightData,\n  prepareFlightRouterStateForRequest,\n  type NormalizedFlightData,\n} from '../../../flight-data-helpers'\nimport { getRedirectError } from '../../redirect'\nimport { RedirectType } from '../../redirect-error'\nimport { createSeededPrefetchCacheEntry } from '../prefetch-cache-utils'\nimport { removeBasePath } from '../../../remove-base-path'\nimport { hasBasePath } from '../../../has-base-path'\nimport {\n  extractInfoFromServerReferenceId,\n  omitUnusedArgs,\n} from '../../../../shared/lib/server-reference-info'\nimport { revalidateEntireCache } from '../../segment-cache'\n\nconst createFromFetch =\n  createFromFetchBrowser as (typeof import('react-server-dom-webpack/client.browser'))['createFromFetch']\n\ntype FetchServerActionResult = {\n  redirectLocation: URL | undefined\n  redirectType: RedirectType | undefined\n  actionResult: ActionResult | undefined\n  actionFlightData: NormalizedFlightData[] | string | undefined\n  isPrerender: boolean\n  revalidatedParts: {\n    tag: boolean\n    cookie: boolean\n    paths: string[]\n  }\n}\n\nasync function fetchServerAction(\n  state: ReadonlyReducerState,\n  nextUrl: ReadonlyReducerState['nextUrl'],\n  { actionId, actionArgs }: ServerActionAction\n): Promise<FetchServerActionResult> {\n  const temporaryReferences = createTemporaryReferenceSet()\n  const info = extractInfoFromServerReferenceId(actionId)\n\n  // TODO: Currently, we're only omitting unused args for the experimental \"use\n  // cache\" functions. Once the server reference info byte feature is stable, we\n  // should apply this to server actions as well.\n  const usedArgs =\n    info.type === 'use-cache' ? omitUnusedArgs(actionArgs, info) : actionArgs\n\n  const body = await encodeReply(usedArgs, { temporaryReferences })\n\n  const res = await fetch(state.canonicalUrl, {\n    method: 'POST',\n    headers: {\n      Accept: RSC_CONTENT_TYPE_HEADER,\n      [ACTION_HEADER]: actionId,\n      [NEXT_ROUTER_STATE_TREE_HEADER]: prepareFlightRouterStateForRequest(\n        state.tree\n      ),\n      ...(process.env.NEXT_DEPLOYMENT_ID\n        ? {\n            'x-deployment-id': process.env.NEXT_DEPLOYMENT_ID,\n          }\n        : {}),\n      ...(nextUrl\n        ? {\n            [NEXT_URL]: nextUrl,\n          }\n        : {}),\n    },\n    body,\n  })\n\n  // Handle server actions that the server didn't recognize.\n  const unrecognizedActionHeader = res.headers.get(NEXT_ACTION_NOT_FOUND_HEADER)\n  if (unrecognizedActionHeader === '1') {\n    throw new UnrecognizedActionError(\n      `Server Action \"${actionId}\" was not found on the server. \\nRead more: https://nextjs.org/docs/messages/failed-to-find-server-action`\n    )\n  }\n\n  const redirectHeader = res.headers.get('x-action-redirect')\n  const [location, _redirectType] = redirectHeader?.split(';') || []\n  let redirectType: RedirectType | undefined\n  switch (_redirectType) {\n    case 'push':\n      redirectType = RedirectType.push\n      break\n    case 'replace':\n      redirectType = RedirectType.replace\n      break\n    default:\n      redirectType = undefined\n  }\n\n  const isPrerender = !!res.headers.get(NEXT_IS_PRERENDER_HEADER)\n  let revalidatedParts: FetchServerActionResult['revalidatedParts']\n  try {\n    const revalidatedHeader = JSON.parse(\n      res.headers.get('x-action-revalidated') || '[[],0,0]'\n    )\n    revalidatedParts = {\n      paths: revalidatedHeader[0] || [],\n      tag: !!revalidatedHeader[1],\n      cookie: revalidatedHeader[2],\n    }\n  } catch (e) {\n    revalidatedParts = NO_REVALIDATED_PARTS\n  }\n\n  const redirectLocation = location\n    ? assignLocation(\n        location,\n        new URL(state.canonicalUrl, window.location.href)\n      )\n    : undefined\n\n  const contentType = res.headers.get('content-type')\n  const isRscResponse = !!(\n    contentType && contentType.startsWith(RSC_CONTENT_TYPE_HEADER)\n  )\n\n  // Handle invalid server action responses.\n  // A valid response must have `content-type: text/x-component`, unless it's an external redirect.\n  // (external redirects have an 'x-action-redirect' header, but the body is an empty 'text/plain')\n  if (!isRscResponse && !redirectLocation) {\n    // The server can respond with a text/plain error message, but we'll fallback to something generic\n    // if there isn't one.\n    const message =\n      res.status >= 400 && contentType === 'text/plain'\n        ? await res.text()\n        : 'An unexpected response was received from the server.'\n\n    throw new Error(message)\n  }\n\n  let actionResult: FetchServerActionResult['actionResult']\n  let actionFlightData: FetchServerActionResult['actionFlightData']\n  if (isRscResponse) {\n    const response: ActionFlightResponse = await createFromFetch(\n      Promise.resolve(res),\n      { callServer, findSourceMapURL, temporaryReferences }\n    )\n\n    // An internal redirect can send an RSC response, but does not have a useful `actionResult`.\n    actionResult = redirectLocation ? undefined : response.a\n    actionFlightData = normalizeFlightData(response.f)\n  } else {\n    // An external redirect doesn't contain RSC data.\n    actionResult = undefined\n    actionFlightData = undefined\n  }\n\n  return {\n    actionResult,\n    actionFlightData,\n    redirectLocation,\n    redirectType,\n    revalidatedParts,\n    isPrerender,\n  }\n}\n\nconst NO_REVALIDATED_PARTS = {\n  paths: [],\n  tag: false,\n  cookie: false,\n}\n\n/*\n * This reducer is responsible for calling the server action and processing any side-effects from the server action.\n * It does not mutate the state by itself but rather delegates to other reducers to do the actual mutation.\n */\nexport function serverActionReducer(\n  state: ReadonlyReducerState,\n  action: ServerActionAction\n): ReducerState {\n  const { resolve, reject } = action\n  const mutable: ServerActionMutable = {}\n\n  let currentTree = state.tree\n\n  mutable.preserveCustomHistoryState = false\n\n  // only pass along the `nextUrl` param (used for interception routes) if the current route was intercepted.\n  // If the route has been intercepted, the action should be as well.\n  // Otherwise the server action might be intercepted with the wrong action id\n  // (ie, one that corresponds with the intercepted route)\n  const nextUrl =\n    state.nextUrl && hasInterceptionRouteInCurrentTree(state.tree)\n      ? state.nextUrl\n      : null\n\n  const navigatedAt = Date.now()\n\n  return fetchServerAction(state, nextUrl, action).then(\n    async ({\n      actionResult,\n      actionFlightData: flightData,\n      redirectLocation,\n      redirectType,\n      isPrerender,\n      revalidatedParts,\n    }) => {\n      let redirectHref: string | undefined\n\n      // honor the redirect type instead of defaulting to push in case of server actions.\n      if (redirectLocation) {\n        if (redirectType === RedirectType.replace) {\n          state.pushRef.pendingPush = false\n          mutable.pendingPush = false\n        } else {\n          state.pushRef.pendingPush = true\n          mutable.pendingPush = true\n        }\n\n        redirectHref = createHrefFromUrl(redirectLocation, false)\n        mutable.canonicalUrl = redirectHref\n      }\n\n      if (!flightData) {\n        resolve(actionResult)\n\n        // If there is a redirect but no flight data we need to do a mpaNavigation.\n        if (redirectLocation) {\n          return handleExternalUrl(\n            state,\n            mutable,\n            redirectLocation.href,\n            state.pushRef.pendingPush\n          )\n        }\n        return state\n      }\n\n      if (typeof flightData === 'string') {\n        // Handle case when navigating to page in `pages` from `app`\n        resolve(actionResult)\n\n        return handleExternalUrl(\n          state,\n          mutable,\n          flightData,\n          state.pushRef.pendingPush\n        )\n      }\n\n      const actionRevalidated =\n        revalidatedParts.paths.length > 0 ||\n        revalidatedParts.tag ||\n        revalidatedParts.cookie\n\n      for (const normalizedFlightData of flightData) {\n        const {\n          tree: treePatch,\n          seedData: cacheNodeSeedData,\n          head,\n          isRootRender,\n        } = normalizedFlightData\n\n        if (!isRootRender) {\n          // TODO-APP: handle this case better\n          console.log('SERVER ACTION APPLY FAILED')\n          resolve(actionResult)\n\n          return state\n        }\n\n        // Given the path can only have two items the items are only the router state and rsc for the root.\n        const newTree = applyRouterStatePatchToTree(\n          // TODO-APP: remove ''\n          [''],\n          currentTree,\n          treePatch,\n          redirectHref ? redirectHref : state.canonicalUrl\n        )\n\n        if (newTree === null) {\n          resolve(actionResult)\n\n          return handleSegmentMismatch(state, action, treePatch)\n        }\n\n        if (isNavigatingToNewRootLayout(currentTree, newTree)) {\n          resolve(actionResult)\n\n          return handleExternalUrl(\n            state,\n            mutable,\n            redirectHref || state.canonicalUrl,\n            state.pushRef.pendingPush\n          )\n        }\n\n        // The server sent back RSC data for the server action, so we need to apply it to the cache.\n        if (cacheNodeSeedData !== null) {\n          const rsc = cacheNodeSeedData[1]\n          const cache: CacheNode = createEmptyCacheNode()\n          cache.rsc = rsc\n          cache.prefetchRsc = null\n          cache.loading = cacheNodeSeedData[3]\n          fillLazyItemsTillLeafWithHead(\n            navigatedAt,\n            cache,\n            // Existing cache is not passed in as server actions have to invalidate the entire cache.\n            undefined,\n            treePatch,\n            cacheNodeSeedData,\n            head,\n            undefined\n          )\n\n          mutable.cache = cache\n          if (process.env.__NEXT_CLIENT_SEGMENT_CACHE) {\n            revalidateEntireCache(state.nextUrl, newTree)\n          } else {\n            mutable.prefetchCache = new Map()\n          }\n          if (actionRevalidated) {\n            await refreshInactiveParallelSegments({\n              navigatedAt,\n              state,\n              updatedTree: newTree,\n              updatedCache: cache,\n              includeNextUrl: Boolean(nextUrl),\n              canonicalUrl: mutable.canonicalUrl || state.canonicalUrl,\n            })\n          }\n        }\n\n        mutable.patchedTree = newTree\n        currentTree = newTree\n      }\n\n      if (redirectLocation && redirectHref) {\n        if (!process.env.__NEXT_CLIENT_SEGMENT_CACHE && !actionRevalidated) {\n          // Because the RedirectBoundary will trigger a navigation, we need to seed the prefetch cache\n          // with the FlightData that we got from the server action for the target page, so that it's\n          // available when the page is navigated to and doesn't need to be re-fetched.\n          // We only do this if the server action didn't revalidate any data, as in that case the\n          // client cache will be cleared and the data will be re-fetched anyway.\n          // NOTE: We don't do this in the Segment Cache implementation.\n          // Dynamic data should never be placed into the cache, unless it's\n          // \"converted\" to static data using <Link prefetch={true}>. What we\n          // do instead is re-prefetch links and forms whenever the cache is\n          // invalidated.\n          createSeededPrefetchCacheEntry({\n            url: redirectLocation,\n            data: {\n              flightData,\n              canonicalUrl: undefined,\n              couldBeIntercepted: false,\n              prerendered: false,\n              postponed: false,\n              // TODO: We should be able to set this if the server action\n              // returned a fully static response.\n              staleTime: -1,\n            },\n            tree: state.tree,\n            prefetchCache: state.prefetchCache,\n            nextUrl: state.nextUrl,\n            kind: isPrerender ? PrefetchKind.FULL : PrefetchKind.AUTO,\n          })\n          mutable.prefetchCache = state.prefetchCache\n        }\n\n        // If the action triggered a redirect, the action promise will be rejected with\n        // a redirect so that it's handled by RedirectBoundary as we won't have a valid\n        // action result to resolve the promise with. This will effectively reset the state of\n        // the component that called the action as the error boundary will remount the tree.\n        // The status code doesn't matter here as the action handler will have already sent\n        // a response with the correct status code.\n        reject(\n          getRedirectError(\n            hasBasePath(redirectHref)\n              ? removeBasePath(redirectHref)\n              : redirectHref,\n            redirectType || RedirectType.push\n          )\n        )\n      } else {\n        resolve(actionResult)\n      }\n\n      return handleMutable(state, mutable)\n    },\n    (e: any) => {\n      // When the server action is rejected we don't update the state and instead call the reject handler of the promise.\n      reject(e)\n\n      return state\n    }\n  )\n}\n"], "names": ["serverActionReducer", "createFromFetch", "createFromFetchBrowser", "fetchServerAction", "state", "nextUrl", "actionId", "actionArgs", "temporaryReferences", "createTemporaryReferenceSet", "info", "extractInfoFromServerReferenceId", "usedArgs", "type", "omit<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "body", "encodeReply", "res", "fetch", "canonicalUrl", "method", "headers", "Accept", "RSC_CONTENT_TYPE_HEADER", "ACTION_HEADER", "NEXT_ROUTER_STATE_TREE_HEADER", "prepareFlightRouterStateForRequest", "tree", "process", "env", "NEXT_DEPLOYMENT_ID", "NEXT_URL", "unrecognizedActionHeader", "get", "NEXT_ACTION_NOT_FOUND_HEADER", "UnrecognizedActionError", "redirectHeader", "location", "_redirectType", "split", "redirectType", "RedirectType", "push", "replace", "undefined", "<PERSON><PERSON><PERSON><PERSON>", "NEXT_IS_PRERENDER_HEADER", "revalidatedParts", "revalidatedHeader", "JSON", "parse", "paths", "tag", "cookie", "e", "NO_REVALIDATED_PARTS", "redirectLocation", "assignLocation", "URL", "window", "href", "contentType", "isRscResponse", "startsWith", "message", "status", "text", "Error", "actionResult", "actionFlightData", "response", "Promise", "resolve", "callServer", "findSourceMapURL", "a", "normalizeFlightData", "f", "action", "reject", "mutable", "currentTree", "preserveCustomHistoryState", "hasInterceptionRouteInCurrentTree", "navigatedAt", "Date", "now", "then", "flightData", "redirectHref", "pushRef", "pendingPush", "createHrefFromUrl", "handleExternalUrl", "actionRevalidated", "length", "normalizedFlightData", "treePatch", "seedData", "cacheNodeSeedData", "head", "isRootRender", "console", "log", "newTree", "applyRouterStatePatchToTree", "handleSegmentMismatch", "isNavigatingToNewRootLayout", "rsc", "cache", "createEmptyCacheNode", "prefetchRsc", "loading", "fillLazyItemsTillLeafWithHead", "__NEXT_CLIENT_SEGMENT_CACHE", "revalidateEntireCache", "prefetchCache", "Map", "refreshInactiveParallelSegments", "updatedTree", "updatedCache", "includeNextUrl", "Boolean", "patchedTree", "createSeededPrefetchCacheEntry", "url", "data", "couldBeIntercepted", "prerendered", "postponed", "staleTime", "kind", "PrefetchKind", "FULL", "AUTO", "getRedirectError", "has<PERSON>ase<PERSON><PERSON>", "removeBasePath", "handleMutable"], "mappings": ";;;;+BAqNgBA;;;eAAAA;;;+BAjNW;qCACM;kCAQ1B;yCACiC;wBAQjC;oCAQA;gCACwB;mCACG;iCACA;6CACU;6CACA;+BAEd;+CACgB;2BACT;mDACa;uCACZ;iDACU;mCAKzC;0BAC0B;+BACJ;oCACkB;gCAChB;6BACH;qCAIrB;8BAC+B;AAEtC,MAAMC,kBACJC,uBAAsB;AAexB,eAAeC,kBACbC,KAA2B,EAC3BC,OAAwC,EACxC,KAA4C;IAA5C,IAAA,EAAEC,QAAQ,EAAEC,UAAU,EAAsB,GAA5C;IAEA,MAAMC,sBAAsBC,IAAAA,mCAA2B;IACvD,MAAMC,OAAOC,IAAAA,qDAAgC,EAACL;IAE9C,6EAA6E;IAC7E,8EAA8E;IAC9E,+CAA+C;IAC/C,MAAMM,WACJF,KAAKG,IAAI,KAAK,cAAcC,IAAAA,mCAAc,EAACP,YAAYG,QAAQH;IAEjE,MAAMQ,OAAO,MAAMC,IAAAA,mBAAW,EAACJ,UAAU;QAAEJ;IAAoB;IAE/D,MAAMS,MAAM,MAAMC,MAAMd,MAAMe,YAAY,EAAE;QAC1CC,QAAQ;QACRC,SAAS;YACPC,QAAQC,yCAAuB;YAC/B,CAACC,+BAAa,CAAC,EAAElB;YACjB,CAACmB,+CAA6B,CAAC,EAAEC,IAAAA,qDAAkC,EACjEtB,MAAMuB,IAAI;YAEZ,GAAIC,QAAQC,GAAG,CAACC,kBAAkB,GAC9B;gBACE,mBAAmBF,QAAQC,GAAG,CAACC,kBAAkB;YACnD,IACA,CAAC,CAAC;YACN,GAAIzB,UACA;gBACE,CAAC0B,0BAAQ,CAAC,EAAE1B;YACd,IACA,CAAC,CAAC;QACR;QACAU;IACF;IAEA,0DAA0D;IAC1D,MAAMiB,2BAA2Bf,IAAII,OAAO,CAACY,GAAG,CAACC,8CAA4B;IAC7E,IAAIF,6BAA6B,KAAK;QACpC,MAAM,qBAEL,CAFK,IAAIG,gDAAuB,CAC/B,AAAC,oBAAiB7B,WAAS,8GADvB,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAM8B,iBAAiBnB,IAAII,OAAO,CAACY,GAAG,CAAC;IACvC,MAAM,CAACI,UAAUC,cAAc,GAAGF,CAAAA,kCAAAA,eAAgBG,KAAK,CAAC,SAAQ,EAAE;IAClE,IAAIC;IACJ,OAAQF;QACN,KAAK;YACHE,eAAeC,2BAAY,CAACC,IAAI;YAChC;QACF,KAAK;YACHF,eAAeC,2BAAY,CAACE,OAAO;YACnC;QACF;YACEH,eAAeI;IACnB;IAEA,MAAMC,cAAc,CAAC,CAAC5B,IAAII,OAAO,CAACY,GAAG,CAACa,0CAAwB;IAC9D,IAAIC;IACJ,IAAI;QACF,MAAMC,oBAAoBC,KAAKC,KAAK,CAClCjC,IAAII,OAAO,CAACY,GAAG,CAAC,2BAA2B;QAE7Cc,mBAAmB;YACjBI,OAAOH,iBAAiB,CAAC,EAAE,IAAI,EAAE;YACjCI,KAAK,CAAC,CAACJ,iBAAiB,CAAC,EAAE;YAC3BK,QAAQL,iBAAiB,CAAC,EAAE;QAC9B;IACF,EAAE,OAAOM,GAAG;QACVP,mBAAmBQ;IACrB;IAEA,MAAMC,mBAAmBnB,WACrBoB,IAAAA,8BAAc,EACZpB,UACA,IAAIqB,IAAItD,MAAMe,YAAY,EAAEwC,OAAOtB,QAAQ,CAACuB,IAAI,KAElDhB;IAEJ,MAAMiB,cAAc5C,IAAII,OAAO,CAACY,GAAG,CAAC;IACpC,MAAM6B,gBAAgB,CAAC,CACrBD,CAAAA,eAAeA,YAAYE,UAAU,CAACxC,yCAAuB,CAAA;IAG/D,0CAA0C;IAC1C,iGAAiG;IACjG,iGAAiG;IACjG,IAAI,CAACuC,iBAAiB,CAACN,kBAAkB;QACvC,kGAAkG;QAClG,sBAAsB;QACtB,MAAMQ,UACJ/C,IAAIgD,MAAM,IAAI,OAAOJ,gBAAgB,eACjC,MAAM5C,IAAIiD,IAAI,KACd;QAEN,MAAM,qBAAkB,CAAlB,IAAIC,MAAMH,UAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAiB;IACzB;IAEA,IAAII;IACJ,IAAIC;IACJ,IAAIP,eAAe;QACjB,MAAMQ,WAAiC,MAAMrE,gBAC3CsE,QAAQC,OAAO,CAACvD,MAChB;YAAEwD,YAAAA,yBAAU;YAAEC,kBAAAA,qCAAgB;YAAElE;QAAoB;QAGtD,4FAA4F;QAC5F4D,eAAeZ,mBAAmBZ,YAAY0B,SAASK,CAAC;QACxDN,mBAAmBO,IAAAA,sCAAmB,EAACN,SAASO,CAAC;IACnD,OAAO;QACL,iDAAiD;QACjDT,eAAexB;QACfyB,mBAAmBzB;IACrB;IAEA,OAAO;QACLwB;QACAC;QACAb;QACAhB;QACAO;QACAF;IACF;AACF;AAEA,MAAMU,uBAAuB;IAC3BJ,OAAO,EAAE;IACTC,KAAK;IACLC,QAAQ;AACV;AAMO,SAASrD,oBACdI,KAA2B,EAC3B0E,MAA0B;IAE1B,MAAM,EAAEN,OAAO,EAAEO,MAAM,EAAE,GAAGD;IAC5B,MAAME,UAA+B,CAAC;IAEtC,IAAIC,cAAc7E,MAAMuB,IAAI;IAE5BqD,QAAQE,0BAA0B,GAAG;IAErC,2GAA2G;IAC3G,mEAAmE;IACnE,4EAA4E;IAC5E,wDAAwD;IACxD,MAAM7E,UACJD,MAAMC,OAAO,IAAI8E,IAAAA,oEAAiC,EAAC/E,MAAMuB,IAAI,IACzDvB,MAAMC,OAAO,GACb;IAEN,MAAM+E,cAAcC,KAAKC,GAAG;IAE5B,OAAOnF,kBAAkBC,OAAOC,SAASyE,QAAQS,IAAI,CACnD;YAAO,EACLnB,YAAY,EACZC,kBAAkBmB,UAAU,EAC5BhC,gBAAgB,EAChBhB,YAAY,EACZK,WAAW,EACXE,gBAAgB,EACjB;QACC,IAAI0C;QAEJ,mFAAmF;QACnF,IAAIjC,kBAAkB;YACpB,IAAIhB,iBAAiBC,2BAAY,CAACE,OAAO,EAAE;gBACzCvC,MAAMsF,OAAO,CAACC,WAAW,GAAG;gBAC5BX,QAAQW,WAAW,GAAG;YACxB,OAAO;gBACLvF,MAAMsF,OAAO,CAACC,WAAW,GAAG;gBAC5BX,QAAQW,WAAW,GAAG;YACxB;YAEAF,eAAeG,IAAAA,oCAAiB,EAACpC,kBAAkB;YACnDwB,QAAQ7D,YAAY,GAAGsE;QACzB;QAEA,IAAI,CAACD,YAAY;YACfhB,QAAQJ;YAER,2EAA2E;YAC3E,IAAIZ,kBAAkB;gBACpB,OAAOqC,IAAAA,kCAAiB,EACtBzF,OACA4E,SACAxB,iBAAiBI,IAAI,EACrBxD,MAAMsF,OAAO,CAACC,WAAW;YAE7B;YACA,OAAOvF;QACT;QAEA,IAAI,OAAOoF,eAAe,UAAU;YAClC,4DAA4D;YAC5DhB,QAAQJ;YAER,OAAOyB,IAAAA,kCAAiB,EACtBzF,OACA4E,SACAQ,YACApF,MAAMsF,OAAO,CAACC,WAAW;QAE7B;QAEA,MAAMG,oBACJ/C,iBAAiBI,KAAK,CAAC4C,MAAM,GAAG,KAChChD,iBAAiBK,GAAG,IACpBL,iBAAiBM,MAAM;QAEzB,KAAK,MAAM2C,wBAAwBR,WAAY;YAC7C,MAAM,EACJ7D,MAAMsE,SAAS,EACfC,UAAUC,iBAAiB,EAC3BC,IAAI,EACJC,YAAY,EACb,GAAGL;YAEJ,IAAI,CAACK,cAAc;gBACjB,oCAAoC;gBACpCC,QAAQC,GAAG,CAAC;gBACZ/B,QAAQJ;gBAER,OAAOhE;YACT;YAEA,mGAAmG;YACnG,MAAMoG,UAAUC,IAAAA,wDAA2B,EACzC,sBAAsB;YACtB;gBAAC;aAAG,EACJxB,aACAgB,WACAR,eAAeA,eAAerF,MAAMe,YAAY;YAGlD,IAAIqF,YAAY,MAAM;gBACpBhC,QAAQJ;gBAER,OAAOsC,IAAAA,4CAAqB,EAACtG,OAAO0E,QAAQmB;YAC9C;YAEA,IAAIU,IAAAA,wDAA2B,EAAC1B,aAAauB,UAAU;gBACrDhC,QAAQJ;gBAER,OAAOyB,IAAAA,kCAAiB,EACtBzF,OACA4E,SACAS,gBAAgBrF,MAAMe,YAAY,EAClCf,MAAMsF,OAAO,CAACC,WAAW;YAE7B;YAEA,4FAA4F;YAC5F,IAAIQ,sBAAsB,MAAM;gBAC9B,MAAMS,MAAMT,iBAAiB,CAAC,EAAE;gBAChC,MAAMU,QAAmBC,IAAAA,+BAAoB;gBAC7CD,MAAMD,GAAG,GAAGA;gBACZC,MAAME,WAAW,GAAG;gBACpBF,MAAMG,OAAO,GAAGb,iBAAiB,CAAC,EAAE;gBACpCc,IAAAA,4DAA6B,EAC3B7B,aACAyB,OACA,yFAAyF;gBACzFjE,WACAqD,WACAE,mBACAC,MACAxD;gBAGFoC,QAAQ6B,KAAK,GAAGA;gBAChB,IAAIjF,QAAQC,GAAG,CAACqF,2BAA2B,EAAE;oBAC3CC,IAAAA,mCAAqB,EAAC/G,MAAMC,OAAO,EAAEmG;gBACvC,OAAO;oBACLxB,QAAQoC,aAAa,GAAG,IAAIC;gBAC9B;gBACA,IAAIvB,mBAAmB;oBACrB,MAAMwB,IAAAA,gEAA+B,EAAC;wBACpClC;wBACAhF;wBACAmH,aAAaf;wBACbgB,cAAcX;wBACdY,gBAAgBC,QAAQrH;wBACxBc,cAAc6D,QAAQ7D,YAAY,IAAIf,MAAMe,YAAY;oBAC1D;gBACF;YACF;YAEA6D,QAAQ2C,WAAW,GAAGnB;YACtBvB,cAAcuB;QAChB;QAEA,IAAIhD,oBAAoBiC,cAAc;YACpC,IAAI,CAAC7D,QAAQC,GAAG,CAACqF,2BAA2B,IAAI,CAACpB,mBAAmB;gBAClE,6FAA6F;gBAC7F,2FAA2F;gBAC3F,6EAA6E;gBAC7E,uFAAuF;gBACvF,uEAAuE;gBACvE,8DAA8D;gBAC9D,kEAAkE;gBAClE,mEAAmE;gBACnE,kEAAkE;gBAClE,eAAe;gBACf8B,IAAAA,kDAA8B,EAAC;oBAC7BC,KAAKrE;oBACLsE,MAAM;wBACJtC;wBACArE,cAAcyB;wBACdmF,oBAAoB;wBACpBC,aAAa;wBACbC,WAAW;wBACX,2DAA2D;wBAC3D,oCAAoC;wBACpCC,WAAW,CAAC;oBACd;oBACAvG,MAAMvB,MAAMuB,IAAI;oBAChByF,eAAehH,MAAMgH,aAAa;oBAClC/G,SAASD,MAAMC,OAAO;oBACtB8H,MAAMtF,cAAcuF,gCAAY,CAACC,IAAI,GAAGD,gCAAY,CAACE,IAAI;gBAC3D;gBACAtD,QAAQoC,aAAa,GAAGhH,MAAMgH,aAAa;YAC7C;YAEA,+EAA+E;YAC/E,+EAA+E;YAC/E,sFAAsF;YACtF,oFAAoF;YACpF,mFAAmF;YACnF,2CAA2C;YAC3CrC,OACEwD,IAAAA,0BAAgB,EACdC,IAAAA,wBAAW,EAAC/C,gBACRgD,IAAAA,8BAAc,EAAChD,gBACfA,cACJjD,gBAAgBC,2BAAY,CAACC,IAAI;QAGvC,OAAO;YACL8B,QAAQJ;QACV;QAEA,OAAOsE,IAAAA,4BAAa,EAACtI,OAAO4E;IAC9B,GACA,CAAC1B;QACC,mHAAmH;QACnHyB,OAAOzB;QAEP,OAAOlD;IACT;AAEJ", "ignoreList": [0]}