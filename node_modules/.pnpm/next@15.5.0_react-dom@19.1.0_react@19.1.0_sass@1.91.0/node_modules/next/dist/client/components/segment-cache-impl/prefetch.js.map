{"version": 3, "sources": ["../../../../src/client/components/segment-cache-impl/prefetch.ts"], "sourcesContent": ["import type { Flight<PERSON>outerState } from '../../../server/app-render/types'\nimport { createPrefetchURL } from '../app-router'\nimport { createCacheKey } from './cache-key'\nimport { schedulePrefetchTask } from './scheduler'\nimport {\n  PrefetchPriority,\n  type PrefetchTaskFetchStrategy,\n} from '../segment-cache'\n\n/**\n * Entrypoint for prefetching a URL into the Segment Cache.\n * @param href - The URL to prefetch. Typically this will come from a <Link>,\n * or router.prefetch. It must be validated before we attempt to prefetch it.\n * @param nextUrl - A special header used by the server for interception routes.\n * Roughly corresponds to the current URL.\n * @param treeAtTimeOfPrefetch - The FlightRouterState at the time the prefetch\n * was requested. This is only used when PPR is disabled.\n * @param fetchStrategy - Whether to prefetch dynamic data, in addition to\n * static data. This is used by `<Link prefetch={true}>`.\n * @param onInvalidate - A callback that will be called when the prefetch cache\n * When called, it signals to the listener that the data associated with the\n * prefetch may have been invalidated from the cache. This is not a live\n * subscription — it's called at most once per `prefetch` call. The only\n * supported use case is to trigger a new prefetch inside the listener, if\n * desired. It also may be called even in cases where the associated data is\n * still cached. Prefetching is a poll-based (pull) operation, not an event-\n * based (push) one. Rather than subscribe to specific cache entries, you\n * occasionally poll the prefetch cache to check if anything is missing.\n */\nexport function prefetch(\n  href: string,\n  nextUrl: string | null,\n  treeAtTimeOfPrefetch: FlightRouterState,\n  fetchStrategy: PrefetchTaskFetchStrategy,\n  onInvalidate: null | (() => void)\n) {\n  const url = createPrefetchURL(href)\n  if (url === null) {\n    // This href should not be prefetched.\n    return\n  }\n  const cacheKey = createCacheKey(url.href, nextUrl)\n  schedulePrefetchTask(\n    cacheKey,\n    treeAtTimeOfPrefetch,\n    fetchStrategy,\n    PrefetchPriority.Default,\n    onInvalidate\n  )\n}\n"], "names": ["prefetch", "href", "nextUrl", "treeAtTimeOfPrefetch", "fetchStrategy", "onInvalidate", "url", "createPrefetchURL", "cache<PERSON>ey", "createCacheKey", "schedulePrefetchTask", "PrefetchPriority", "<PERSON><PERSON><PERSON>"], "mappings": ";;;;+BA6BgBA;;;eAAAA;;;2BA5BkB;0BACH;2BACM;8BAI9B;AAsBA,SAASA,SACdC,IAAY,EACZC,OAAsB,EACtBC,oBAAuC,EACvCC,aAAwC,EACxCC,YAAiC;IAEjC,MAAMC,MAAMC,IAAAA,4BAAiB,EAACN;IAC9B,IAAIK,QAAQ,MAAM;QAChB,sCAAsC;QACtC;IACF;IACA,MAAME,WAAWC,IAAAA,wBAAc,EAACH,IAAIL,IAAI,EAAEC;IAC1CQ,IAAAA,+BAAoB,EAClBF,UACAL,sBACAC,eACAO,8BAAgB,CAACC,OAAO,EACxBP;AAEJ", "ignoreList": [0]}