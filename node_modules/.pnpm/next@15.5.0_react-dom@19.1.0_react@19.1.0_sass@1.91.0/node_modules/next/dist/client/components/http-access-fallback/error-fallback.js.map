{"version": 3, "sources": ["../../../../src/client/components/http-access-fallback/error-fallback.tsx"], "sourcesContent": ["import { styles } from '../styles/access-error-styles'\n\nexport function HTTPAccessErrorFallback({\n  status,\n  message,\n}: {\n  status: number\n  message: string\n}) {\n  return (\n    <>\n      {/* <head> */}\n      <title>{`${status}: ${message}`}</title>\n      {/* </head> */}\n      <div style={styles.error}>\n        <div>\n          <style\n            dangerouslySetInnerHTML={{\n              /* Minified CSS from\n                body { margin: 0; color: #000; background: #fff; }\n                .next-error-h1 {\n                  border-right: 1px solid rgba(0, 0, 0, .3);\n                }\n\n                @media (prefers-color-scheme: dark) {\n                  body { color: #fff; background: #000; }\n                  .next-error-h1 {\n                    border-right: 1px solid rgba(255, 255, 255, .3);\n                  }\n                }\n              */\n              __html: `body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}`,\n            }}\n          />\n          <h1 className=\"next-error-h1\" style={styles.h1}>\n            {status}\n          </h1>\n          <div style={styles.desc}>\n            <h2 style={styles.h2}>{message}</h2>\n          </div>\n        </div>\n      </div>\n    </>\n  )\n}\n"], "names": ["HTTPAccessErrorFallback", "status", "message", "title", "div", "style", "styles", "error", "dangerouslySetInnerHTML", "__html", "h1", "className", "desc", "h2"], "mappings": ";;;;+BAEgBA;;;eAAAA;;;;mCAFO;AAEhB,SAASA,wBAAwB,KAMvC;IANuC,IAAA,EACtCC,MAAM,EACNC,OAAO,EAIR,GANuC;IAOtC,qBACE;;0BAEE,qBAACC;0BAAO,AAAGF,SAAO,OAAIC;;0BAEtB,qBAACE;gBAAIC,OAAOC,yBAAM,CAACC,KAAK;0BACtB,cAAA,sBAACH;;sCACC,qBAACC;4BACCG,yBAAyB;gCACvB;;;;;;;;;;;;cAYA,GACAC,QAAS;4BACX;;sCAEF,qBAACC;4BAAGC,WAAU;4BAAgBN,OAAOC,yBAAM,CAACI,EAAE;sCAC3CT;;sCAEH,qBAACG;4BAAIC,OAAOC,yBAAM,CAACM,IAAI;sCACrB,cAAA,qBAACC;gCAAGR,OAAOC,yBAAM,CAACO,EAAE;0CAAGX;;;;;;;;AAMnC", "ignoreList": [0]}