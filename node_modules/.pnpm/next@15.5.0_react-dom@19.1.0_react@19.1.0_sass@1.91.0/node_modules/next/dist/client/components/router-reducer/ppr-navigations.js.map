{"version": 3, "sources": ["../../../../src/client/components/router-reducer/ppr-navigations.ts"], "sourcesContent": ["import type {\n  CacheNodeSeedData,\n  FlightRouterState,\n  FlightSegmentPath,\n  Segment,\n} from '../../../server/app-render/types'\nimport type {\n  CacheNode,\n  ChildSegmentMap,\n  HeadData,\n  LoadingModuleData,\n  ReadyCacheNode,\n} from '../../../shared/lib/app-router-context.shared-runtime'\nimport { DEFAULT_SEGMENT_KEY } from '../../../shared/lib/segment'\nimport { matchSegment } from '../match-segments'\nimport { createRouterCacheKey } from './create-router-cache-key'\nimport type { FetchServerResponseResult } from './fetch-server-response'\nimport { isNavigatingToNewRootLayout } from './is-navigating-to-new-root-layout'\nimport { DYNAMIC_STALETIME_MS } from './prefetch-cache-utils'\n\n// This is yet another tree type that is used to track pending promises that\n// need to be fulfilled once the dynamic data is received. The terminal nodes of\n// this tree represent the new Cache Node trees that were created during this\n// request. We can't use the Cache Node tree or Route State tree directly\n// because those include reused nodes, too. This tree is discarded as soon as\n// the navigation response is received.\ntype SPANavigationTask = {\n  // The router state that corresponds to the tree that this Task represents.\n  route: FlightRouterState\n  // The CacheNode that corresponds to the tree that this Task represents. If\n  // `children` is null (i.e. if this is a terminal task node), then `node`\n  // represents a brand new Cache Node tree, which way or may not need to be\n  // filled with dynamic data from the server.\n  node: CacheNode | null\n  // The tree sent to the server during the dynamic request. This is the\n  // same as `route`, except with the `refetch` marker set on dynamic segments.\n  // If all the segments are static, then this will be null, and no server\n  // request is required.\n  dynamicRequestTree: FlightRouterState | null\n  children: Map<string, SPANavigationTask> | null\n}\n\n// A special type used to bail out and trigger a full-page navigation.\ntype MPANavigationTask = {\n  // MPA tasks are distinguised from SPA tasks by having a null `route`.\n  route: null\n  node: null\n  dynamicRequestTree: null\n  children: null\n}\n\nconst MPA_NAVIGATION_TASK: MPANavigationTask = {\n  route: null,\n  node: null,\n  dynamicRequestTree: null,\n  children: null,\n}\n\nexport type Task = SPANavigationTask | MPANavigationTask\n\n// Creates a new Cache Node tree (i.e. copy-on-write) that represents the\n// optimistic result of a navigation, using both the current Cache Node tree and\n// data that was prefetched prior to navigation.\n//\n// At the moment we call this function, we haven't yet received the navigation\n// response from the server. It could send back something completely different\n// from the tree that was prefetched — due to rewrites, default routes, parallel\n// routes, etc.\n//\n// But in most cases, it will return the same tree that we prefetched, just with\n// the dynamic holes filled in. So we optimistically assume this will happen,\n// and accept that the real result could be arbitrarily different.\n//\n// We'll reuse anything that was already in the previous tree, since that's what\n// the server does.\n//\n// New segments (ones that don't appear in the old tree) are assigned an\n// unresolved promise. The data for these promises will be fulfilled later, when\n// the navigation response is received.\n//\n// The tree can be rendered immediately after it is created (that's why this is\n// a synchronous function). Any new trees that do not have prefetch data will\n// suspend during rendering, until the dynamic data streams in.\n//\n// Returns a Task object, which contains both the updated Cache Node and a path\n// to the pending subtrees that need to be resolved by the navigation response.\n//\n// A return value of `null` means there were no changes, and the previous tree\n// can be reused without initiating a server request.\nexport function startPPRNavigation(\n  navigatedAt: number,\n  oldCacheNode: CacheNode,\n  oldRouterState: FlightRouterState,\n  newRouterState: FlightRouterState,\n  prefetchData: CacheNodeSeedData | null,\n  prefetchHead: HeadData | null,\n  isPrefetchHeadPartial: boolean,\n  isSamePageNavigation: boolean,\n  scrollableSegmentsResult: Array<FlightSegmentPath>\n): Task | null {\n  const segmentPath: Array<FlightSegmentPath> = []\n  return updateCacheNodeOnNavigation(\n    navigatedAt,\n    oldCacheNode,\n    oldRouterState,\n    newRouterState,\n    false,\n    prefetchData,\n    prefetchHead,\n    isPrefetchHeadPartial,\n    isSamePageNavigation,\n    segmentPath,\n    scrollableSegmentsResult\n  )\n}\n\nfunction updateCacheNodeOnNavigation(\n  navigatedAt: number,\n  oldCacheNode: CacheNode,\n  oldRouterState: FlightRouterState,\n  newRouterState: FlightRouterState,\n  didFindRootLayout: boolean,\n  prefetchData: CacheNodeSeedData | null,\n  prefetchHead: HeadData | null,\n  isPrefetchHeadPartial: boolean,\n  isSamePageNavigation: boolean,\n  segmentPath: FlightSegmentPath,\n  scrollableSegmentsResult: Array<FlightSegmentPath>\n): Task | null {\n  // Diff the old and new trees to reuse the shared layouts.\n  const oldRouterStateChildren = oldRouterState[1]\n  const newRouterStateChildren = newRouterState[1]\n  const prefetchDataChildren = prefetchData !== null ? prefetchData[2] : null\n\n  if (!didFindRootLayout) {\n    // We're currently traversing the part of the tree that was also part of\n    // the previous route. If we discover a root layout, then we don't need to\n    // trigger an MPA navigation. See beginRenderingNewRouteTree for context.\n    const isRootLayout = newRouterState[4] === true\n    if (isRootLayout) {\n      // Found a matching root layout.\n      didFindRootLayout = true\n    }\n  }\n\n  const oldParallelRoutes = oldCacheNode.parallelRoutes\n\n  // Clone the current set of segment children, even if they aren't active in\n  // the new tree.\n  // TODO: We currently retain all the inactive segments indefinitely, until\n  // there's an explicit refresh, or a parent layout is lazily refreshed. We\n  // rely on this for popstate navigations, which update the Router State Tree\n  // but do not eagerly perform a data fetch, because they expect the segment\n  // data to already be in the Cache Node tree. For highly static sites that\n  // are mostly read-only, this may happen only rarely, causing memory to\n  // leak. We should figure out a better model for the lifetime of inactive\n  // segments, so we can maintain instant back/forward navigations without\n  // leaking memory indefinitely.\n  const prefetchParallelRoutes = new Map(oldParallelRoutes)\n\n  // As we diff the trees, we may sometimes modify (copy-on-write, not mutate)\n  // the Route Tree that was returned by the server — for example, in the case\n  // of default parallel routes, we preserve the currently active segment. To\n  // avoid mutating the original tree, we clone the router state children along\n  // the return path.\n  let patchedRouterStateChildren: {\n    [parallelRouteKey: string]: FlightRouterState\n  } = {}\n  let taskChildren = null\n\n  // Most navigations require a request to fetch additional data from the\n  // server, either because the data was not already prefetched, or because the\n  // target route contains dynamic data that cannot be prefetched.\n  //\n  // However, if the target route is fully static, and it's already completely\n  // loaded into the segment cache, then we can skip the server request.\n  //\n  // This starts off as `false`, and is set to `true` if any of the child\n  // routes requires a dynamic request.\n  let needsDynamicRequest = false\n  // As we traverse the children, we'll construct a FlightRouterState that can\n  // be sent to the server to request the dynamic data. If it turns out that\n  // nothing in the subtree is dynamic (i.e. needsDynamicRequest is false at the\n  // end), then this will be discarded.\n  // TODO: We can probably optimize the format of this data structure to only\n  // include paths that are dynamic. Instead of reusing the\n  // FlightRouterState type.\n  let dynamicRequestTreeChildren: {\n    [parallelRouteKey: string]: FlightRouterState\n  } = {}\n\n  for (let parallelRouteKey in newRouterStateChildren) {\n    const newRouterStateChild: FlightRouterState =\n      newRouterStateChildren[parallelRouteKey]\n    const oldRouterStateChild: FlightRouterState | void =\n      oldRouterStateChildren[parallelRouteKey]\n    const oldSegmentMapChild = oldParallelRoutes.get(parallelRouteKey)\n    const prefetchDataChild: CacheNodeSeedData | void | null =\n      prefetchDataChildren !== null\n        ? prefetchDataChildren[parallelRouteKey]\n        : null\n\n    const newSegmentChild = newRouterStateChild[0]\n    const newSegmentPathChild = segmentPath.concat([\n      parallelRouteKey,\n      newSegmentChild,\n    ])\n    const newSegmentKeyChild = createRouterCacheKey(newSegmentChild)\n\n    const oldSegmentChild =\n      oldRouterStateChild !== undefined ? oldRouterStateChild[0] : undefined\n\n    const oldCacheNodeChild =\n      oldSegmentMapChild !== undefined\n        ? oldSegmentMapChild.get(newSegmentKeyChild)\n        : undefined\n\n    let taskChild: Task | null\n    if (newSegmentChild === DEFAULT_SEGMENT_KEY) {\n      // This is another kind of leaf segment — a default route.\n      //\n      // Default routes have special behavior. When there's no matching segment\n      // for a parallel route, Next.js preserves the currently active segment\n      // during a client navigation — but not for initial render. The server\n      // leaves it to the client to account for this. So we need to handle\n      // it here.\n      if (oldRouterStateChild !== undefined) {\n        // Reuse the existing Router State for this segment. We spawn a \"task\"\n        // just to keep track of the updated router state; unlike most, it's\n        // already fulfilled and won't be affected by the dynamic response.\n        taskChild = spawnReusedTask(oldRouterStateChild)\n      } else {\n        // There's no currently active segment. Switch to the \"create\" path.\n        taskChild = beginRenderingNewRouteTree(\n          navigatedAt,\n          oldRouterStateChild,\n          newRouterStateChild,\n          oldCacheNodeChild,\n          didFindRootLayout,\n          prefetchDataChild !== undefined ? prefetchDataChild : null,\n          prefetchHead,\n          isPrefetchHeadPartial,\n          newSegmentPathChild,\n          scrollableSegmentsResult\n        )\n      }\n    } else if (\n      isSamePageNavigation &&\n      // Check if this is a page segment.\n      // TODO: We're not consistent about how we do this check. Some places\n      // check if the segment starts with PAGE_SEGMENT_KEY, but most seem to\n      // check if there any any children, which is why I'm doing it here. We\n      // should probably encode an empty children set as `null` though. Either\n      // way, we should update all the checks to be consistent.\n      Object.keys(newRouterStateChild[1]).length === 0\n    ) {\n      // We special case navigations to the exact same URL as the current\n      // location. It's a common UI pattern for apps to refresh when you click a\n      // link to the current page. So when this happens, we refresh the dynamic\n      // data in the page segments.\n      //\n      // Note that this does not apply if the any part of the hash or search\n      // query has changed. This might feel a bit weird but it makes more sense\n      // when you consider that the way to trigger this behavior is to click\n      // the same link multiple times.\n      //\n      // TODO: We should probably refresh the *entire* route when this case\n      // occurs, not just the page segments. Essentially treating it the same as\n      // a refresh() triggered by an action, which is the more explicit way of\n      // modeling the UI pattern described above.\n      //\n      // Also note that this only refreshes the dynamic data, not static/\n      // cached data. If the page segment is fully static and prefetched, the\n      // request is skipped. (This is also how refresh() works.)\n      taskChild = beginRenderingNewRouteTree(\n        navigatedAt,\n        oldRouterStateChild,\n        newRouterStateChild,\n        oldCacheNodeChild,\n        didFindRootLayout,\n        prefetchDataChild !== undefined ? prefetchDataChild : null,\n        prefetchHead,\n        isPrefetchHeadPartial,\n        newSegmentPathChild,\n        scrollableSegmentsResult\n      )\n    } else if (\n      oldRouterStateChild !== undefined &&\n      oldSegmentChild !== undefined &&\n      matchSegment(newSegmentChild, oldSegmentChild)\n    ) {\n      if (\n        oldCacheNodeChild !== undefined &&\n        oldRouterStateChild !== undefined\n      ) {\n        // This segment exists in both the old and new trees. Recursively update\n        // the children.\n        taskChild = updateCacheNodeOnNavigation(\n          navigatedAt,\n          oldCacheNodeChild,\n          oldRouterStateChild,\n          newRouterStateChild,\n          didFindRootLayout,\n          prefetchDataChild,\n          prefetchHead,\n          isPrefetchHeadPartial,\n          isSamePageNavigation,\n          newSegmentPathChild,\n          scrollableSegmentsResult\n        )\n      } else {\n        // There's no existing Cache Node for this segment. Switch to the\n        // \"create\" path.\n        taskChild = beginRenderingNewRouteTree(\n          navigatedAt,\n          oldRouterStateChild,\n          newRouterStateChild,\n          oldCacheNodeChild,\n          didFindRootLayout,\n          prefetchDataChild !== undefined ? prefetchDataChild : null,\n          prefetchHead,\n          isPrefetchHeadPartial,\n          newSegmentPathChild,\n          scrollableSegmentsResult\n        )\n      }\n    } else {\n      // This is a new tree. Switch to the \"create\" path.\n      taskChild = beginRenderingNewRouteTree(\n        navigatedAt,\n        oldRouterStateChild,\n        newRouterStateChild,\n        oldCacheNodeChild,\n        didFindRootLayout,\n        prefetchDataChild !== undefined ? prefetchDataChild : null,\n        prefetchHead,\n        isPrefetchHeadPartial,\n        newSegmentPathChild,\n        scrollableSegmentsResult\n      )\n    }\n\n    if (taskChild !== null) {\n      // Recursively propagate up the child tasks.\n\n      if (taskChild.route === null) {\n        // One of the child tasks discovered a change to the root layout.\n        // Immediately unwind from this recursive traversal.\n        return MPA_NAVIGATION_TASK\n      }\n\n      if (taskChildren === null) {\n        taskChildren = new Map()\n      }\n      taskChildren.set(parallelRouteKey, taskChild)\n      const newCacheNodeChild = taskChild.node\n      if (newCacheNodeChild !== null) {\n        const newSegmentMapChild: ChildSegmentMap = new Map(oldSegmentMapChild)\n        newSegmentMapChild.set(newSegmentKeyChild, newCacheNodeChild)\n        prefetchParallelRoutes.set(parallelRouteKey, newSegmentMapChild)\n      }\n\n      // The child tree's route state may be different from the prefetched\n      // route sent by the server. We need to clone it as we traverse back up\n      // the tree.\n      const taskChildRoute = taskChild.route\n      patchedRouterStateChildren[parallelRouteKey] = taskChildRoute\n\n      const dynamicRequestTreeChild = taskChild.dynamicRequestTree\n      if (dynamicRequestTreeChild !== null) {\n        // Something in the child tree is dynamic.\n        needsDynamicRequest = true\n        dynamicRequestTreeChildren[parallelRouteKey] = dynamicRequestTreeChild\n      } else {\n        dynamicRequestTreeChildren[parallelRouteKey] = taskChildRoute\n      }\n    } else {\n      // The child didn't change. We can use the prefetched router state.\n      patchedRouterStateChildren[parallelRouteKey] = newRouterStateChild\n      dynamicRequestTreeChildren[parallelRouteKey] = newRouterStateChild\n    }\n  }\n\n  if (taskChildren === null) {\n    // No new tasks were spawned.\n    return null\n  }\n\n  const newCacheNode: ReadyCacheNode = {\n    lazyData: null,\n    rsc: oldCacheNode.rsc,\n    // We intentionally aren't updating the prefetchRsc field, since this node\n    // is already part of the current tree, because it would be weird for\n    // prefetch data to be newer than the final data. It probably won't ever be\n    // observable anyway, but it could happen if the segment is unmounted then\n    // mounted again, because LayoutRouter will momentarily switch to rendering\n    // prefetchRsc, via useDeferredValue.\n    prefetchRsc: oldCacheNode.prefetchRsc,\n    head: oldCacheNode.head,\n    prefetchHead: oldCacheNode.prefetchHead,\n    loading: oldCacheNode.loading,\n\n    // Everything is cloned except for the children, which we computed above.\n    parallelRoutes: prefetchParallelRoutes,\n\n    navigatedAt,\n  }\n\n  return {\n    // Return a cloned copy of the router state with updated children.\n    route: patchRouterStateWithNewChildren(\n      newRouterState,\n      patchedRouterStateChildren\n    ),\n    node: newCacheNode,\n    dynamicRequestTree: needsDynamicRequest\n      ? patchRouterStateWithNewChildren(\n          newRouterState,\n          dynamicRequestTreeChildren\n        )\n      : null,\n    children: taskChildren,\n  }\n}\n\nfunction beginRenderingNewRouteTree(\n  navigatedAt: number,\n  oldRouterState: FlightRouterState | void,\n  newRouterState: FlightRouterState,\n  existingCacheNode: CacheNode | void,\n  didFindRootLayout: boolean,\n  prefetchData: CacheNodeSeedData | null,\n  possiblyPartialPrefetchHead: HeadData | null,\n  isPrefetchHeadPartial: boolean,\n  segmentPath: FlightSegmentPath,\n  scrollableSegmentsResult: Array<FlightSegmentPath>\n): Task {\n  if (!didFindRootLayout) {\n    // The route tree changed before we reached a layout. (The highest-level\n    // layout in a route tree is referred to as the \"root\" layout.) This could\n    // mean that we're navigating between two different root layouts. When this\n    // happens, we perform a full-page (MPA-style) navigation.\n    //\n    // However, the algorithm for deciding where to start rendering a route\n    // (i.e. the one performed in order to reach this function) is stricter\n    // than the one used to detect a change in the root layout. So just because\n    // we're re-rendering a segment outside of the root layout does not mean we\n    // should trigger a full-page navigation.\n    //\n    // Specifically, we handle dynamic parameters differently: two segments are\n    // considered the same even if their parameter values are different.\n    //\n    // Refer to isNavigatingToNewRootLayout for details.\n    //\n    // Note that we only have to perform this extra traversal if we didn't\n    // already discover a root layout in the part of the tree that is unchanged.\n    // In the common case, this branch is skipped completely.\n    if (\n      oldRouterState === undefined ||\n      isNavigatingToNewRootLayout(oldRouterState, newRouterState)\n    ) {\n      // The root layout changed. Perform a full-page navigation.\n      return MPA_NAVIGATION_TASK\n    }\n  }\n  return createCacheNodeOnNavigation(\n    navigatedAt,\n    newRouterState,\n    existingCacheNode,\n    prefetchData,\n    possiblyPartialPrefetchHead,\n    isPrefetchHeadPartial,\n    segmentPath,\n    scrollableSegmentsResult\n  )\n}\n\nfunction createCacheNodeOnNavigation(\n  navigatedAt: number,\n  routerState: FlightRouterState,\n  existingCacheNode: CacheNode | void,\n  prefetchData: CacheNodeSeedData | null,\n  possiblyPartialPrefetchHead: HeadData | null,\n  isPrefetchHeadPartial: boolean,\n  segmentPath: FlightSegmentPath,\n  scrollableSegmentsResult: Array<FlightSegmentPath>\n): SPANavigationTask {\n  // Same traversal as updateCacheNodeNavigation, but we switch to this path\n  // once we reach the part of the tree that was not in the previous route. We\n  // don't need to diff against the old tree, we just need to create a new one.\n\n  // The head is assigned to every leaf segment delivered by the server. Based\n  // on corresponding logic in fill-lazy-items-till-leaf-with-head.ts\n  const routerStateChildren = routerState[1]\n  const isLeafSegment = Object.keys(routerStateChildren).length === 0\n\n  // Even we're rendering inside the \"new\" part of the target tree, we may have\n  // a locally cached segment that we can reuse. This may come from either 1)\n  // the CacheNode tree, which lives in React state and is populated by previous\n  // navigations; or 2) the prefetch cache, which is a separate cache that is\n  // populated by prefetches.\n  let rsc: React.ReactNode\n  let loading: LoadingModuleData | Promise<LoadingModuleData>\n  let head: HeadData | null\n  let cacheNodeNavigatedAt: number\n  if (\n    existingCacheNode !== undefined &&\n    // DYNAMIC_STALETIME_MS defaults to 0, but it can be increased using\n    // the experimental.staleTimes.dynamic config. When set, we'll avoid\n    // refetching dynamic data if it was fetched within the given threshold.\n    existingCacheNode.navigatedAt + DYNAMIC_STALETIME_MS > navigatedAt\n  ) {\n    // We have an existing CacheNode for this segment, and it's not stale. We\n    // should reuse it rather than request a new one.\n    rsc = existingCacheNode.rsc\n    loading = existingCacheNode.loading\n    head = existingCacheNode.head\n\n    // Don't update the navigatedAt timestamp, since we're reusing stale data.\n    cacheNodeNavigatedAt = existingCacheNode.navigatedAt\n  } else if (prefetchData !== null) {\n    // There's no existing CacheNode for this segment, but we do have prefetch\n    // data. If the prefetch data is fully static (i.e. does not contain any\n    // dynamic holes), we don't need to request it from the server.\n    rsc = prefetchData[1]\n    loading = prefetchData[3]\n    head = isLeafSegment ? possiblyPartialPrefetchHead : null\n    // Even though we're accessing the data from the prefetch cache, this is\n    // conceptually a new segment, not a reused one. So we should update the\n    // navigatedAt timestamp.\n    cacheNodeNavigatedAt = navigatedAt\n    const isPrefetchRscPartial = prefetchData[4]\n    if (\n      // Check if the segment data is partial\n      isPrefetchRscPartial ||\n      // Check if the head is partial (only relevant if this is a leaf segment)\n      (isPrefetchHeadPartial && isLeafSegment)\n    ) {\n      // We only have partial data from this segment. Like missing segments, we\n      // must request the full data from the server.\n      return spawnPendingTask(\n        navigatedAt,\n        routerState,\n        prefetchData,\n        possiblyPartialPrefetchHead,\n        isPrefetchHeadPartial,\n        segmentPath,\n        scrollableSegmentsResult\n      )\n    } else {\n      // The prefetch data is fully static, so we can omit it from the\n      // navigation request.\n    }\n  } else {\n    // There's no prefetch for this segment. Everything from this point will be\n    // requested from the server, even if there are static children below it.\n    // Create a terminal task node that will later be fulfilled by\n    // server response.\n    return spawnPendingTask(\n      navigatedAt,\n      routerState,\n      null,\n      possiblyPartialPrefetchHead,\n      isPrefetchHeadPartial,\n      segmentPath,\n      scrollableSegmentsResult\n    )\n  }\n\n  // We already have a full segment we can render, so we don't need to request a\n  // new one from the server. Keep traversing down the tree until we reach\n  // something that requires a dynamic request.\n  const prefetchDataChildren = prefetchData !== null ? prefetchData[2] : null\n  const taskChildren = new Map()\n  const existingCacheNodeChildren =\n    existingCacheNode !== undefined ? existingCacheNode.parallelRoutes : null\n  const cacheNodeChildren = new Map(existingCacheNodeChildren)\n  let dynamicRequestTreeChildren: {\n    [parallelRouteKey: string]: FlightRouterState\n  } = {}\n  let needsDynamicRequest = false\n  if (isLeafSegment) {\n    // The segment path of every leaf segment (i.e. page) is collected into\n    // a result array. This is used by the LayoutRouter to scroll to ensure that\n    // new pages are visible after a navigation.\n    // TODO: We should use a string to represent the segment path instead of\n    // an array. We already use a string representation for the path when\n    // accessing the Segment Cache, so we can use the same one.\n    scrollableSegmentsResult.push(segmentPath)\n  } else {\n    for (let parallelRouteKey in routerStateChildren) {\n      const routerStateChild: FlightRouterState =\n        routerStateChildren[parallelRouteKey]\n      const prefetchDataChild: CacheNodeSeedData | void | null =\n        prefetchDataChildren !== null\n          ? prefetchDataChildren[parallelRouteKey]\n          : null\n      const existingSegmentMapChild =\n        existingCacheNodeChildren !== null\n          ? existingCacheNodeChildren.get(parallelRouteKey)\n          : undefined\n      const segmentChild = routerStateChild[0]\n      const segmentPathChild = segmentPath.concat([\n        parallelRouteKey,\n        segmentChild,\n      ])\n      const segmentKeyChild = createRouterCacheKey(segmentChild)\n\n      const existingCacheNodeChild =\n        existingSegmentMapChild !== undefined\n          ? existingSegmentMapChild.get(segmentKeyChild)\n          : undefined\n\n      const taskChild = createCacheNodeOnNavigation(\n        navigatedAt,\n        routerStateChild,\n        existingCacheNodeChild,\n        prefetchDataChild,\n        possiblyPartialPrefetchHead,\n        isPrefetchHeadPartial,\n        segmentPathChild,\n        scrollableSegmentsResult\n      )\n      taskChildren.set(parallelRouteKey, taskChild)\n      const dynamicRequestTreeChild = taskChild.dynamicRequestTree\n      if (dynamicRequestTreeChild !== null) {\n        // Something in the child tree is dynamic.\n        needsDynamicRequest = true\n        dynamicRequestTreeChildren[parallelRouteKey] = dynamicRequestTreeChild\n      } else {\n        dynamicRequestTreeChildren[parallelRouteKey] = routerStateChild\n      }\n      const newCacheNodeChild = taskChild.node\n      if (newCacheNodeChild !== null) {\n        const newSegmentMapChild: ChildSegmentMap = new Map()\n        newSegmentMapChild.set(segmentKeyChild, newCacheNodeChild)\n        cacheNodeChildren.set(parallelRouteKey, newSegmentMapChild)\n      }\n    }\n  }\n\n  return {\n    // Since we're inside a new route tree, unlike the\n    // `updateCacheNodeOnNavigation` path, the router state on the children\n    // tasks is always the same as the router state we pass in. So we don't need\n    // to clone/modify it.\n    route: routerState,\n    node: {\n      lazyData: null,\n      // Since this segment is already full, we don't need to use the\n      // `prefetchRsc` field.\n      rsc,\n      prefetchRsc: null,\n      head,\n      prefetchHead: null,\n      loading,\n      parallelRoutes: cacheNodeChildren,\n      navigatedAt: cacheNodeNavigatedAt,\n    },\n    dynamicRequestTree: needsDynamicRequest\n      ? patchRouterStateWithNewChildren(routerState, dynamicRequestTreeChildren)\n      : null,\n    children: taskChildren,\n  }\n}\n\nfunction patchRouterStateWithNewChildren(\n  baseRouterState: FlightRouterState,\n  newChildren: { [parallelRouteKey: string]: FlightRouterState }\n): FlightRouterState {\n  const clone: FlightRouterState = [baseRouterState[0], newChildren]\n  // Based on equivalent logic in apply-router-state-patch-to-tree, but should\n  // confirm whether we need to copy all of these fields. Not sure the server\n  // ever sends, e.g. the refetch marker.\n  if (2 in baseRouterState) {\n    clone[2] = baseRouterState[2]\n  }\n  if (3 in baseRouterState) {\n    clone[3] = baseRouterState[3]\n  }\n  if (4 in baseRouterState) {\n    clone[4] = baseRouterState[4]\n  }\n  return clone\n}\n\nfunction spawnPendingTask(\n  navigatedAt: number,\n  routerState: FlightRouterState,\n  prefetchData: CacheNodeSeedData | null,\n  prefetchHead: HeadData | null,\n  isPrefetchHeadPartial: boolean,\n  segmentPath: FlightSegmentPath,\n  scrollableSegmentsResult: Array<FlightSegmentPath>\n): SPANavigationTask {\n  // Create a task that will later be fulfilled by data from the server.\n\n  // Clone the prefetched route tree and the `refetch` marker to it. We'll send\n  // this to the server so it knows where to start rendering.\n  const dynamicRequestTree = patchRouterStateWithNewChildren(\n    routerState,\n    routerState[1]\n  )\n  dynamicRequestTree[3] = 'refetch'\n\n  const newTask: Task = {\n    route: routerState,\n\n    // Corresponds to the part of the route that will be rendered on the server.\n    node: createPendingCacheNode(\n      navigatedAt,\n      routerState,\n      prefetchData,\n      prefetchHead,\n      isPrefetchHeadPartial,\n      segmentPath,\n      scrollableSegmentsResult\n    ),\n    // Because this is non-null, and it gets propagated up through the parent\n    // tasks, the root task will know that it needs to perform a server request.\n    dynamicRequestTree,\n    children: null,\n  }\n  return newTask\n}\n\nfunction spawnReusedTask(reusedRouterState: FlightRouterState): Task {\n  // Create a task that reuses an existing segment, e.g. when reusing\n  // the current active segment in place of a default route.\n  return {\n    route: reusedRouterState,\n    node: null,\n    dynamicRequestTree: null,\n    children: null,\n  }\n}\n\n// Writes a dynamic server response into the tree created by\n// updateCacheNodeOnNavigation. All pending promises that were spawned by the\n// navigation will be resolved, either with dynamic data from the server, or\n// `null` to indicate that the data is missing.\n//\n// A `null` value will trigger a lazy fetch during render, which will then patch\n// up the tree using the same mechanism as the non-PPR implementation\n// (serverPatchReducer).\n//\n// Usually, the server will respond with exactly the subset of data that we're\n// waiting for — everything below the nearest shared layout. But technically,\n// the server can return anything it wants.\n//\n// This does _not_ create a new tree; it modifies the existing one in place.\n// Which means it must follow the Suspense rules of cache safety.\nexport function listenForDynamicRequest(\n  task: SPANavigationTask,\n  responsePromise: Promise<FetchServerResponseResult>\n) {\n  responsePromise.then(\n    ({ flightData }: FetchServerResponseResult) => {\n      if (typeof flightData === 'string') {\n        // Happens when navigating to page in `pages` from `app`. We shouldn't\n        // get here because should have already handled this during\n        // the prefetch.\n        return\n      }\n      for (const normalizedFlightData of flightData) {\n        const {\n          segmentPath,\n          tree: serverRouterState,\n          seedData: dynamicData,\n          head: dynamicHead,\n        } = normalizedFlightData\n\n        if (!dynamicData) {\n          // This shouldn't happen. PPR should always send back a response.\n          // However, `FlightDataPath` is a shared type and the pre-PPR handling of\n          // this might return null.\n          continue\n        }\n\n        writeDynamicDataIntoPendingTask(\n          task,\n          segmentPath,\n          serverRouterState,\n          dynamicData,\n          dynamicHead\n        )\n      }\n\n      // Now that we've exhausted all the data we received from the server, if\n      // there are any remaining pending tasks in the tree, abort them now.\n      // If there's any missing data, it will trigger a lazy fetch.\n      abortTask(task, null)\n    },\n    (error: any) => {\n      // This will trigger an error during render\n      abortTask(task, error)\n    }\n  )\n}\n\nfunction writeDynamicDataIntoPendingTask(\n  rootTask: SPANavigationTask,\n  segmentPath: FlightSegmentPath,\n  serverRouterState: FlightRouterState,\n  dynamicData: CacheNodeSeedData,\n  dynamicHead: HeadData\n) {\n  // The data sent by the server represents only a subtree of the app. We need\n  // to find the part of the task tree that matches the server response, and\n  // fulfill it using the dynamic data.\n  //\n  // segmentPath represents the parent path of subtree. It's a repeating pattern\n  // of parallel route key and segment:\n  //\n  //   [string, Segment, string, Segment, string, Segment, ...]\n  //\n  // Iterate through the path and finish any tasks that match this payload.\n  let task = rootTask\n  for (let i = 0; i < segmentPath.length; i += 2) {\n    const parallelRouteKey: string = segmentPath[i]\n    const segment: Segment = segmentPath[i + 1]\n    const taskChildren = task.children\n    if (taskChildren !== null) {\n      const taskChild = taskChildren.get(parallelRouteKey)\n      if (taskChild !== undefined) {\n        const taskSegment = taskChild.route[0]\n        if (matchSegment(segment, taskSegment)) {\n          // Found a match for this task. Keep traversing down the task tree.\n          task = taskChild\n          continue\n        }\n      }\n    }\n    // We didn't find a child task that matches the server data. Exit. We won't\n    // abort the task, though, because a different FlightDataPath may be able to\n    // fulfill it (see loop in listenForDynamicRequest). We only abort tasks\n    // once we've run out of data.\n    return\n  }\n\n  finishTaskUsingDynamicDataPayload(\n    task,\n    serverRouterState,\n    dynamicData,\n    dynamicHead\n  )\n}\n\nfunction finishTaskUsingDynamicDataPayload(\n  task: SPANavigationTask,\n  serverRouterState: FlightRouterState,\n  dynamicData: CacheNodeSeedData,\n  dynamicHead: HeadData\n) {\n  if (task.dynamicRequestTree === null) {\n    // Everything in this subtree is already complete. Bail out.\n    return\n  }\n\n  // dynamicData may represent a larger subtree than the task. Before we can\n  // finish the task, we need to line them up.\n  const taskChildren = task.children\n  const taskNode = task.node\n  if (taskChildren === null) {\n    // We've reached the leaf node of the pending task. The server data tree\n    // lines up the pending Cache Node tree. We can now switch to the\n    // normal algorithm.\n    if (taskNode !== null) {\n      finishPendingCacheNode(\n        taskNode,\n        task.route,\n        serverRouterState,\n        dynamicData,\n        dynamicHead\n      )\n      // Set this to null to indicate that this task is now complete.\n      task.dynamicRequestTree = null\n    }\n    return\n  }\n  // The server returned more data than we need to finish the task. Skip over\n  // the extra segments until we reach the leaf task node.\n  const serverChildren = serverRouterState[1]\n  const dynamicDataChildren = dynamicData[2]\n\n  for (const parallelRouteKey in serverRouterState) {\n    const serverRouterStateChild: FlightRouterState =\n      serverChildren[parallelRouteKey]\n    const dynamicDataChild: CacheNodeSeedData | null | void =\n      dynamicDataChildren[parallelRouteKey]\n\n    const taskChild = taskChildren.get(parallelRouteKey)\n    if (taskChild !== undefined) {\n      const taskSegment = taskChild.route[0]\n      if (\n        matchSegment(serverRouterStateChild[0], taskSegment) &&\n        dynamicDataChild !== null &&\n        dynamicDataChild !== undefined\n      ) {\n        // Found a match for this task. Keep traversing down the task tree.\n        return finishTaskUsingDynamicDataPayload(\n          taskChild,\n          serverRouterStateChild,\n          dynamicDataChild,\n          dynamicHead\n        )\n      }\n    }\n    // We didn't find a child task that matches the server data. We won't abort\n    // the task, though, because a different FlightDataPath may be able to\n    // fulfill it (see loop in listenForDynamicRequest). We only abort tasks\n    // once we've run out of data.\n  }\n}\n\nfunction createPendingCacheNode(\n  navigatedAt: number,\n  routerState: FlightRouterState,\n  prefetchData: CacheNodeSeedData | null,\n  prefetchHead: HeadData | null,\n  isPrefetchHeadPartial: boolean,\n  segmentPath: FlightSegmentPath,\n  scrollableSegmentsResult: Array<FlightSegmentPath>\n): ReadyCacheNode {\n  const routerStateChildren = routerState[1]\n  const prefetchDataChildren = prefetchData !== null ? prefetchData[2] : null\n\n  const parallelRoutes = new Map()\n  for (let parallelRouteKey in routerStateChildren) {\n    const routerStateChild: FlightRouterState =\n      routerStateChildren[parallelRouteKey]\n    const prefetchDataChild: CacheNodeSeedData | null | void =\n      prefetchDataChildren !== null\n        ? prefetchDataChildren[parallelRouteKey]\n        : null\n\n    const segmentChild = routerStateChild[0]\n    const segmentPathChild = segmentPath.concat([\n      parallelRouteKey,\n      segmentChild,\n    ])\n    const segmentKeyChild = createRouterCacheKey(segmentChild)\n\n    const newCacheNodeChild = createPendingCacheNode(\n      navigatedAt,\n      routerStateChild,\n      prefetchDataChild === undefined ? null : prefetchDataChild,\n      prefetchHead,\n      isPrefetchHeadPartial,\n      segmentPathChild,\n      scrollableSegmentsResult\n    )\n\n    const newSegmentMapChild: ChildSegmentMap = new Map()\n    newSegmentMapChild.set(segmentKeyChild, newCacheNodeChild)\n    parallelRoutes.set(parallelRouteKey, newSegmentMapChild)\n  }\n\n  // The head is assigned to every leaf segment delivered by the server. Based\n  // on corresponding logic in fill-lazy-items-till-leaf-with-head.ts\n  const isLeafSegment = parallelRoutes.size === 0\n\n  if (isLeafSegment) {\n    // The segment path of every leaf segment (i.e. page) is collected into\n    // a result array. This is used by the LayoutRouter to scroll to ensure that\n    // new pages are visible after a navigation.\n    // TODO: We should use a string to represent the segment path instead of\n    // an array. We already use a string representation for the path when\n    // accessing the Segment Cache, so we can use the same one.\n    scrollableSegmentsResult.push(segmentPath)\n  }\n\n  const maybePrefetchRsc = prefetchData !== null ? prefetchData[1] : null\n  const maybePrefetchLoading = prefetchData !== null ? prefetchData[3] : null\n  return {\n    lazyData: null,\n    parallelRoutes: parallelRoutes,\n\n    prefetchRsc: maybePrefetchRsc !== undefined ? maybePrefetchRsc : null,\n    prefetchHead: isLeafSegment ? prefetchHead : [null, null],\n\n    // TODO: Technically, a loading boundary could contain dynamic data. We must\n    // have separate `loading` and `prefetchLoading` fields to handle this, like\n    // we do for the segment data and head.\n    loading: maybePrefetchLoading !== undefined ? maybePrefetchLoading : null,\n\n    // Create a deferred promise. This will be fulfilled once the dynamic\n    // response is received from the server.\n    rsc: createDeferredRsc() as React.ReactNode,\n    head: isLeafSegment ? (createDeferredRsc() as React.ReactNode) : null,\n\n    navigatedAt,\n  }\n}\n\nfunction finishPendingCacheNode(\n  cacheNode: CacheNode,\n  taskState: FlightRouterState,\n  serverState: FlightRouterState,\n  dynamicData: CacheNodeSeedData,\n  dynamicHead: HeadData\n): void {\n  // Writes a dynamic response into an existing Cache Node tree. This does _not_\n  // create a new tree, it updates the existing tree in-place. So it must follow\n  // the Suspense rules of cache safety — it can resolve pending promises, but\n  // it cannot overwrite existing data. It can add segments to the tree (because\n  // a missing segment will cause the layout router to suspend).\n  // but it cannot delete them.\n  //\n  // We must resolve every promise in the tree, or else it will suspend\n  // indefinitely. If we did not receive data for a segment, we will resolve its\n  // data promise to `null` to trigger a lazy fetch during render.\n  const taskStateChildren = taskState[1]\n  const serverStateChildren = serverState[1]\n  const dataChildren = dynamicData[2]\n\n  // The router state that we traverse the tree with (taskState) is the same one\n  // that we used to construct the pending Cache Node tree. That way we're sure\n  // to resolve all the pending promises.\n  const parallelRoutes = cacheNode.parallelRoutes\n  for (let parallelRouteKey in taskStateChildren) {\n    const taskStateChild: FlightRouterState =\n      taskStateChildren[parallelRouteKey]\n    const serverStateChild: FlightRouterState | void =\n      serverStateChildren[parallelRouteKey]\n    const dataChild: CacheNodeSeedData | null | void =\n      dataChildren[parallelRouteKey]\n\n    const segmentMapChild = parallelRoutes.get(parallelRouteKey)\n    const taskSegmentChild = taskStateChild[0]\n    const taskSegmentKeyChild = createRouterCacheKey(taskSegmentChild)\n\n    const cacheNodeChild =\n      segmentMapChild !== undefined\n        ? segmentMapChild.get(taskSegmentKeyChild)\n        : undefined\n\n    if (cacheNodeChild !== undefined) {\n      if (\n        serverStateChild !== undefined &&\n        matchSegment(taskSegmentChild, serverStateChild[0])\n      ) {\n        if (dataChild !== undefined && dataChild !== null) {\n          // This is the happy path. Recursively update all the children.\n          finishPendingCacheNode(\n            cacheNodeChild,\n            taskStateChild,\n            serverStateChild,\n            dataChild,\n            dynamicHead\n          )\n        } else {\n          // The server never returned data for this segment. Trigger a lazy\n          // fetch during render. This shouldn't happen because the Route Tree\n          // and the Seed Data tree sent by the server should always be the same\n          // shape when part of the same server response.\n          abortPendingCacheNode(taskStateChild, cacheNodeChild, null)\n        }\n      } else {\n        // The server never returned data for this segment. Trigger a lazy\n        // fetch during render.\n        abortPendingCacheNode(taskStateChild, cacheNodeChild, null)\n      }\n    } else {\n      // The server response matches what was expected to receive, but there's\n      // no matching Cache Node in the task tree. This is a bug in the\n      // implementation because we should have created a node for every\n      // segment in the tree that's associated with this task.\n    }\n  }\n\n  // Use the dynamic data from the server to fulfill the deferred RSC promise\n  // on the Cache Node.\n  const rsc = cacheNode.rsc\n  const dynamicSegmentData = dynamicData[1]\n  if (rsc === null) {\n    // This is a lazy cache node. We can overwrite it. This is only safe\n    // because we know that the LayoutRouter suspends if `rsc` is `null`.\n    cacheNode.rsc = dynamicSegmentData\n  } else if (isDeferredRsc(rsc)) {\n    // This is a deferred RSC promise. We can fulfill it with the data we just\n    // received from the server. If it was already resolved by a different\n    // navigation, then this does nothing because we can't overwrite data.\n    rsc.resolve(dynamicSegmentData)\n  } else {\n    // This is not a deferred RSC promise, nor is it empty, so it must have\n    // been populated by a different navigation. We must not overwrite it.\n  }\n\n  // Check if this is a leaf segment. If so, it will have a `head` property with\n  // a pending promise that needs to be resolved with the dynamic head from\n  // the server.\n  const head = cacheNode.head\n  if (isDeferredRsc(head)) {\n    head.resolve(dynamicHead)\n  }\n}\n\nexport function abortTask(task: SPANavigationTask, error: any): void {\n  const cacheNode = task.node\n  if (cacheNode === null) {\n    // This indicates the task is already complete.\n    return\n  }\n\n  const taskChildren = task.children\n  if (taskChildren === null) {\n    // Reached the leaf task node. This is the root of a pending cache\n    // node tree.\n    abortPendingCacheNode(task.route, cacheNode, error)\n  } else {\n    // This is an intermediate task node. Keep traversing until we reach a\n    // task node with no children. That will be the root of the cache node tree\n    // that needs to be resolved.\n    for (const taskChild of taskChildren.values()) {\n      abortTask(taskChild, error)\n    }\n  }\n\n  // Set this to null to indicate that this task is now complete.\n  task.dynamicRequestTree = null\n}\n\nfunction abortPendingCacheNode(\n  routerState: FlightRouterState,\n  cacheNode: CacheNode,\n  error: any\n): void {\n  // For every pending segment in the tree, resolve its `rsc` promise to `null`\n  // to trigger a lazy fetch during render.\n  //\n  // Or, if an error object is provided, it will error instead.\n  const routerStateChildren = routerState[1]\n  const parallelRoutes = cacheNode.parallelRoutes\n  for (let parallelRouteKey in routerStateChildren) {\n    const routerStateChild: FlightRouterState =\n      routerStateChildren[parallelRouteKey]\n    const segmentMapChild = parallelRoutes.get(parallelRouteKey)\n    if (segmentMapChild === undefined) {\n      // This shouldn't happen because we're traversing the same tree that was\n      // used to construct the cache nodes in the first place.\n      continue\n    }\n    const segmentChild = routerStateChild[0]\n    const segmentKeyChild = createRouterCacheKey(segmentChild)\n    const cacheNodeChild = segmentMapChild.get(segmentKeyChild)\n    if (cacheNodeChild !== undefined) {\n      abortPendingCacheNode(routerStateChild, cacheNodeChild, error)\n    } else {\n      // This shouldn't happen because we're traversing the same tree that was\n      // used to construct the cache nodes in the first place.\n    }\n  }\n  const rsc = cacheNode.rsc\n  if (isDeferredRsc(rsc)) {\n    if (error === null) {\n      // This will trigger a lazy fetch during render.\n      rsc.resolve(null)\n    } else {\n      // This will trigger an error during rendering.\n      rsc.reject(error)\n    }\n  }\n\n  // Check if this is a leaf segment. If so, it will have a `head` property with\n  // a pending promise that needs to be resolved. If an error was provided, we\n  // will not resolve it with an error, since this is rendered at the root of\n  // the app. We want the segment to error, not the entire app.\n  const head = cacheNode.head\n  if (isDeferredRsc(head)) {\n    head.resolve(null)\n  }\n}\n\nexport function updateCacheNodeOnPopstateRestoration(\n  oldCacheNode: CacheNode,\n  routerState: FlightRouterState\n): ReadyCacheNode {\n  // A popstate navigation reads data from the local cache. It does not issue\n  // new network requests (unless the cache entries have been evicted). So, we\n  // update the cache to drop the prefetch data for any segment whose dynamic\n  // data was already received. This prevents an unnecessary flash back to PPR\n  // state during a back/forward navigation.\n  //\n  // This function clones the entire cache node tree and sets the `prefetchRsc`\n  // field to `null` to prevent it from being rendered. We can't mutate the node\n  // in place because this is a concurrent data structure.\n\n  const routerStateChildren = routerState[1]\n  const oldParallelRoutes = oldCacheNode.parallelRoutes\n  const newParallelRoutes = new Map(oldParallelRoutes)\n  for (let parallelRouteKey in routerStateChildren) {\n    const routerStateChild: FlightRouterState =\n      routerStateChildren[parallelRouteKey]\n    const segmentChild = routerStateChild[0]\n    const segmentKeyChild = createRouterCacheKey(segmentChild)\n    const oldSegmentMapChild = oldParallelRoutes.get(parallelRouteKey)\n    if (oldSegmentMapChild !== undefined) {\n      const oldCacheNodeChild = oldSegmentMapChild.get(segmentKeyChild)\n      if (oldCacheNodeChild !== undefined) {\n        const newCacheNodeChild = updateCacheNodeOnPopstateRestoration(\n          oldCacheNodeChild,\n          routerStateChild\n        )\n        const newSegmentMapChild = new Map(oldSegmentMapChild)\n        newSegmentMapChild.set(segmentKeyChild, newCacheNodeChild)\n        newParallelRoutes.set(parallelRouteKey, newSegmentMapChild)\n      }\n    }\n  }\n\n  // Only show prefetched data if the dynamic data is still pending.\n  //\n  // Tehnically, what we're actually checking is whether the dynamic network\n  // response was received. But since it's a streaming response, this does not\n  // mean that all the dynamic data has fully streamed in. It just means that\n  // _some_ of the dynamic data was received. But as a heuristic, we assume that\n  // the rest dynamic data will stream in quickly, so it's still better to skip\n  // the prefetch state.\n  const rsc = oldCacheNode.rsc\n  const shouldUsePrefetch = isDeferredRsc(rsc) && rsc.status === 'pending'\n\n  return {\n    lazyData: null,\n    rsc,\n    head: oldCacheNode.head,\n\n    prefetchHead: shouldUsePrefetch ? oldCacheNode.prefetchHead : [null, null],\n    prefetchRsc: shouldUsePrefetch ? oldCacheNode.prefetchRsc : null,\n    loading: oldCacheNode.loading,\n\n    // These are the cloned children we computed above\n    parallelRoutes: newParallelRoutes,\n\n    navigatedAt: oldCacheNode.navigatedAt,\n  }\n}\n\nconst DEFERRED = Symbol()\n\ntype PendingDeferredRsc = Promise<React.ReactNode> & {\n  status: 'pending'\n  resolve: (value: React.ReactNode) => void\n  reject: (error: any) => void\n  tag: Symbol\n}\n\ntype FulfilledDeferredRsc = Promise<React.ReactNode> & {\n  status: 'fulfilled'\n  value: React.ReactNode\n  resolve: (value: React.ReactNode) => void\n  reject: (error: any) => void\n  tag: Symbol\n}\n\ntype RejectedDeferredRsc = Promise<React.ReactNode> & {\n  status: 'rejected'\n  reason: any\n  resolve: (value: React.ReactNode) => void\n  reject: (error: any) => void\n  tag: Symbol\n}\n\ntype DeferredRsc =\n  | PendingDeferredRsc\n  | FulfilledDeferredRsc\n  | RejectedDeferredRsc\n\n// This type exists to distinguish a DeferredRsc from a Flight promise. It's a\n// compromise to avoid adding an extra field on every Cache Node, which would be\n// awkward because the pre-PPR parts of codebase would need to account for it,\n// too. We can remove it once type Cache Node type is more settled.\nfunction isDeferredRsc(value: any): value is DeferredRsc {\n  return value && value.tag === DEFERRED\n}\n\nfunction createDeferredRsc(): PendingDeferredRsc {\n  let resolve: any\n  let reject: any\n  const pendingRsc = new Promise<React.ReactNode>((res, rej) => {\n    resolve = res\n    reject = rej\n  }) as PendingDeferredRsc\n  pendingRsc.status = 'pending'\n  pendingRsc.resolve = (value: React.ReactNode) => {\n    if (pendingRsc.status === 'pending') {\n      const fulfilledRsc: FulfilledDeferredRsc = pendingRsc as any\n      fulfilledRsc.status = 'fulfilled'\n      fulfilledRsc.value = value\n      resolve(value)\n    }\n  }\n  pendingRsc.reject = (error: any) => {\n    if (pendingRsc.status === 'pending') {\n      const rejectedRsc: RejectedDeferredRsc = pendingRsc as any\n      rejectedRsc.status = 'rejected'\n      rejectedRsc.reason = error\n      reject(error)\n    }\n  }\n  pendingRsc.tag = DEFERRED\n  return pendingRsc\n}\n"], "names": ["abortTask", "listenForDynamicRequest", "startPPRNavigation", "updateCacheNodeOnPopstateRestoration", "MPA_NAVIGATION_TASK", "route", "node", "dynamicRequestTree", "children", "navigatedAt", "oldCacheNode", "oldRouterState", "newRouterState", "prefetchData", "prefetchHead", "isPrefetchHeadPartial", "isSamePageNavigation", "scrollableSegmentsResult", "segmentPath", "updateCacheNodeOnNavigation", "didFindRootLayout", "oldRouterStateChildren", "newRouterStateChildren", "prefetchDataChildren", "isRootLayout", "oldParallelRoutes", "parallelRoutes", "prefetchParallelRoutes", "Map", "patchedRouterStateChildren", "task<PERSON><PERSON><PERSON><PERSON>", "needsDynamicRequest", "dynamicRequestTreeChildren", "parallelRouteKey", "newRouterStateChild", "oldRouterStateChild", "oldSegmentMapChild", "get", "prefetchDataChild", "newSegmentChild", "newSegment<PERSON>ath<PERSON><PERSON><PERSON>", "concat", "newSegmentKeyChild", "createRouterCache<PERSON>ey", "oldSegment<PERSON>hild", "undefined", "oldCacheNodeChild", "task<PERSON><PERSON><PERSON>", "DEFAULT_SEGMENT_KEY", "spawnReusedTask", "beginRenderingNewRouteTree", "Object", "keys", "length", "matchSegment", "set", "newCacheNodeChild", "newSegmentMapChild", "taskChildRoute", "dynamicRequestTreeChild", "newCacheNode", "lazyData", "rsc", "prefetchRsc", "head", "loading", "patchRouterStateWithNewChildren", "existingCacheNode", "possiblyPartialPrefetchHead", "isNavigatingToNewRootLayout", "createCacheNodeOnNavigation", "routerState", "routerStateChildren", "isLeafSegment", "cacheNodeNavigatedAt", "DYNAMIC_STALETIME_MS", "isPrefetchRscPartial", "spawnPendingTask", "existingCacheNodeChildren", "cacheNodeChildren", "push", "routerStateChild", "existingSegmentMapChild", "segmentChild", "segmentPathChild", "segmentKeyChild", "existingCacheNodeChild", "baseRouterState", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "clone", "newTask", "createPendingCacheNode", "reusedRouterState", "task", "responsePromise", "then", "flightData", "normalizedFlightData", "tree", "serverRouterState", "seedData", "dynamicData", "dynamicHead", "writeDynamicDataIntoPendingTask", "error", "rootTask", "i", "segment", "taskSegment", "finishTaskUsingDynamicDataPayload", "taskNode", "finishPendingCacheNode", "serverChildren", "dynamicDataChildren", "serverRouterStateChild", "dynamicDataChild", "size", "maybePrefetchRsc", "maybePrefetchLoading", "createDeferredRsc", "cacheNode", "taskState", "serverState", "taskStateChildren", "serverStateChildren", "dataChildren", "taskStateChild", "serverStateChild", "dataChild", "segmentMapChild", "taskSegmentChild", "taskSegmentKeyChild", "cacheNodeChild", "abortPendingCacheNode", "dynamicSegmentData", "isDeferredRsc", "resolve", "values", "reject", "newParallelRoutes", "shouldUsePrefetch", "status", "DEFERRED", "Symbol", "value", "tag", "pendingRsc", "Promise", "res", "rej", "fulfilledRsc", "rejectedRsc", "reason"], "mappings": ";;;;;;;;;;;;;;;;;IA0kCgBA,SAAS;eAATA;;IA1VAC,uBAAuB;eAAvBA;;IAvpBAC,kBAAkB;eAAlBA;;IA6jCAC,oCAAoC;eAApCA;;;yBAzoCoB;+BACP;sCACQ;6CAEO;oCACP;AAiCrC,MAAMC,sBAAyC;IAC7CC,OAAO;IACPC,MAAM;IACNC,oBAAoB;IACpBC,UAAU;AACZ;AAiCO,SAASN,mBACdO,WAAmB,EACnBC,YAAuB,EACvBC,cAAiC,EACjCC,cAAiC,EACjCC,YAAsC,EACtCC,YAA6B,EAC7BC,qBAA8B,EAC9BC,oBAA6B,EAC7BC,wBAAkD;IAElD,MAAMC,cAAwC,EAAE;IAChD,OAAOC,4BACLV,aACAC,cACAC,gBACAC,gBACA,OACAC,cACAC,cACAC,uBACAC,sBACAE,aACAD;AAEJ;AAEA,SAASE,4BACPV,WAAmB,EACnBC,YAAuB,EACvBC,cAAiC,EACjCC,cAAiC,EACjCQ,iBAA0B,EAC1BP,YAAsC,EACtCC,YAA6B,EAC7BC,qBAA8B,EAC9BC,oBAA6B,EAC7BE,WAA8B,EAC9BD,wBAAkD;IAElD,0DAA0D;IAC1D,MAAMI,yBAAyBV,cAAc,CAAC,EAAE;IAChD,MAAMW,yBAAyBV,cAAc,CAAC,EAAE;IAChD,MAAMW,uBAAuBV,iBAAiB,OAAOA,YAAY,CAAC,EAAE,GAAG;IAEvE,IAAI,CAACO,mBAAmB;QACtB,wEAAwE;QACxE,0EAA0E;QAC1E,yEAAyE;QACzE,MAAMI,eAAeZ,cAAc,CAAC,EAAE,KAAK;QAC3C,IAAIY,cAAc;YAChB,gCAAgC;YAChCJ,oBAAoB;QACtB;IACF;IAEA,MAAMK,oBAAoBf,aAAagB,cAAc;IAErD,2EAA2E;IAC3E,gBAAgB;IAChB,0EAA0E;IAC1E,0EAA0E;IAC1E,4EAA4E;IAC5E,2EAA2E;IAC3E,0EAA0E;IAC1E,uEAAuE;IACvE,yEAAyE;IACzE,wEAAwE;IACxE,+BAA+B;IAC/B,MAAMC,yBAAyB,IAAIC,IAAIH;IAEvC,4EAA4E;IAC5E,4EAA4E;IAC5E,2EAA2E;IAC3E,6EAA6E;IAC7E,mBAAmB;IACnB,IAAII,6BAEA,CAAC;IACL,IAAIC,eAAe;IAEnB,uEAAuE;IACvE,6EAA6E;IAC7E,gEAAgE;IAChE,EAAE;IACF,4EAA4E;IAC5E,sEAAsE;IACtE,EAAE;IACF,uEAAuE;IACvE,qCAAqC;IACrC,IAAIC,sBAAsB;IAC1B,4EAA4E;IAC5E,0EAA0E;IAC1E,8EAA8E;IAC9E,qCAAqC;IACrC,2EAA2E;IAC3E,yDAAyD;IACzD,0BAA0B;IAC1B,IAAIC,6BAEA,CAAC;IAEL,IAAK,IAAIC,oBAAoBX,uBAAwB;QACnD,MAAMY,sBACJZ,sBAAsB,CAACW,iBAAiB;QAC1C,MAAME,sBACJd,sBAAsB,CAACY,iBAAiB;QAC1C,MAAMG,qBAAqBX,kBAAkBY,GAAG,CAACJ;QACjD,MAAMK,oBACJf,yBAAyB,OACrBA,oBAAoB,CAACU,iBAAiB,GACtC;QAEN,MAAMM,kBAAkBL,mBAAmB,CAAC,EAAE;QAC9C,MAAMM,sBAAsBtB,YAAYuB,MAAM,CAAC;YAC7CR;YACAM;SACD;QACD,MAAMG,qBAAqBC,IAAAA,0CAAoB,EAACJ;QAEhD,MAAMK,kBACJT,wBAAwBU,YAAYV,mBAAmB,CAAC,EAAE,GAAGU;QAE/D,MAAMC,oBACJV,uBAAuBS,YACnBT,mBAAmBC,GAAG,CAACK,sBACvBG;QAEN,IAAIE;QACJ,IAAIR,oBAAoBS,4BAAmB,EAAE;YAC3C,0DAA0D;YAC1D,EAAE;YACF,yEAAyE;YACzE,uEAAuE;YACvE,sEAAsE;YACtE,oEAAoE;YACpE,WAAW;YACX,IAAIb,wBAAwBU,WAAW;gBACrC,sEAAsE;gBACtE,oEAAoE;gBACpE,mEAAmE;gBACnEE,YAAYE,gBAAgBd;YAC9B,OAAO;gBACL,oEAAoE;gBACpEY,YAAYG,2BACVzC,aACA0B,qBACAD,qBACAY,mBACA1B,mBACAkB,sBAAsBO,YAAYP,oBAAoB,MACtDxB,cACAC,uBACAyB,qBACAvB;YAEJ;QACF,OAAO,IACLD,wBACA,mCAAmC;QACnC,qEAAqE;QACrE,sEAAsE;QACtE,sEAAsE;QACtE,wEAAwE;QACxE,yDAAyD;QACzDmC,OAAOC,IAAI,CAAClB,mBAAmB,CAAC,EAAE,EAAEmB,MAAM,KAAK,GAC/C;YACA,mEAAmE;YACnE,0EAA0E;YAC1E,yEAAyE;YACzE,6BAA6B;YAC7B,EAAE;YACF,sEAAsE;YACtE,yEAAyE;YACzE,sEAAsE;YACtE,gCAAgC;YAChC,EAAE;YACF,qEAAqE;YACrE,0EAA0E;YAC1E,wEAAwE;YACxE,2CAA2C;YAC3C,EAAE;YACF,mEAAmE;YACnE,uEAAuE;YACvE,0DAA0D;YAC1DN,YAAYG,2BACVzC,aACA0B,qBACAD,qBACAY,mBACA1B,mBACAkB,sBAAsBO,YAAYP,oBAAoB,MACtDxB,cACAC,uBACAyB,qBACAvB;QAEJ,OAAO,IACLkB,wBAAwBU,aACxBD,oBAAoBC,aACpBS,IAAAA,2BAAY,EAACf,iBAAiBK,kBAC9B;YACA,IACEE,sBAAsBD,aACtBV,wBAAwBU,WACxB;gBACA,wEAAwE;gBACxE,gBAAgB;gBAChBE,YAAY5B,4BACVV,aACAqC,mBACAX,qBACAD,qBACAd,mBACAkB,mBACAxB,cACAC,uBACAC,sBACAwB,qBACAvB;YAEJ,OAAO;gBACL,iEAAiE;gBACjE,iBAAiB;gBACjB8B,YAAYG,2BACVzC,aACA0B,qBACAD,qBACAY,mBACA1B,mBACAkB,sBAAsBO,YAAYP,oBAAoB,MACtDxB,cACAC,uBACAyB,qBACAvB;YAEJ;QACF,OAAO;YACL,mDAAmD;YACnD8B,YAAYG,2BACVzC,aACA0B,qBACAD,qBACAY,mBACA1B,mBACAkB,sBAAsBO,YAAYP,oBAAoB,MACtDxB,cACAC,uBACAyB,qBACAvB;QAEJ;QAEA,IAAI8B,cAAc,MAAM;YACtB,4CAA4C;YAE5C,IAAIA,UAAU1C,KAAK,KAAK,MAAM;gBAC5B,iEAAiE;gBACjE,oDAAoD;gBACpD,OAAOD;YACT;YAEA,IAAI0B,iBAAiB,MAAM;gBACzBA,eAAe,IAAIF;YACrB;YACAE,aAAayB,GAAG,CAACtB,kBAAkBc;YACnC,MAAMS,oBAAoBT,UAAUzC,IAAI;YACxC,IAAIkD,sBAAsB,MAAM;gBAC9B,MAAMC,qBAAsC,IAAI7B,IAAIQ;gBACpDqB,mBAAmBF,GAAG,CAACb,oBAAoBc;gBAC3C7B,uBAAuB4B,GAAG,CAACtB,kBAAkBwB;YAC/C;YAEA,oEAAoE;YACpE,uEAAuE;YACvE,YAAY;YACZ,MAAMC,iBAAiBX,UAAU1C,KAAK;YACtCwB,0BAA0B,CAACI,iBAAiB,GAAGyB;YAE/C,MAAMC,0BAA0BZ,UAAUxC,kBAAkB;YAC5D,IAAIoD,4BAA4B,MAAM;gBACpC,0CAA0C;gBAC1C5B,sBAAsB;gBACtBC,0BAA0B,CAACC,iBAAiB,GAAG0B;YACjD,OAAO;gBACL3B,0BAA0B,CAACC,iBAAiB,GAAGyB;YACjD;QACF,OAAO;YACL,mEAAmE;YACnE7B,0BAA0B,CAACI,iBAAiB,GAAGC;YAC/CF,0BAA0B,CAACC,iBAAiB,GAAGC;QACjD;IACF;IAEA,IAAIJ,iBAAiB,MAAM;QACzB,6BAA6B;QAC7B,OAAO;IACT;IAEA,MAAM8B,eAA+B;QACnCC,UAAU;QACVC,KAAKpD,aAAaoD,GAAG;QACrB,0EAA0E;QAC1E,qEAAqE;QACrE,2EAA2E;QAC3E,0EAA0E;QAC1E,2EAA2E;QAC3E,qCAAqC;QACrCC,aAAarD,aAAaqD,WAAW;QACrCC,MAAMtD,aAAasD,IAAI;QACvBlD,cAAcJ,aAAaI,YAAY;QACvCmD,SAASvD,aAAauD,OAAO;QAE7B,yEAAyE;QACzEvC,gBAAgBC;QAEhBlB;IACF;IAEA,OAAO;QACL,kEAAkE;QAClEJ,OAAO6D,gCACLtD,gBACAiB;QAEFvB,MAAMsD;QACNrD,oBAAoBwB,sBAChBmC,gCACEtD,gBACAoB,8BAEF;QACJxB,UAAUsB;IACZ;AACF;AAEA,SAASoB,2BACPzC,WAAmB,EACnBE,cAAwC,EACxCC,cAAiC,EACjCuD,iBAAmC,EACnC/C,iBAA0B,EAC1BP,YAAsC,EACtCuD,2BAA4C,EAC5CrD,qBAA8B,EAC9BG,WAA8B,EAC9BD,wBAAkD;IAElD,IAAI,CAACG,mBAAmB;QACtB,wEAAwE;QACxE,0EAA0E;QAC1E,2EAA2E;QAC3E,0DAA0D;QAC1D,EAAE;QACF,uEAAuE;QACvE,uEAAuE;QACvE,2EAA2E;QAC3E,2EAA2E;QAC3E,yCAAyC;QACzC,EAAE;QACF,2EAA2E;QAC3E,oEAAoE;QACpE,EAAE;QACF,oDAAoD;QACpD,EAAE;QACF,sEAAsE;QACtE,4EAA4E;QAC5E,yDAAyD;QACzD,IACET,mBAAmBkC,aACnBwB,IAAAA,wDAA2B,EAAC1D,gBAAgBC,iBAC5C;YACA,2DAA2D;YAC3D,OAAOR;QACT;IACF;IACA,OAAOkE,4BACL7D,aACAG,gBACAuD,mBACAtD,cACAuD,6BACArD,uBACAG,aACAD;AAEJ;AAEA,SAASqD,4BACP7D,WAAmB,EACnB8D,WAA8B,EAC9BJ,iBAAmC,EACnCtD,YAAsC,EACtCuD,2BAA4C,EAC5CrD,qBAA8B,EAC9BG,WAA8B,EAC9BD,wBAAkD;IAElD,0EAA0E;IAC1E,4EAA4E;IAC5E,6EAA6E;IAE7E,4EAA4E;IAC5E,mEAAmE;IACnE,MAAMuD,sBAAsBD,WAAW,CAAC,EAAE;IAC1C,MAAME,gBAAgBtB,OAAOC,IAAI,CAACoB,qBAAqBnB,MAAM,KAAK;IAElE,6EAA6E;IAC7E,2EAA2E;IAC3E,8EAA8E;IAC9E,2EAA2E;IAC3E,2BAA2B;IAC3B,IAAIS;IACJ,IAAIG;IACJ,IAAID;IACJ,IAAIU;IACJ,IACEP,sBAAsBtB,aACtB,oEAAoE;IACpE,oEAAoE;IACpE,wEAAwE;IACxEsB,kBAAkB1D,WAAW,GAAGkE,wCAAoB,GAAGlE,aACvD;QACA,yEAAyE;QACzE,iDAAiD;QACjDqD,MAAMK,kBAAkBL,GAAG;QAC3BG,UAAUE,kBAAkBF,OAAO;QACnCD,OAAOG,kBAAkBH,IAAI;QAE7B,0EAA0E;QAC1EU,uBAAuBP,kBAAkB1D,WAAW;IACtD,OAAO,IAAII,iBAAiB,MAAM;QAChC,0EAA0E;QAC1E,wEAAwE;QACxE,+DAA+D;QAC/DiD,MAAMjD,YAAY,CAAC,EAAE;QACrBoD,UAAUpD,YAAY,CAAC,EAAE;QACzBmD,OAAOS,gBAAgBL,8BAA8B;QACrD,wEAAwE;QACxE,wEAAwE;QACxE,yBAAyB;QACzBM,uBAAuBjE;QACvB,MAAMmE,uBAAuB/D,YAAY,CAAC,EAAE;QAC5C,IACE,uCAAuC;QACvC+D,wBACA,yEAAyE;QACxE7D,yBAAyB0D,eAC1B;YACA,yEAAyE;YACzE,8CAA8C;YAC9C,OAAOI,iBACLpE,aACA8D,aACA1D,cACAuD,6BACArD,uBACAG,aACAD;QAEJ,OAAO;QACL,gEAAgE;QAChE,sBAAsB;QACxB;IACF,OAAO;QACL,2EAA2E;QAC3E,yEAAyE;QACzE,8DAA8D;QAC9D,mBAAmB;QACnB,OAAO4D,iBACLpE,aACA8D,aACA,MACAH,6BACArD,uBACAG,aACAD;IAEJ;IAEA,8EAA8E;IAC9E,wEAAwE;IACxE,6CAA6C;IAC7C,MAAMM,uBAAuBV,iBAAiB,OAAOA,YAAY,CAAC,EAAE,GAAG;IACvE,MAAMiB,eAAe,IAAIF;IACzB,MAAMkD,4BACJX,sBAAsBtB,YAAYsB,kBAAkBzC,cAAc,GAAG;IACvE,MAAMqD,oBAAoB,IAAInD,IAAIkD;IAClC,IAAI9C,6BAEA,CAAC;IACL,IAAID,sBAAsB;IAC1B,IAAI0C,eAAe;QACjB,uEAAuE;QACvE,4EAA4E;QAC5E,4CAA4C;QAC5C,wEAAwE;QACxE,qEAAqE;QACrE,2DAA2D;QAC3DxD,yBAAyB+D,IAAI,CAAC9D;IAChC,OAAO;QACL,IAAK,IAAIe,oBAAoBuC,oBAAqB;YAChD,MAAMS,mBACJT,mBAAmB,CAACvC,iBAAiB;YACvC,MAAMK,oBACJf,yBAAyB,OACrBA,oBAAoB,CAACU,iBAAiB,GACtC;YACN,MAAMiD,0BACJJ,8BAA8B,OAC1BA,0BAA0BzC,GAAG,CAACJ,oBAC9BY;YACN,MAAMsC,eAAeF,gBAAgB,CAAC,EAAE;YACxC,MAAMG,mBAAmBlE,YAAYuB,MAAM,CAAC;gBAC1CR;gBACAkD;aACD;YACD,MAAME,kBAAkB1C,IAAAA,0CAAoB,EAACwC;YAE7C,MAAMG,yBACJJ,4BAA4BrC,YACxBqC,wBAAwB7C,GAAG,CAACgD,mBAC5BxC;YAEN,MAAME,YAAYuB,4BAChB7D,aACAwE,kBACAK,wBACAhD,mBACA8B,6BACArD,uBACAqE,kBACAnE;YAEFa,aAAayB,GAAG,CAACtB,kBAAkBc;YACnC,MAAMY,0BAA0BZ,UAAUxC,kBAAkB;YAC5D,IAAIoD,4BAA4B,MAAM;gBACpC,0CAA0C;gBAC1C5B,sBAAsB;gBACtBC,0BAA0B,CAACC,iBAAiB,GAAG0B;YACjD,OAAO;gBACL3B,0BAA0B,CAACC,iBAAiB,GAAGgD;YACjD;YACA,MAAMzB,oBAAoBT,UAAUzC,IAAI;YACxC,IAAIkD,sBAAsB,MAAM;gBAC9B,MAAMC,qBAAsC,IAAI7B;gBAChD6B,mBAAmBF,GAAG,CAAC8B,iBAAiB7B;gBACxCuB,kBAAkBxB,GAAG,CAACtB,kBAAkBwB;YAC1C;QACF;IACF;IAEA,OAAO;QACL,kDAAkD;QAClD,uEAAuE;QACvE,4EAA4E;QAC5E,sBAAsB;QACtBpD,OAAOkE;QACPjE,MAAM;YACJuD,UAAU;YACV,+DAA+D;YAC/D,uBAAuB;YACvBC;YACAC,aAAa;YACbC;YACAlD,cAAc;YACdmD;YACAvC,gBAAgBqD;YAChBtE,aAAaiE;QACf;QACAnE,oBAAoBwB,sBAChBmC,gCAAgCK,aAAavC,8BAC7C;QACJxB,UAAUsB;IACZ;AACF;AAEA,SAASoC,gCACPqB,eAAkC,EAClCC,WAA8D;IAE9D,MAAMC,QAA2B;QAACF,eAAe,CAAC,EAAE;QAAEC;KAAY;IAClE,4EAA4E;IAC5E,2EAA2E;IAC3E,uCAAuC;IACvC,IAAI,KAAKD,iBAAiB;QACxBE,KAAK,CAAC,EAAE,GAAGF,eAAe,CAAC,EAAE;IAC/B;IACA,IAAI,KAAKA,iBAAiB;QACxBE,KAAK,CAAC,EAAE,GAAGF,eAAe,CAAC,EAAE;IAC/B;IACA,IAAI,KAAKA,iBAAiB;QACxBE,KAAK,CAAC,EAAE,GAAGF,eAAe,CAAC,EAAE;IAC/B;IACA,OAAOE;AACT;AAEA,SAASZ,iBACPpE,WAAmB,EACnB8D,WAA8B,EAC9B1D,YAAsC,EACtCC,YAA6B,EAC7BC,qBAA8B,EAC9BG,WAA8B,EAC9BD,wBAAkD;IAElD,sEAAsE;IAEtE,6EAA6E;IAC7E,2DAA2D;IAC3D,MAAMV,qBAAqB2D,gCACzBK,aACAA,WAAW,CAAC,EAAE;IAEhBhE,kBAAkB,CAAC,EAAE,GAAG;IAExB,MAAMmF,UAAgB;QACpBrF,OAAOkE;QAEP,4EAA4E;QAC5EjE,MAAMqF,uBACJlF,aACA8D,aACA1D,cACAC,cACAC,uBACAG,aACAD;QAEF,yEAAyE;QACzE,4EAA4E;QAC5EV;QACAC,UAAU;IACZ;IACA,OAAOkF;AACT;AAEA,SAASzC,gBAAgB2C,iBAAoC;IAC3D,mEAAmE;IACnE,0DAA0D;IAC1D,OAAO;QACLvF,OAAOuF;QACPtF,MAAM;QACNC,oBAAoB;QACpBC,UAAU;IACZ;AACF;AAiBO,SAASP,wBACd4F,IAAuB,EACvBC,eAAmD;IAEnDA,gBAAgBC,IAAI,CAClB;YAAC,EAAEC,UAAU,EAA6B;QACxC,IAAI,OAAOA,eAAe,UAAU;YAClC,sEAAsE;YACtE,2DAA2D;YAC3D,gBAAgB;YAChB;QACF;QACA,KAAK,MAAMC,wBAAwBD,WAAY;YAC7C,MAAM,EACJ9E,WAAW,EACXgF,MAAMC,iBAAiB,EACvBC,UAAUC,WAAW,EACrBrC,MAAMsC,WAAW,EAClB,GAAGL;YAEJ,IAAI,CAACI,aAAa;gBAIhB;YACF;YAEAE,gCACEV,MACA3E,aACAiF,mBACAE,aACAC;QAEJ;QAEA,wEAAwE;QACxE,qEAAqE;QACrE,6DAA6D;QAC7DtG,UAAU6F,MAAM;IAClB,GACA,CAACW;QACC,2CAA2C;QAC3CxG,UAAU6F,MAAMW;IAClB;AAEJ;AAEA,SAASD,gCACPE,QAA2B,EAC3BvF,WAA8B,EAC9BiF,iBAAoC,EACpCE,WAA8B,EAC9BC,WAAqB;IAErB,4EAA4E;IAC5E,0EAA0E;IAC1E,qCAAqC;IACrC,EAAE;IACF,8EAA8E;IAC9E,qCAAqC;IACrC,EAAE;IACF,6DAA6D;IAC7D,EAAE;IACF,yEAAyE;IACzE,IAAIT,OAAOY;IACX,IAAK,IAAIC,IAAI,GAAGA,IAAIxF,YAAYmC,MAAM,EAAEqD,KAAK,EAAG;QAC9C,MAAMzE,mBAA2Bf,WAAW,CAACwF,EAAE;QAC/C,MAAMC,UAAmBzF,WAAW,CAACwF,IAAI,EAAE;QAC3C,MAAM5E,eAAe+D,KAAKrF,QAAQ;QAClC,IAAIsB,iBAAiB,MAAM;YACzB,MAAMiB,YAAYjB,aAAaO,GAAG,CAACJ;YACnC,IAAIc,cAAcF,WAAW;gBAC3B,MAAM+D,cAAc7D,UAAU1C,KAAK,CAAC,EAAE;gBACtC,IAAIiD,IAAAA,2BAAY,EAACqD,SAASC,cAAc;oBACtC,mEAAmE;oBACnEf,OAAO9C;oBACP;gBACF;YACF;QACF;QACA,2EAA2E;QAC3E,4EAA4E;QAC5E,wEAAwE;QACxE,8BAA8B;QAC9B;IACF;IAEA8D,kCACEhB,MACAM,mBACAE,aACAC;AAEJ;AAEA,SAASO,kCACPhB,IAAuB,EACvBM,iBAAoC,EACpCE,WAA8B,EAC9BC,WAAqB;IAErB,IAAIT,KAAKtF,kBAAkB,KAAK,MAAM;QACpC,4DAA4D;QAC5D;IACF;IAEA,0EAA0E;IAC1E,4CAA4C;IAC5C,MAAMuB,eAAe+D,KAAKrF,QAAQ;IAClC,MAAMsG,WAAWjB,KAAKvF,IAAI;IAC1B,IAAIwB,iBAAiB,MAAM;QACzB,wEAAwE;QACxE,iEAAiE;QACjE,oBAAoB;QACpB,IAAIgF,aAAa,MAAM;YACrBC,uBACED,UACAjB,KAAKxF,KAAK,EACV8F,mBACAE,aACAC;YAEF,+DAA+D;YAC/DT,KAAKtF,kBAAkB,GAAG;QAC5B;QACA;IACF;IACA,2EAA2E;IAC3E,wDAAwD;IACxD,MAAMyG,iBAAiBb,iBAAiB,CAAC,EAAE;IAC3C,MAAMc,sBAAsBZ,WAAW,CAAC,EAAE;IAE1C,IAAK,MAAMpE,oBAAoBkE,kBAAmB;QAChD,MAAMe,yBACJF,cAAc,CAAC/E,iBAAiB;QAClC,MAAMkF,mBACJF,mBAAmB,CAAChF,iBAAiB;QAEvC,MAAMc,YAAYjB,aAAaO,GAAG,CAACJ;QACnC,IAAIc,cAAcF,WAAW;YAC3B,MAAM+D,cAAc7D,UAAU1C,KAAK,CAAC,EAAE;YACtC,IACEiD,IAAAA,2BAAY,EAAC4D,sBAAsB,CAAC,EAAE,EAAEN,gBACxCO,qBAAqB,QACrBA,qBAAqBtE,WACrB;gBACA,mEAAmE;gBACnE,OAAOgE,kCACL9D,WACAmE,wBACAC,kBACAb;YAEJ;QACF;IACA,2EAA2E;IAC3E,sEAAsE;IACtE,wEAAwE;IACxE,8BAA8B;IAChC;AACF;AAEA,SAASX,uBACPlF,WAAmB,EACnB8D,WAA8B,EAC9B1D,YAAsC,EACtCC,YAA6B,EAC7BC,qBAA8B,EAC9BG,WAA8B,EAC9BD,wBAAkD;IAElD,MAAMuD,sBAAsBD,WAAW,CAAC,EAAE;IAC1C,MAAMhD,uBAAuBV,iBAAiB,OAAOA,YAAY,CAAC,EAAE,GAAG;IAEvE,MAAMa,iBAAiB,IAAIE;IAC3B,IAAK,IAAIK,oBAAoBuC,oBAAqB;QAChD,MAAMS,mBACJT,mBAAmB,CAACvC,iBAAiB;QACvC,MAAMK,oBACJf,yBAAyB,OACrBA,oBAAoB,CAACU,iBAAiB,GACtC;QAEN,MAAMkD,eAAeF,gBAAgB,CAAC,EAAE;QACxC,MAAMG,mBAAmBlE,YAAYuB,MAAM,CAAC;YAC1CR;YACAkD;SACD;QACD,MAAME,kBAAkB1C,IAAAA,0CAAoB,EAACwC;QAE7C,MAAM3B,oBAAoBmC,uBACxBlF,aACAwE,kBACA3C,sBAAsBO,YAAY,OAAOP,mBACzCxB,cACAC,uBACAqE,kBACAnE;QAGF,MAAMwC,qBAAsC,IAAI7B;QAChD6B,mBAAmBF,GAAG,CAAC8B,iBAAiB7B;QACxC9B,eAAe6B,GAAG,CAACtB,kBAAkBwB;IACvC;IAEA,4EAA4E;IAC5E,mEAAmE;IACnE,MAAMgB,gBAAgB/C,eAAe0F,IAAI,KAAK;IAE9C,IAAI3C,eAAe;QACjB,uEAAuE;QACvE,4EAA4E;QAC5E,4CAA4C;QAC5C,wEAAwE;QACxE,qEAAqE;QACrE,2DAA2D;QAC3DxD,yBAAyB+D,IAAI,CAAC9D;IAChC;IAEA,MAAMmG,mBAAmBxG,iBAAiB,OAAOA,YAAY,CAAC,EAAE,GAAG;IACnE,MAAMyG,uBAAuBzG,iBAAiB,OAAOA,YAAY,CAAC,EAAE,GAAG;IACvE,OAAO;QACLgD,UAAU;QACVnC,gBAAgBA;QAEhBqC,aAAasD,qBAAqBxE,YAAYwE,mBAAmB;QACjEvG,cAAc2D,gBAAgB3D,eAAe;YAAC;YAAM;SAAK;QAEzD,4EAA4E;QAC5E,4EAA4E;QAC5E,uCAAuC;QACvCmD,SAASqD,yBAAyBzE,YAAYyE,uBAAuB;QAErE,qEAAqE;QACrE,wCAAwC;QACxCxD,KAAKyD;QACLvD,MAAMS,gBAAiB8C,sBAA0C;QAEjE9G;IACF;AACF;AAEA,SAASsG,uBACPS,SAAoB,EACpBC,SAA4B,EAC5BC,WAA8B,EAC9BrB,WAA8B,EAC9BC,WAAqB;IAErB,8EAA8E;IAC9E,8EAA8E;IAC9E,4EAA4E;IAC5E,8EAA8E;IAC9E,8DAA8D;IAC9D,6BAA6B;IAC7B,EAAE;IACF,qEAAqE;IACrE,8EAA8E;IAC9E,gEAAgE;IAChE,MAAMqB,oBAAoBF,SAAS,CAAC,EAAE;IACtC,MAAMG,sBAAsBF,WAAW,CAAC,EAAE;IAC1C,MAAMG,eAAexB,WAAW,CAAC,EAAE;IAEnC,8EAA8E;IAC9E,6EAA6E;IAC7E,uCAAuC;IACvC,MAAM3E,iBAAiB8F,UAAU9F,cAAc;IAC/C,IAAK,IAAIO,oBAAoB0F,kBAAmB;QAC9C,MAAMG,iBACJH,iBAAiB,CAAC1F,iBAAiB;QACrC,MAAM8F,mBACJH,mBAAmB,CAAC3F,iBAAiB;QACvC,MAAM+F,YACJH,YAAY,CAAC5F,iBAAiB;QAEhC,MAAMgG,kBAAkBvG,eAAeW,GAAG,CAACJ;QAC3C,MAAMiG,mBAAmBJ,cAAc,CAAC,EAAE;QAC1C,MAAMK,sBAAsBxF,IAAAA,0CAAoB,EAACuF;QAEjD,MAAME,iBACJH,oBAAoBpF,YAChBoF,gBAAgB5F,GAAG,CAAC8F,uBACpBtF;QAEN,IAAIuF,mBAAmBvF,WAAW;YAChC,IACEkF,qBAAqBlF,aACrBS,IAAAA,2BAAY,EAAC4E,kBAAkBH,gBAAgB,CAAC,EAAE,GAClD;gBACA,IAAIC,cAAcnF,aAAamF,cAAc,MAAM;oBACjD,+DAA+D;oBAC/DjB,uBACEqB,gBACAN,gBACAC,kBACAC,WACA1B;gBAEJ,OAAO;oBACL,kEAAkE;oBAClE,oEAAoE;oBACpE,sEAAsE;oBACtE,+CAA+C;oBAC/C+B,sBAAsBP,gBAAgBM,gBAAgB;gBACxD;YACF,OAAO;gBACL,kEAAkE;gBAClE,uBAAuB;gBACvBC,sBAAsBP,gBAAgBM,gBAAgB;YACxD;QACF,OAAO;QACL,wEAAwE;QACxE,gEAAgE;QAChE,iEAAiE;QACjE,wDAAwD;QAC1D;IACF;IAEA,2EAA2E;IAC3E,qBAAqB;IACrB,MAAMtE,MAAM0D,UAAU1D,GAAG;IACzB,MAAMwE,qBAAqBjC,WAAW,CAAC,EAAE;IACzC,IAAIvC,QAAQ,MAAM;QAChB,oEAAoE;QACpE,qEAAqE;QACrE0D,UAAU1D,GAAG,GAAGwE;IAClB,OAAO,IAAIC,cAAczE,MAAM;QAC7B,0EAA0E;QAC1E,sEAAsE;QACtE,sEAAsE;QACtEA,IAAI0E,OAAO,CAACF;IACd,OAAO;IACL,uEAAuE;IACvE,sEAAsE;IACxE;IAEA,8EAA8E;IAC9E,yEAAyE;IACzE,cAAc;IACd,MAAMtE,OAAOwD,UAAUxD,IAAI;IAC3B,IAAIuE,cAAcvE,OAAO;QACvBA,KAAKwE,OAAO,CAAClC;IACf;AACF;AAEO,SAAStG,UAAU6F,IAAuB,EAAEW,KAAU;IAC3D,MAAMgB,YAAY3B,KAAKvF,IAAI;IAC3B,IAAIkH,cAAc,MAAM;QACtB,+CAA+C;QAC/C;IACF;IAEA,MAAM1F,eAAe+D,KAAKrF,QAAQ;IAClC,IAAIsB,iBAAiB,MAAM;QACzB,kEAAkE;QAClE,aAAa;QACbuG,sBAAsBxC,KAAKxF,KAAK,EAAEmH,WAAWhB;IAC/C,OAAO;QACL,sEAAsE;QACtE,2EAA2E;QAC3E,6BAA6B;QAC7B,KAAK,MAAMzD,aAAajB,aAAa2G,MAAM,GAAI;YAC7CzI,UAAU+C,WAAWyD;QACvB;IACF;IAEA,+DAA+D;IAC/DX,KAAKtF,kBAAkB,GAAG;AAC5B;AAEA,SAAS8H,sBACP9D,WAA8B,EAC9BiD,SAAoB,EACpBhB,KAAU;IAEV,6EAA6E;IAC7E,yCAAyC;IACzC,EAAE;IACF,6DAA6D;IAC7D,MAAMhC,sBAAsBD,WAAW,CAAC,EAAE;IAC1C,MAAM7C,iBAAiB8F,UAAU9F,cAAc;IAC/C,IAAK,IAAIO,oBAAoBuC,oBAAqB;QAChD,MAAMS,mBACJT,mBAAmB,CAACvC,iBAAiB;QACvC,MAAMgG,kBAAkBvG,eAAeW,GAAG,CAACJ;QAC3C,IAAIgG,oBAAoBpF,WAAW;YAGjC;QACF;QACA,MAAMsC,eAAeF,gBAAgB,CAAC,EAAE;QACxC,MAAMI,kBAAkB1C,IAAAA,0CAAoB,EAACwC;QAC7C,MAAMiD,iBAAiBH,gBAAgB5F,GAAG,CAACgD;QAC3C,IAAI+C,mBAAmBvF,WAAW;YAChCwF,sBAAsBpD,kBAAkBmD,gBAAgB5B;QAC1D,OAAO;QACL,wEAAwE;QACxE,wDAAwD;QAC1D;IACF;IACA,MAAM1C,MAAM0D,UAAU1D,GAAG;IACzB,IAAIyE,cAAczE,MAAM;QACtB,IAAI0C,UAAU,MAAM;YAClB,gDAAgD;YAChD1C,IAAI0E,OAAO,CAAC;QACd,OAAO;YACL,+CAA+C;YAC/C1E,IAAI4E,MAAM,CAAClC;QACb;IACF;IAEA,8EAA8E;IAC9E,4EAA4E;IAC5E,2EAA2E;IAC3E,6DAA6D;IAC7D,MAAMxC,OAAOwD,UAAUxD,IAAI;IAC3B,IAAIuE,cAAcvE,OAAO;QACvBA,KAAKwE,OAAO,CAAC;IACf;AACF;AAEO,SAASrI,qCACdO,YAAuB,EACvB6D,WAA8B;IAE9B,2EAA2E;IAC3E,4EAA4E;IAC5E,2EAA2E;IAC3E,4EAA4E;IAC5E,0CAA0C;IAC1C,EAAE;IACF,6EAA6E;IAC7E,8EAA8E;IAC9E,wDAAwD;IAExD,MAAMC,sBAAsBD,WAAW,CAAC,EAAE;IAC1C,MAAM9C,oBAAoBf,aAAagB,cAAc;IACrD,MAAMiH,oBAAoB,IAAI/G,IAAIH;IAClC,IAAK,IAAIQ,oBAAoBuC,oBAAqB;QAChD,MAAMS,mBACJT,mBAAmB,CAACvC,iBAAiB;QACvC,MAAMkD,eAAeF,gBAAgB,CAAC,EAAE;QACxC,MAAMI,kBAAkB1C,IAAAA,0CAAoB,EAACwC;QAC7C,MAAM/C,qBAAqBX,kBAAkBY,GAAG,CAACJ;QACjD,IAAIG,uBAAuBS,WAAW;YACpC,MAAMC,oBAAoBV,mBAAmBC,GAAG,CAACgD;YACjD,IAAIvC,sBAAsBD,WAAW;gBACnC,MAAMW,oBAAoBrD,qCACxB2C,mBACAmC;gBAEF,MAAMxB,qBAAqB,IAAI7B,IAAIQ;gBACnCqB,mBAAmBF,GAAG,CAAC8B,iBAAiB7B;gBACxCmF,kBAAkBpF,GAAG,CAACtB,kBAAkBwB;YAC1C;QACF;IACF;IAEA,kEAAkE;IAClE,EAAE;IACF,0EAA0E;IAC1E,4EAA4E;IAC5E,2EAA2E;IAC3E,8EAA8E;IAC9E,6EAA6E;IAC7E,sBAAsB;IACtB,MAAMK,MAAMpD,aAAaoD,GAAG;IAC5B,MAAM8E,oBAAoBL,cAAczE,QAAQA,IAAI+E,MAAM,KAAK;IAE/D,OAAO;QACLhF,UAAU;QACVC;QACAE,MAAMtD,aAAasD,IAAI;QAEvBlD,cAAc8H,oBAAoBlI,aAAaI,YAAY,GAAG;YAAC;YAAM;SAAK;QAC1EiD,aAAa6E,oBAAoBlI,aAAaqD,WAAW,GAAG;QAC5DE,SAASvD,aAAauD,OAAO;QAE7B,kDAAkD;QAClDvC,gBAAgBiH;QAEhBlI,aAAaC,aAAaD,WAAW;IACvC;AACF;AAEA,MAAMqI,WAAWC;AA8BjB,8EAA8E;AAC9E,gFAAgF;AAChF,8EAA8E;AAC9E,mEAAmE;AACnE,SAASR,cAAcS,KAAU;IAC/B,OAAOA,SAASA,MAAMC,GAAG,KAAKH;AAChC;AAEA,SAASvB;IACP,IAAIiB;IACJ,IAAIE;IACJ,MAAMQ,aAAa,IAAIC,QAAyB,CAACC,KAAKC;QACpDb,UAAUY;QACVV,SAASW;IACX;IACAH,WAAWL,MAAM,GAAG;IACpBK,WAAWV,OAAO,GAAG,CAACQ;QACpB,IAAIE,WAAWL,MAAM,KAAK,WAAW;YACnC,MAAMS,eAAqCJ;YAC3CI,aAAaT,MAAM,GAAG;YACtBS,aAAaN,KAAK,GAAGA;YACrBR,QAAQQ;QACV;IACF;IACAE,WAAWR,MAAM,GAAG,CAAClC;QACnB,IAAI0C,WAAWL,MAAM,KAAK,WAAW;YACnC,MAAMU,cAAmCL;YACzCK,YAAYV,MAAM,GAAG;YACrBU,YAAYC,MAAM,GAAGhD;YACrBkC,OAAOlC;QACT;IACF;IACA0C,WAAWD,GAAG,GAAGH;IACjB,OAAOI;AACT", "ignoreList": [0]}