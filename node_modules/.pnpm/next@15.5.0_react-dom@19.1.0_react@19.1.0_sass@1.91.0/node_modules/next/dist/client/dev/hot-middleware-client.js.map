{"version": 3, "sources": ["../../../src/client/dev/hot-middleware-client.ts"], "sourcesContent": ["import type {\n  NextRouter,\n  PrivateRouteInfo,\n} from '../../shared/lib/router/router'\nimport connect from './hot-reloader/pages/hot-reloader-pages'\nimport { sendMessage } from './hot-reloader/pages/websocket'\n\n// Define a local type for the window.next object\ninterface NextWindow {\n  next?: {\n    router?: NextRouter & {\n      components: { [pathname: string]: PrivateRouteInfo }\n    }\n  }\n  __nextDevClientId?: string\n  location: Location\n}\n\ndeclare const window: NextWindow\n\nlet reloading = false\n\nexport default () => {\n  const devClient = connect()\n\n  devClient.subscribeToHmrEvent((obj: any) => {\n    if (reloading) return\n\n    // Retrieve the router if it's available\n    const router = window.next?.router\n\n    // Determine if we're on an error page or the router is not initialized\n    const isOnErrorPage =\n      !router || router.pathname === '/404' || router.pathname === '/_error'\n\n    switch (obj.action) {\n      case 'reloadPage': {\n        sendMessage(\n          JSON.stringify({\n            event: 'client-reload-page',\n            clientId: window.__nextDevClientId,\n          })\n        )\n        reloading = true\n        return window.location.reload()\n      }\n      case 'removedPage': {\n        const [page] = obj.data\n\n        // Check if the removed page is the current page\n        const isCurrentPage = page === router?.pathname\n\n        // We enter here if the removed page is currently being viewed\n        // or if we happen to be on an error page.\n        if (isCurrentPage || isOnErrorPage) {\n          sendMessage(\n            JSON.stringify({\n              event: 'client-removed-page',\n              clientId: window.__nextDevClientId,\n              page,\n            })\n          )\n          return window.location.reload()\n        }\n        return\n      }\n      case 'addedPage': {\n        const [page] = obj.data\n\n        // Check if the added page is the current page\n        const isCurrentPage = page === router?.pathname\n\n        // Check if the page component is not yet loaded\n        const isPageNotLoaded =\n          typeof router?.components?.[page] === 'undefined'\n\n        // We enter this block if the newly added page is the one currently being viewed\n        // but hasn't been loaded yet, or if we're on an error page.\n        if ((isCurrentPage && isPageNotLoaded) || isOnErrorPage) {\n          sendMessage(\n            JSON.stringify({\n              event: 'client-added-page',\n              clientId: window.__nextDevClientId,\n              page,\n            })\n          )\n          return window.location.reload()\n        }\n        return\n      }\n      case 'serverError':\n      case 'devPagesManifestUpdate':\n      case 'isrManifest':\n      case 'building':\n      case 'finishBuilding': {\n        return\n      }\n      default: {\n        throw new Error('Unexpected action ' + obj.action)\n      }\n    }\n  })\n\n  return devClient\n}\n"], "names": ["reloading", "devClient", "connect", "subscribeToHmrEvent", "obj", "window", "router", "next", "isOnErrorPage", "pathname", "action", "sendMessage", "JSON", "stringify", "event", "clientId", "__nextDevClientId", "location", "reload", "page", "data", "isCurrentPage", "isPageNotLoaded", "components", "Error"], "mappings": ";;;;+BAsBA;;;eAAA;;;;2EAlBoB;2BACQ;AAe5B,IAAIA,YAAY;MAEhB,WAAe;IACb,MAAMC,YAAYC,IAAAA,yBAAO;IAEzBD,UAAUE,mBAAmB,CAAC,CAACC;YAIdC;QAHf,IAAIL,WAAW;QAEf,wCAAwC;QACxC,MAAMM,UAASD,eAAAA,OAAOE,IAAI,qBAAXF,aAAaC,MAAM;QAElC,uEAAuE;QACvE,MAAME,gBACJ,CAACF,UAAUA,OAAOG,QAAQ,KAAK,UAAUH,OAAOG,QAAQ,KAAK;QAE/D,OAAQL,IAAIM,MAAM;YAChB,KAAK;gBAAc;oBACjBC,IAAAA,sBAAW,EACTC,KAAKC,SAAS,CAAC;wBACbC,OAAO;wBACPC,UAAUV,OAAOW,iBAAiB;oBACpC;oBAEFhB,YAAY;oBACZ,OAAOK,OAAOY,QAAQ,CAACC,MAAM;gBAC/B;YACA,KAAK;gBAAe;oBAClB,MAAM,CAACC,KAAK,GAAGf,IAAIgB,IAAI;oBAEvB,gDAAgD;oBAChD,MAAMC,gBAAgBF,UAASb,0BAAAA,OAAQG,QAAQ;oBAE/C,8DAA8D;oBAC9D,0CAA0C;oBAC1C,IAAIY,iBAAiBb,eAAe;wBAClCG,IAAAA,sBAAW,EACTC,KAAKC,SAAS,CAAC;4BACbC,OAAO;4BACPC,UAAUV,OAAOW,iBAAiB;4BAClCG;wBACF;wBAEF,OAAOd,OAAOY,QAAQ,CAACC,MAAM;oBAC/B;oBACA;gBACF;YACA,KAAK;gBAAa;wBAQPZ;oBAPT,MAAM,CAACa,KAAK,GAAGf,IAAIgB,IAAI;oBAEvB,8CAA8C;oBAC9C,MAAMC,gBAAgBF,UAASb,0BAAAA,OAAQG,QAAQ;oBAE/C,gDAAgD;oBAChD,MAAMa,kBACJ,QAAOhB,2BAAAA,qBAAAA,OAAQiB,UAAU,qBAAlBjB,kBAAoB,CAACa,KAAK,MAAK;oBAExC,gFAAgF;oBAChF,4DAA4D;oBAC5D,IAAI,AAACE,iBAAiBC,mBAAoBd,eAAe;wBACvDG,IAAAA,sBAAW,EACTC,KAAKC,SAAS,CAAC;4BACbC,OAAO;4BACPC,UAAUV,OAAOW,iBAAiB;4BAClCG;wBACF;wBAEF,OAAOd,OAAOY,QAAQ,CAACC,MAAM;oBAC/B;oBACA;gBACF;YACA,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBAAkB;oBACrB;gBACF;YACA;gBAAS;oBACP,MAAM,qBAA4C,CAA5C,IAAIM,MAAM,uBAAuBpB,IAAIM,MAAM,GAA3C,qBAAA;+BAAA;oCAAA;sCAAA;oBAA2C;gBACnD;QACF;IACF;IAEA,OAAOT;AACT", "ignoreList": [0]}