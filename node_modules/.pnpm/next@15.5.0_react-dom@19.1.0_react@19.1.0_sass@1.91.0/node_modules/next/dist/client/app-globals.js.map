{"version": 3, "sources": ["../../src/client/app-globals.ts"], "sourcesContent": ["// imports polyfill from `@next/polyfill-module` after build.\nimport '../build/polyfills/polyfill-module'\n\n// Only setup devtools in development\nif (process.env.NODE_ENV !== 'production') {\n  require('../next-devtools/userspace/app/app-dev-overlay-setup') as typeof import('../next-devtools/userspace/app/app-dev-overlay-setup')\n}\n"], "names": ["process", "env", "NODE_ENV", "require"], "mappings": "AAAA,6DAA6D;;;;;QACtD;AAEP,qCAAqC;AACrC,IAAIA,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;IACzCC,QAAQ;AACV", "ignoreList": [0]}