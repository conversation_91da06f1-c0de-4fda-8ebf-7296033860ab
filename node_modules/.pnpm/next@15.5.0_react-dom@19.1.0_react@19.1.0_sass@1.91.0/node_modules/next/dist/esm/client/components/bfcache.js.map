{"version": 3, "sources": ["../../../src/client/components/bfcache.ts"], "sourcesContent": ["import type { FlightRouterState } from '../../server/app-render/types'\nimport { useState } from 'react'\n\n// When the flag is disabled, only track the currently active tree\nconst MAX_BF_CACHE_ENTRIES = process.env.__NEXT_ROUTER_BF_CACHE ? 3 : 1\n\nexport type RouterBFCacheEntry = {\n  tree: FlightRouterState\n  stateKey: string\n  // The entries form a linked list, sorted in order of most recently active.\n  next: RouterBFCacheEntry | null\n}\n\n/**\n * Keeps track of the most recent N trees (FlightRouterStates) that were active\n * at a certain segment level. E.g. for a segment \"/a/b/[param]\", this hook\n * tracks the last N param values that the router rendered for N.\n *\n * The result of this hook precisely determines the number and order of\n * trees that are rendered in parallel at their segment level.\n *\n * The purpose of this cache is to we can preserve the React and DOM state of\n * some number of inactive trees, by rendering them in an <Activity> boundary.\n * That means it would not make sense for the the lifetime of the cache to be\n * any longer than the lifetime of the React tree; e.g. if the hook were\n * unmounted, then the React tree would be, too. So, we use React state to\n * manage it.\n *\n * Note that we don't store the RSC data for the cache entries in this hook —\n * the data for inactive segments is stored in the parent CacheNode, which\n * *does* have a longer lifetime than the React tree. This hook only determines\n * which of those trees should have their *state* preserved, by <Activity>.\n */\nexport function useRouterBFCache(\n  activeTree: FlightRouterState,\n  activeStateKey: string\n): RouterBFCacheEntry {\n  // The currently active entry. The entries form a linked list, sorted in\n  // order of most recently active. This allows us to reuse parts of the list\n  // without cloning, unless there's a reordering or removal.\n  // TODO: Once we start tracking back/forward history at each route level,\n  // we should use the history order instead. In other words, when traversing\n  // to an existing entry as a result of a popstate event, we should maintain\n  // the existing order instead of moving it to the front of the list. I think\n  // an initial implementation of this could be to pass an incrementing id\n  // to history.pushState/replaceState, then use that here for ordering.\n  const [prevActiveEntry, setPrevActiveEntry] = useState<RouterBFCacheEntry>(\n    () => {\n      const initialEntry: RouterBFCacheEntry = {\n        tree: activeTree,\n        stateKey: activeStateKey,\n        next: null,\n      }\n      return initialEntry\n    }\n  )\n\n  if (prevActiveEntry.tree === activeTree) {\n    // Fast path. The active tree hasn't changed, so we can reuse the\n    // existing state.\n    return prevActiveEntry\n  }\n\n  // The route tree changed. Note that this doesn't mean that the tree changed\n  // *at this level* — the change may be due to a child route. Either way, we\n  // need to either add or update the router tree in the bfcache.\n  //\n  // The rest of the code looks more complicated than it actually is because we\n  // can't mutate the state in place; we have to copy-on-write.\n\n  // Create a new entry for the active cache key. This is the head of the new\n  // linked list.\n  const newActiveEntry: RouterBFCacheEntry = {\n    tree: activeTree,\n    stateKey: activeStateKey,\n    next: null,\n  }\n\n  // We need to append the old list onto the new list. If the head of the new\n  // list was already present in the cache, then we'll need to clone everything\n  // that came before it. Then we can reuse the rest.\n  let n = 1\n  let oldEntry: RouterBFCacheEntry | null = prevActiveEntry\n  let clonedEntry: RouterBFCacheEntry = newActiveEntry\n  while (oldEntry !== null && n < MAX_BF_CACHE_ENTRIES) {\n    if (oldEntry.stateKey === activeStateKey) {\n      // Fast path. This entry in the old list that corresponds to the key that\n      // is now active. We've already placed a clone of this entry at the front\n      // of the new list. We can reuse the rest of the old list without cloning.\n      // NOTE: We don't need to worry about eviction in this case because we\n      // haven't increased the size of the cache, and we assume the max size\n      // is constant across renders. If we were to change it to a dynamic limit,\n      // then the implementation would need to account for that.\n      clonedEntry.next = oldEntry.next\n      break\n    } else {\n      // Clone the entry and append it to the list.\n      n++\n      const entry: RouterBFCacheEntry = {\n        tree: oldEntry.tree,\n        stateKey: oldEntry.stateKey,\n        next: null,\n      }\n      clonedEntry.next = entry\n      clonedEntry = entry\n    }\n    oldEntry = oldEntry.next\n  }\n\n  setPrevActiveEntry(newActiveEntry)\n  return newActiveEntry\n}\n"], "names": ["useState", "MAX_BF_CACHE_ENTRIES", "process", "env", "__NEXT_ROUTER_BF_CACHE", "useRouterBFCache", "activeTree", "activeStateKey", "prevActiveEntry", "setPrevActiveEntry", "initialEntry", "tree", "stateKey", "next", "newActiveEntry", "n", "oldEntry", "clonedEntry", "entry"], "mappings": "AACA,SAASA,QAAQ,QAAQ,QAAO;AAEhC,kEAAkE;AAClE,MAAMC,uBAAuBC,QAAQC,GAAG,CAACC,sBAAsB,GAAG,IAAI;AAStE;;;;;;;;;;;;;;;;;;;CAmBC,GACD,OAAO,SAASC,iBACdC,UAA6B,EAC7BC,cAAsB;IAEtB,wEAAwE;IACxE,2EAA2E;IAC3E,2DAA2D;IAC3D,yEAAyE;IACzE,2EAA2E;IAC3E,2EAA2E;IAC3E,4EAA4E;IAC5E,wEAAwE;IACxE,sEAAsE;IACtE,MAAM,CAACC,iBAAiBC,mBAAmB,GAAGT,SAC5C;QACE,MAAMU,eAAmC;YACvCC,MAAML;YACNM,UAAUL;YACVM,MAAM;QACR;QACA,OAAOH;IACT;IAGF,IAAIF,gBAAgBG,IAAI,KAAKL,YAAY;QACvC,iEAAiE;QACjE,kBAAkB;QAClB,OAAOE;IACT;IAEA,4EAA4E;IAC5E,2EAA2E;IAC3E,+DAA+D;IAC/D,EAAE;IACF,6EAA6E;IAC7E,6DAA6D;IAE7D,2EAA2E;IAC3E,eAAe;IACf,MAAMM,iBAAqC;QACzCH,MAAML;QACNM,UAAUL;QACVM,MAAM;IACR;IAEA,2EAA2E;IAC3E,6EAA6E;IAC7E,mDAAmD;IACnD,IAAIE,IAAI;IACR,IAAIC,WAAsCR;IAC1C,IAAIS,cAAkCH;IACtC,MAAOE,aAAa,QAAQD,IAAId,qBAAsB;QACpD,IAAIe,SAASJ,QAAQ,KAAKL,gBAAgB;YACxC,yEAAyE;YACzE,yEAAyE;YACzE,0EAA0E;YAC1E,sEAAsE;YACtE,sEAAsE;YACtE,0EAA0E;YAC1E,0DAA0D;YAC1DU,YAAYJ,IAAI,GAAGG,SAASH,IAAI;YAChC;QACF,OAAO;YACL,6CAA6C;YAC7CE;YACA,MAAMG,QAA4B;gBAChCP,MAAMK,SAASL,IAAI;gBACnBC,UAAUI,SAASJ,QAAQ;gBAC3BC,MAAM;YACR;YACAI,YAAYJ,IAAI,GAAGK;YACnBD,cAAcC;QAChB;QACAF,WAAWA,SAASH,IAAI;IAC1B;IAEAJ,mBAAmBK;IACnB,OAAOA;AACT", "ignoreList": [0]}