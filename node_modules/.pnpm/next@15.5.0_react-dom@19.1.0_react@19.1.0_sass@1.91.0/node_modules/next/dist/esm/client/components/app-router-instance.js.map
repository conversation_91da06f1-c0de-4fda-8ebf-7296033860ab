{"version": 3, "sources": ["../../../src/client/components/app-router-instance.ts"], "sourcesContent": ["import {\n  type AppRouterState,\n  type ReducerActions,\n  type ReducerState,\n  ACTION_REFRESH,\n  ACTION_SERVER_ACTION,\n  ACTION_NAVIGATE,\n  ACTION_RESTORE,\n  type NavigateAction,\n  ACTION_HMR_REFRESH,\n  PrefetchKind,\n  ACTION_PREFETCH,\n} from './router-reducer/router-reducer-types'\nimport { reducer } from './router-reducer/router-reducer'\nimport { startTransition } from 'react'\nimport { isThenable } from '../../shared/lib/is-thenable'\nimport {\n  FetchStrategy,\n  prefetch as prefetchWithSegmentCache,\n  type PrefetchTaskFetchStrategy,\n} from './segment-cache'\nimport { dispatchAppRouterAction } from './use-action-queue'\nimport { addBasePath } from '../add-base-path'\nimport { createPrefetchURL, isExternalURL } from './app-router'\nimport { prefetchReducer } from './router-reducer/reducers/prefetch-reducer'\nimport type {\n  AppRouterInstance,\n  NavigateOptions,\n  PrefetchOptions,\n} from '../../shared/lib/app-router-context.shared-runtime'\nimport { setLinkForCurrentNavigation, type LinkInstance } from './links'\nimport type { FlightRouterState } from '../../server/app-render/types'\nimport type { ClientInstrumentationHooks } from '../app-index'\nimport type { GlobalErrorComponent } from './builtin/global-error'\n\nexport type DispatchStatePromise = React.Dispatch<ReducerState>\n\nexport type AppRouterActionQueue = {\n  state: AppRouterState\n  dispatch: (payload: ReducerActions, setState: DispatchStatePromise) => void\n  action: (state: AppRouterState, action: ReducerActions) => ReducerState\n\n  onRouterTransitionStart:\n    | ((url: string, type: 'push' | 'replace' | 'traverse') => void)\n    | null\n\n  pending: ActionQueueNode | null\n  needsRefresh?: boolean\n  last: ActionQueueNode | null\n}\n\nexport type GlobalErrorState = [\n  GlobalError: GlobalErrorComponent,\n  styles: React.ReactNode,\n]\n\nexport type ActionQueueNode = {\n  payload: ReducerActions\n  next: ActionQueueNode | null\n  resolve: (value: ReducerState) => void\n  reject: (err: Error) => void\n  discarded?: boolean\n}\n\nfunction runRemainingActions(\n  actionQueue: AppRouterActionQueue,\n  setState: DispatchStatePromise\n) {\n  if (actionQueue.pending !== null) {\n    actionQueue.pending = actionQueue.pending.next\n    if (actionQueue.pending !== null) {\n      // eslint-disable-next-line @typescript-eslint/no-use-before-define\n      runAction({\n        actionQueue,\n        action: actionQueue.pending,\n        setState,\n      })\n    } else {\n      // No more actions are pending, check if a refresh is needed\n      if (actionQueue.needsRefresh) {\n        actionQueue.needsRefresh = false\n        actionQueue.dispatch(\n          {\n            type: ACTION_REFRESH,\n            origin: window.location.origin,\n          },\n          setState\n        )\n      }\n    }\n  }\n}\n\nasync function runAction({\n  actionQueue,\n  action,\n  setState,\n}: {\n  actionQueue: AppRouterActionQueue\n  action: ActionQueueNode\n  setState: DispatchStatePromise\n}) {\n  const prevState = actionQueue.state\n\n  actionQueue.pending = action\n\n  const payload = action.payload\n  const actionResult = actionQueue.action(prevState, payload)\n\n  function handleResult(nextState: AppRouterState) {\n    // if we discarded this action, the state should also be discarded\n    if (action.discarded) {\n      return\n    }\n\n    actionQueue.state = nextState\n\n    runRemainingActions(actionQueue, setState)\n    action.resolve(nextState)\n  }\n\n  // if the action is a promise, set up a callback to resolve it\n  if (isThenable(actionResult)) {\n    actionResult.then(handleResult, (err) => {\n      runRemainingActions(actionQueue, setState)\n      action.reject(err)\n    })\n  } else {\n    handleResult(actionResult)\n  }\n}\n\nfunction dispatchAction(\n  actionQueue: AppRouterActionQueue,\n  payload: ReducerActions,\n  setState: DispatchStatePromise\n) {\n  let resolvers: {\n    resolve: (value: ReducerState) => void\n    reject: (reason: any) => void\n  } = { resolve: setState, reject: () => {} }\n\n  // most of the action types are async with the exception of restore\n  // it's important that restore is handled quickly since it's fired on the popstate event\n  // and we don't want to add any delay on a back/forward nav\n  // this only creates a promise for the async actions\n  if (payload.type !== ACTION_RESTORE) {\n    // Create the promise and assign the resolvers to the object.\n    const deferredPromise = new Promise<AppRouterState>((resolve, reject) => {\n      resolvers = { resolve, reject }\n    })\n\n    startTransition(() => {\n      // we immediately notify React of the pending promise -- the resolver is attached to the action node\n      // and will be called when the associated action promise resolves\n      setState(deferredPromise)\n    })\n  }\n\n  const newAction: ActionQueueNode = {\n    payload,\n    next: null,\n    resolve: resolvers.resolve,\n    reject: resolvers.reject,\n  }\n\n  // Check if the queue is empty\n  if (actionQueue.pending === null) {\n    // The queue is empty, so add the action and start it immediately\n    // Mark this action as the last in the queue\n    actionQueue.last = newAction\n\n    runAction({\n      actionQueue,\n      action: newAction,\n      setState,\n    })\n  } else if (\n    payload.type === ACTION_NAVIGATE ||\n    payload.type === ACTION_RESTORE\n  ) {\n    // Navigations (including back/forward) take priority over any pending actions.\n    // Mark the pending action as discarded (so the state is never applied) and start the navigation action immediately.\n    actionQueue.pending.discarded = true\n\n    // The rest of the current queue should still execute after this navigation.\n    // (Note that it can't contain any earlier navigations, because we always put those into `actionQueue.pending` by calling `runAction`)\n    newAction.next = actionQueue.pending.next\n\n    // if the pending action was a server action, mark the queue as needing a refresh once events are processed\n    if (actionQueue.pending.payload.type === ACTION_SERVER_ACTION) {\n      actionQueue.needsRefresh = true\n    }\n\n    runAction({\n      actionQueue,\n      action: newAction,\n      setState,\n    })\n  } else {\n    // The queue is not empty, so add the action to the end of the queue\n    // It will be started by runRemainingActions after the previous action finishes\n    if (actionQueue.last !== null) {\n      actionQueue.last.next = newAction\n    }\n    actionQueue.last = newAction\n  }\n}\n\nlet globalActionQueue: AppRouterActionQueue | null = null\n\nexport function createMutableActionQueue(\n  initialState: AppRouterState,\n  instrumentationHooks: ClientInstrumentationHooks | null\n): AppRouterActionQueue {\n  const actionQueue: AppRouterActionQueue = {\n    state: initialState,\n    dispatch: (payload: ReducerActions, setState: DispatchStatePromise) =>\n      dispatchAction(actionQueue, payload, setState),\n    action: async (state: AppRouterState, action: ReducerActions) => {\n      const result = reducer(state, action)\n      return result\n    },\n    pending: null,\n    last: null,\n    onRouterTransitionStart:\n      instrumentationHooks !== null &&\n      typeof instrumentationHooks.onRouterTransitionStart === 'function'\n        ? // This profiling hook will be called at the start of every navigation.\n          instrumentationHooks.onRouterTransitionStart\n        : null,\n  }\n\n  if (typeof window !== 'undefined') {\n    // The action queue is lazily created on hydration, but after that point\n    // it doesn't change. So we can store it in a global rather than pass\n    // it around everywhere via props/context.\n    if (globalActionQueue !== null) {\n      throw new Error(\n        'Internal Next.js Error: createMutableActionQueue was called more ' +\n          'than once'\n      )\n    }\n    globalActionQueue = actionQueue\n  }\n\n  return actionQueue\n}\n\nexport function getCurrentAppRouterState(): AppRouterState | null {\n  return globalActionQueue !== null ? globalActionQueue.state : null\n}\n\nfunction getAppRouterActionQueue(): AppRouterActionQueue {\n  if (globalActionQueue === null) {\n    throw new Error(\n      'Internal Next.js error: Router action dispatched before initialization.'\n    )\n  }\n  return globalActionQueue\n}\n\nfunction getProfilingHookForOnNavigationStart() {\n  if (globalActionQueue !== null) {\n    return globalActionQueue.onRouterTransitionStart\n  }\n  return null\n}\n\nexport function dispatchNavigateAction(\n  href: string,\n  navigateType: NavigateAction['navigateType'],\n  shouldScroll: boolean,\n  linkInstanceRef: LinkInstance | null\n): void {\n  // TODO: This stuff could just go into the reducer. Leaving as-is for now\n  // since we're about to rewrite all the router reducer stuff anyway.\n  const url = new URL(addBasePath(href), location.href)\n  if (process.env.__NEXT_APP_NAV_FAIL_HANDLING) {\n    window.next.__pendingUrl = url\n  }\n\n  setLinkForCurrentNavigation(linkInstanceRef)\n\n  const onRouterTransitionStart = getProfilingHookForOnNavigationStart()\n  if (onRouterTransitionStart !== null) {\n    onRouterTransitionStart(href, navigateType)\n  }\n\n  dispatchAppRouterAction({\n    type: ACTION_NAVIGATE,\n    url,\n    isExternalUrl: isExternalURL(url),\n    locationSearch: location.search,\n    shouldScroll,\n    navigateType,\n    allowAliasing: true,\n  })\n}\n\nexport function dispatchTraverseAction(\n  href: string,\n  tree: FlightRouterState | undefined\n) {\n  const onRouterTransitionStart = getProfilingHookForOnNavigationStart()\n  if (onRouterTransitionStart !== null) {\n    onRouterTransitionStart(href, 'traverse')\n  }\n  dispatchAppRouterAction({\n    type: ACTION_RESTORE,\n    url: new URL(href),\n    tree,\n  })\n}\n\n/**\n * The app router that is exposed through `useRouter`. These are public API\n * methods. Internal Next.js code should call the lower level methods directly\n * (although there's lots of existing code that doesn't do that).\n */\nexport const publicAppRouterInstance: AppRouterInstance = {\n  back: () => window.history.back(),\n  forward: () => window.history.forward(),\n  prefetch: process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? // Unlike the old implementation, the Segment Cache doesn't store its\n      // data in the router reducer state; it writes into a global mutable\n      // cache. So we don't need to dispatch an action.\n      (href: string, options?: PrefetchOptions) => {\n        const actionQueue = getAppRouterActionQueue()\n        const prefetchKind = options?.kind ?? PrefetchKind.AUTO\n\n        // We don't currently offer a way to issue a runtime prefetch via `router.prefetch()`.\n        // This will be possible when we update its API to not take a PrefetchKind.\n        let fetchStrategy: PrefetchTaskFetchStrategy\n        switch (prefetchKind) {\n          case PrefetchKind.AUTO: {\n            // We default to PPR. We'll discover whether or not the route supports it with the initial prefetch.\n            fetchStrategy = FetchStrategy.PPR\n            break\n          }\n          case PrefetchKind.FULL: {\n            fetchStrategy = FetchStrategy.Full\n            break\n          }\n          case PrefetchKind.TEMPORARY: {\n            // This concept doesn't exist in the segment cache implementation.\n            return\n          }\n          default: {\n            prefetchKind satisfies never\n            // Despite typescript thinking that this can't happen,\n            // we might get an unexpected value from user code.\n            // We don't know what they want, but we know they want a prefetch,\n            // so use the default.\n            fetchStrategy = FetchStrategy.PPR\n          }\n        }\n\n        prefetchWithSegmentCache(\n          href,\n          actionQueue.state.nextUrl,\n          actionQueue.state.tree,\n          fetchStrategy,\n          options?.onInvalidate ?? null\n        )\n      }\n    : (href: string, options?: PrefetchOptions) => {\n        // Use the old prefetch implementation.\n        const actionQueue = getAppRouterActionQueue()\n        const url = createPrefetchURL(href)\n        if (url !== null) {\n          // The prefetch reducer doesn't actually update any state or\n          // trigger a rerender. It just writes to a mutable cache. So we\n          // shouldn't bother calling setState/dispatch; we can just re-run\n          // the reducer directly using the current state.\n          // TODO: Refactor this away from a \"reducer\" so it's\n          // less confusing.\n          prefetchReducer(actionQueue.state, {\n            type: ACTION_PREFETCH,\n            url,\n            kind: options?.kind ?? PrefetchKind.FULL,\n          })\n        }\n      },\n  replace: (href: string, options?: NavigateOptions) => {\n    startTransition(() => {\n      dispatchNavigateAction(href, 'replace', options?.scroll ?? true, null)\n    })\n  },\n  push: (href: string, options?: NavigateOptions) => {\n    startTransition(() => {\n      dispatchNavigateAction(href, 'push', options?.scroll ?? true, null)\n    })\n  },\n  refresh: () => {\n    startTransition(() => {\n      dispatchAppRouterAction({\n        type: ACTION_REFRESH,\n        origin: window.location.origin,\n      })\n    })\n  },\n  hmrRefresh: () => {\n    if (process.env.NODE_ENV !== 'development') {\n      throw new Error(\n        'hmrRefresh can only be used in development mode. Please use refresh instead.'\n      )\n    } else {\n      startTransition(() => {\n        dispatchAppRouterAction({\n          type: ACTION_HMR_REFRESH,\n          origin: window.location.origin,\n        })\n      })\n    }\n  },\n}\n\n// Exists for debugging purposes. Don't use in application code.\nif (typeof window !== 'undefined' && window.next) {\n  window.next.router = publicAppRouterInstance\n}\n"], "names": ["ACTION_REFRESH", "ACTION_SERVER_ACTION", "ACTION_NAVIGATE", "ACTION_RESTORE", "ACTION_HMR_REFRESH", "PrefetchKind", "ACTION_PREFETCH", "reducer", "startTransition", "isThenable", "FetchStrategy", "prefetch", "prefetchWithSegmentCache", "dispatchAppRouterAction", "addBasePath", "createPrefetchURL", "isExternalURL", "prefetchReducer", "setLinkForCurrentNavigation", "runRemainingActions", "actionQueue", "setState", "pending", "next", "runAction", "action", "needsRefresh", "dispatch", "type", "origin", "window", "location", "prevState", "state", "payload", "actionResult", "handleResult", "nextState", "discarded", "resolve", "then", "err", "reject", "dispatchAction", "resolvers", "deferred<PERSON><PERSON><PERSON>", "Promise", "newAction", "last", "globalActionQueue", "createMutableActionQueue", "initialState", "<PERSON><PERSON><PERSON><PERSON>", "result", "onRouterTransitionStart", "Error", "getCurrentAppRouterState", "getAppRouterActionQueue", "getProfilingHookForOnNavigationStart", "dispatchNavigateAction", "href", "navigateType", "shouldScroll", "linkInstanceRef", "url", "URL", "process", "env", "__NEXT_APP_NAV_FAIL_HANDLING", "__pendingUrl", "isExternalUrl", "locationSearch", "search", "allowAliasing", "dispatchTraverseAction", "tree", "publicAppRouterInstance", "back", "history", "forward", "__NEXT_CLIENT_SEGMENT_CACHE", "options", "prefetchKind", "kind", "AUTO", "fetchStrategy", "PPR", "FULL", "Full", "TEMPORARY", "nextUrl", "onInvalidate", "replace", "scroll", "push", "refresh", "hmrRefresh", "NODE_ENV", "router"], "mappings": "AAAA,SAIEA,cAAc,EACdC,oBAAoB,EACpBC,eAAe,EACfC,cAAc,EAEdC,kBAAkB,EAClBC,YAAY,EACZC,eAAe,QACV,wCAAuC;AAC9C,SAASC,OAAO,QAAQ,kCAAiC;AACzD,SAASC,eAAe,QAAQ,QAAO;AACvC,SAASC,UAAU,QAAQ,+BAA8B;AACzD,SACEC,aAAa,EACbC,YAAYC,wBAAwB,QAE/B,kBAAiB;AACxB,SAASC,uBAAuB,QAAQ,qBAAoB;AAC5D,SAASC,WAAW,QAAQ,mBAAkB;AAC9C,SAASC,iBAAiB,EAAEC,aAAa,QAAQ,eAAc;AAC/D,SAASC,eAAe,QAAQ,6CAA4C;AAM5E,SAASC,2BAA2B,QAA2B,UAAS;AAkCxE,SAASC,oBACPC,WAAiC,EACjCC,QAA8B;IAE9B,IAAID,YAAYE,OAAO,KAAK,MAAM;QAChCF,YAAYE,OAAO,GAAGF,YAAYE,OAAO,CAACC,IAAI;QAC9C,IAAIH,YAAYE,OAAO,KAAK,MAAM;YAChC,mEAAmE;YACnEE,UAAU;gBACRJ;gBACAK,QAAQL,YAAYE,OAAO;gBAC3BD;YACF;QACF,OAAO;YACL,4DAA4D;YAC5D,IAAID,YAAYM,YAAY,EAAE;gBAC5BN,YAAYM,YAAY,GAAG;gBAC3BN,YAAYO,QAAQ,CAClB;oBACEC,MAAM5B;oBACN6B,QAAQC,OAAOC,QAAQ,CAACF,MAAM;gBAChC,GACAR;YAEJ;QACF;IACF;AACF;AAEA,eAAeG,UAAU,KAQxB;IARwB,IAAA,EACvBJ,WAAW,EACXK,MAAM,EACNJ,QAAQ,EAKT,GARwB;IASvB,MAAMW,YAAYZ,YAAYa,KAAK;IAEnCb,YAAYE,OAAO,GAAGG;IAEtB,MAAMS,UAAUT,OAAOS,OAAO;IAC9B,MAAMC,eAAef,YAAYK,MAAM,CAACO,WAAWE;IAEnD,SAASE,aAAaC,SAAyB;QAC7C,kEAAkE;QAClE,IAAIZ,OAAOa,SAAS,EAAE;YACpB;QACF;QAEAlB,YAAYa,KAAK,GAAGI;QAEpBlB,oBAAoBC,aAAaC;QACjCI,OAAOc,OAAO,CAACF;IACjB;IAEA,8DAA8D;IAC9D,IAAI5B,WAAW0B,eAAe;QAC5BA,aAAaK,IAAI,CAACJ,cAAc,CAACK;YAC/BtB,oBAAoBC,aAAaC;YACjCI,OAAOiB,MAAM,CAACD;QAChB;IACF,OAAO;QACLL,aAAaD;IACf;AACF;AAEA,SAASQ,eACPvB,WAAiC,EACjCc,OAAuB,EACvBb,QAA8B;IAE9B,IAAIuB,YAGA;QAAEL,SAASlB;QAAUqB,QAAQ,KAAO;IAAE;IAE1C,mEAAmE;IACnE,wFAAwF;IACxF,2DAA2D;IAC3D,oDAAoD;IACpD,IAAIR,QAAQN,IAAI,KAAKzB,gBAAgB;QACnC,6DAA6D;QAC7D,MAAM0C,kBAAkB,IAAIC,QAAwB,CAACP,SAASG;YAC5DE,YAAY;gBAAEL;gBAASG;YAAO;QAChC;QAEAlC,gBAAgB;YACd,oGAAoG;YACpG,iEAAiE;YACjEa,SAASwB;QACX;IACF;IAEA,MAAME,YAA6B;QACjCb;QACAX,MAAM;QACNgB,SAASK,UAAUL,OAAO;QAC1BG,QAAQE,UAAUF,MAAM;IAC1B;IAEA,8BAA8B;IAC9B,IAAItB,YAAYE,OAAO,KAAK,MAAM;QAChC,iEAAiE;QACjE,4CAA4C;QAC5CF,YAAY4B,IAAI,GAAGD;QAEnBvB,UAAU;YACRJ;YACAK,QAAQsB;YACR1B;QACF;IACF,OAAO,IACLa,QAAQN,IAAI,KAAK1B,mBACjBgC,QAAQN,IAAI,KAAKzB,gBACjB;QACA,+EAA+E;QAC/E,oHAAoH;QACpHiB,YAAYE,OAAO,CAACgB,SAAS,GAAG;QAEhC,4EAA4E;QAC5E,sIAAsI;QACtIS,UAAUxB,IAAI,GAAGH,YAAYE,OAAO,CAACC,IAAI;QAEzC,2GAA2G;QAC3G,IAAIH,YAAYE,OAAO,CAACY,OAAO,CAACN,IAAI,KAAK3B,sBAAsB;YAC7DmB,YAAYM,YAAY,GAAG;QAC7B;QAEAF,UAAU;YACRJ;YACAK,QAAQsB;YACR1B;QACF;IACF,OAAO;QACL,oEAAoE;QACpE,+EAA+E;QAC/E,IAAID,YAAY4B,IAAI,KAAK,MAAM;YAC7B5B,YAAY4B,IAAI,CAACzB,IAAI,GAAGwB;QAC1B;QACA3B,YAAY4B,IAAI,GAAGD;IACrB;AACF;AAEA,IAAIE,oBAAiD;AAErD,OAAO,SAASC,yBACdC,YAA4B,EAC5BC,oBAAuD;IAEvD,MAAMhC,cAAoC;QACxCa,OAAOkB;QACPxB,UAAU,CAACO,SAAyBb,WAClCsB,eAAevB,aAAac,SAASb;QACvCI,QAAQ,OAAOQ,OAAuBR;YACpC,MAAM4B,SAAS9C,QAAQ0B,OAAOR;YAC9B,OAAO4B;QACT;QACA/B,SAAS;QACT0B,MAAM;QACNM,yBACEF,yBAAyB,QACzB,OAAOA,qBAAqBE,uBAAuB,KAAK,aAEpDF,qBAAqBE,uBAAuB,GAC5C;IACR;IAEA,IAAI,OAAOxB,WAAW,aAAa;QACjC,wEAAwE;QACxE,qEAAqE;QACrE,0CAA0C;QAC1C,IAAImB,sBAAsB,MAAM;YAC9B,MAAM,qBAGL,CAHK,IAAIM,MACR,sEACE,cAFE,qBAAA;uBAAA;4BAAA;8BAAA;YAGN;QACF;QACAN,oBAAoB7B;IACtB;IAEA,OAAOA;AACT;AAEA,OAAO,SAASoC;IACd,OAAOP,sBAAsB,OAAOA,kBAAkBhB,KAAK,GAAG;AAChE;AAEA,SAASwB;IACP,IAAIR,sBAAsB,MAAM;QAC9B,MAAM,qBAEL,CAFK,IAAIM,MACR,4EADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IACA,OAAON;AACT;AAEA,SAASS;IACP,IAAIT,sBAAsB,MAAM;QAC9B,OAAOA,kBAAkBK,uBAAuB;IAClD;IACA,OAAO;AACT;AAEA,OAAO,SAASK,uBACdC,IAAY,EACZC,YAA4C,EAC5CC,YAAqB,EACrBC,eAAoC;IAEpC,yEAAyE;IACzE,oEAAoE;IACpE,MAAMC,MAAM,IAAIC,IAAInD,YAAY8C,OAAO7B,SAAS6B,IAAI;IACpD,IAAIM,QAAQC,GAAG,CAACC,4BAA4B,EAAE;QAC5CtC,OAAOP,IAAI,CAAC8C,YAAY,GAAGL;IAC7B;IAEA9C,4BAA4B6C;IAE5B,MAAMT,0BAA0BI;IAChC,IAAIJ,4BAA4B,MAAM;QACpCA,wBAAwBM,MAAMC;IAChC;IAEAhD,wBAAwB;QACtBe,MAAM1B;QACN8D;QACAM,eAAetD,cAAcgD;QAC7BO,gBAAgBxC,SAASyC,MAAM;QAC/BV;QACAD;QACAY,eAAe;IACjB;AACF;AAEA,OAAO,SAASC,uBACdd,IAAY,EACZe,IAAmC;IAEnC,MAAMrB,0BAA0BI;IAChC,IAAIJ,4BAA4B,MAAM;QACpCA,wBAAwBM,MAAM;IAChC;IACA/C,wBAAwB;QACtBe,MAAMzB;QACN6D,KAAK,IAAIC,IAAIL;QACbe;IACF;AACF;AAEA;;;;CAIC,GACD,OAAO,MAAMC,0BAA6C;IACxDC,MAAM,IAAM/C,OAAOgD,OAAO,CAACD,IAAI;IAC/BE,SAAS,IAAMjD,OAAOgD,OAAO,CAACC,OAAO;IACrCpE,UAAUuD,QAAQC,GAAG,CAACa,2BAA2B,GAE7C,oEAAoE;IACpE,iDAAiD;IACjD,CAACpB,MAAcqB;QACb,MAAM7D,cAAcqC;YACCwB;QAArB,MAAMC,eAAeD,CAAAA,gBAAAA,2BAAAA,QAASE,IAAI,YAAbF,gBAAiB5E,aAAa+E,IAAI;QAEvD,sFAAsF;QACtF,2EAA2E;QAC3E,IAAIC;QACJ,OAAQH;YACN,KAAK7E,aAAa+E,IAAI;gBAAE;oBACtB,oGAAoG;oBACpGC,gBAAgB3E,cAAc4E,GAAG;oBACjC;gBACF;YACA,KAAKjF,aAAakF,IAAI;gBAAE;oBACtBF,gBAAgB3E,cAAc8E,IAAI;oBAClC;gBACF;YACA,KAAKnF,aAAaoF,SAAS;gBAAE;oBAC3B,kEAAkE;oBAClE;gBACF;YACA;gBAAS;oBACPP;oBACA,sDAAsD;oBACtD,mDAAmD;oBACnD,kEAAkE;oBAClE,sBAAsB;oBACtBG,gBAAgB3E,cAAc4E,GAAG;gBACnC;QACF;YAOEL;QALFrE,yBACEgD,MACAxC,YAAYa,KAAK,CAACyD,OAAO,EACzBtE,YAAYa,KAAK,CAAC0C,IAAI,EACtBU,eACAJ,CAAAA,wBAAAA,2BAAAA,QAASU,YAAY,YAArBV,wBAAyB;IAE7B,IACA,CAACrB,MAAcqB;QACb,uCAAuC;QACvC,MAAM7D,cAAcqC;QACpB,MAAMO,MAAMjD,kBAAkB6C;QAC9B,IAAII,QAAQ,MAAM;gBAURiB;YATR,4DAA4D;YAC5D,+DAA+D;YAC/D,iEAAiE;YACjE,gDAAgD;YAChD,oDAAoD;YACpD,kBAAkB;YAClBhE,gBAAgBG,YAAYa,KAAK,EAAE;gBACjCL,MAAMtB;gBACN0D;gBACAmB,MAAMF,CAAAA,gBAAAA,2BAAAA,QAASE,IAAI,YAAbF,gBAAiB5E,aAAakF,IAAI;YAC1C;QACF;IACF;IACJK,SAAS,CAAChC,MAAcqB;QACtBzE,gBAAgB;gBAC0ByE;YAAxCtB,uBAAuBC,MAAM,WAAWqB,CAAAA,kBAAAA,2BAAAA,QAASY,MAAM,YAAfZ,kBAAmB,MAAM;QACnE;IACF;IACAa,MAAM,CAAClC,MAAcqB;QACnBzE,gBAAgB;gBACuByE;YAArCtB,uBAAuBC,MAAM,QAAQqB,CAAAA,kBAAAA,2BAAAA,QAASY,MAAM,YAAfZ,kBAAmB,MAAM;QAChE;IACF;IACAc,SAAS;QACPvF,gBAAgB;YACdK,wBAAwB;gBACtBe,MAAM5B;gBACN6B,QAAQC,OAAOC,QAAQ,CAACF,MAAM;YAChC;QACF;IACF;IACAmE,YAAY;QACV,IAAI9B,QAAQC,GAAG,CAAC8B,QAAQ,KAAK,eAAe;YAC1C,MAAM,qBAEL,CAFK,IAAI1C,MACR,iFADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF,OAAO;YACL/C,gBAAgB;gBACdK,wBAAwB;oBACtBe,MAAMxB;oBACNyB,QAAQC,OAAOC,QAAQ,CAACF,MAAM;gBAChC;YACF;QACF;IACF;AACF,EAAC;AAED,gEAAgE;AAChE,IAAI,OAAOC,WAAW,eAAeA,OAAOP,IAAI,EAAE;IAChDO,OAAOP,IAAI,CAAC2E,MAAM,GAAGtB;AACvB", "ignoreList": [0]}