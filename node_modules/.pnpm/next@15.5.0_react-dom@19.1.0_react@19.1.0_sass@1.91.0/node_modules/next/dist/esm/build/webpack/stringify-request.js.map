{"version": 3, "sources": ["../../../src/build/webpack/stringify-request.ts"], "sourcesContent": ["import type webpack from 'webpack'\n\nexport function stringifyRequest(\n  loaderContext: webpack.LoaderContext<any>,\n  request: string\n) {\n  return JSON.stringify(\n    loaderContext.utils.contextify(\n      loaderContext.context || loaderContext.rootContext,\n      request\n    )\n  )\n}\n"], "names": ["stringifyRequest", "loaderContext", "request", "JSON", "stringify", "utils", "contextify", "context", "rootContext"], "mappings": "AAEA,OAAO,SAASA,iBACdC,aAAyC,EACzCC,OAAe;IAEf,OAAOC,KAAKC,SAAS,CACnBH,cAAcI,KAAK,CAACC,UAAU,CAC5BL,cAAcM,OAAO,IAAIN,cAAcO,WAAW,EAClDN;AAGN", "ignoreList": [0]}