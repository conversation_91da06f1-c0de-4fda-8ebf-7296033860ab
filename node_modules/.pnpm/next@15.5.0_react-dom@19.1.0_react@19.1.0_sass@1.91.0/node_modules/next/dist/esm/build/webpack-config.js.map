{"version": 3, "sources": ["../../src/build/webpack-config.ts"], "sourcesContent": ["import React from 'react'\nimport ReactRefreshWebpackPlugin from 'next/dist/compiled/@next/react-refresh-utils/dist/ReactRefreshWebpackPlugin'\nimport { yellow, bold } from '../lib/picocolors'\nimport crypto from 'crypto'\nimport { webpack } from 'next/dist/compiled/webpack/webpack'\nimport path from 'path'\n\nimport { getDefineEnv } from './define-env'\nimport { escapeStringRegexp } from '../shared/lib/escape-regexp'\nimport { WEBPACK_LAYERS, WEBPACK_RESOURCE_QUERIES } from '../lib/constants'\nimport type { WebpackLayerName } from '../lib/constants'\nimport {\n  isWebpackBundledLayer,\n  isWebpackClientOnlyLayer,\n  shouldUseReactServerCondition,\n  isWebpackDefaultLayer,\n  RSPACK_DEFAULT_LAYERS_REGEX,\n} from './utils'\nimport type { CustomRoutes } from '../lib/load-custom-routes.js'\nimport {\n  CLIENT_STATIC_FILES_RUNTIME_AMP,\n  CLIENT_STATIC_FILES_RUNTIME_MAIN,\n  CLIENT_STATIC_FILES_RUNTIME_MAIN_APP,\n  CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL,\n  CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH,\n  CLIENT_STATIC_FILES_RUNTIME_WEBPACK,\n  MIDDLEWARE_REACT_LOADABLE_MANIFEST,\n  REACT_LOADABLE_MANIFEST,\n  SERVER_DIRECTORY,\n  COMPILER_NAMES,\n} from '../shared/lib/constants'\nimport type { CompilerNameValues } from '../shared/lib/constants'\nimport { execOnce } from '../shared/lib/utils'\nimport type { NextConfigComplete } from '../server/config-shared'\nimport { finalizeEntrypoint } from './entries'\nimport * as Log from './output/log'\nimport { buildConfiguration } from './webpack/config'\nimport MiddlewarePlugin, {\n  getEdgePolyfilledModules,\n  handleWebpackExternalForEdgeRuntime,\n} from './webpack/plugins/middleware-plugin'\nimport BuildManifestPlugin from './webpack/plugins/build-manifest-plugin'\nimport { JsConfigPathsPlugin } from './webpack/plugins/jsconfig-paths-plugin'\nimport { DropClientPage } from './webpack/plugins/next-drop-client-page-plugin'\nimport PagesManifestPlugin from './webpack/plugins/pages-manifest-plugin'\nimport { ProfilingPlugin } from './webpack/plugins/profiling-plugin'\nimport { ReactLoadablePlugin } from './webpack/plugins/react-loadable-plugin'\nimport { WellKnownErrorsPlugin } from './webpack/plugins/wellknown-errors-plugin'\nimport { regexLikeCss } from './webpack/config/blocks/css'\nimport { CopyFilePlugin } from './webpack/plugins/copy-file-plugin'\nimport { ClientReferenceManifestPlugin } from './webpack/plugins/flight-manifest-plugin'\nimport { FlightClientEntryPlugin as NextFlightClientEntryPlugin } from './webpack/plugins/flight-client-entry-plugin'\nimport { RspackFlightClientEntryPlugin } from './webpack/plugins/rspack-flight-client-entry-plugin'\nimport { NextTypesPlugin } from './webpack/plugins/next-types-plugin'\nimport type {\n  Feature,\n  SWC_TARGET_TRIPLE,\n} from './webpack/plugins/telemetry-plugin/telemetry-plugin'\nimport type { Span } from '../trace'\nimport type { MiddlewareMatcher } from './analysis/get-page-static-info'\nimport loadJsConfig, {\n  type JsConfig,\n  type ResolvedBaseUrl,\n} from './load-jsconfig'\nimport { loadBindings } from './swc'\nimport { AppBuildManifestPlugin } from './webpack/plugins/app-build-manifest-plugin'\nimport { SubresourceIntegrityPlugin } from './webpack/plugins/subresource-integrity-plugin'\nimport { NextFontManifestPlugin } from './webpack/plugins/next-font-manifest-plugin'\nimport { getSupportedBrowsers } from './utils'\nimport { MemoryWithGcCachePlugin } from './webpack/plugins/memory-with-gc-cache-plugin'\nimport { getBabelConfigFile } from './get-babel-config-file'\nimport { needsExperimentalReact } from '../lib/needs-experimental-react'\nimport type { SWCLoaderOptions } from './webpack/loaders/next-swc-loader'\nimport { isResourceInPackages, makeExternalHandler } from './handle-externals'\nimport {\n  getMainField,\n  edgeConditionNames,\n} from './webpack-config-rules/resolve'\nimport { OptionalPeerDependencyResolverPlugin } from './webpack/plugins/optional-peer-dependency-resolve-plugin'\nimport {\n  createWebpackAliases,\n  createServerOnlyClientOnlyAliases,\n  createVendoredReactAliases,\n  createNextApiEsmAliases,\n  createAppRouterApiAliases,\n} from './create-compiler-aliases'\nimport { hasCustomExportOutput } from '../export/utils'\nimport { CssChunkingPlugin } from './webpack/plugins/css-chunking-plugin'\nimport {\n  getBabelLoader,\n  getReactCompilerLoader,\n} from './get-babel-loader-config'\nimport {\n  NEXT_PROJECT_ROOT,\n  NEXT_PROJECT_ROOT_DIST_CLIENT,\n} from './next-dir-paths'\nimport { getRspackCore, getRspackReactRefresh } from '../shared/lib/get-rspack'\nimport { RspackProfilingPlugin } from './webpack/plugins/rspack-profiling-plugin'\nimport getWebpackBundler from '../shared/lib/get-webpack-bundler'\nimport type { NextBuildContext } from './build-context'\nimport type { RootParamsLoaderOpts } from './webpack/loaders/next-root-params-loader'\nimport type { InvalidImportLoaderOpts } from './webpack/loaders/next-invalid-import-error-loader'\n\ntype ExcludesFalse = <T>(x: T | false) => x is T\ntype ClientEntries = {\n  [key: string]: string | string[]\n}\n\nconst EXTERNAL_PACKAGES =\n  require('../lib/server-external-packages.json') as string[]\n\nconst DEFAULT_TRANSPILED_PACKAGES =\n  require('../lib/default-transpiled-packages.json') as string[]\n\nif (parseInt(React.version) < 18) {\n  throw new Error('Next.js requires react >= 18.2.0 to be installed.')\n}\n\nexport const babelIncludeRegexes: RegExp[] = [\n  /next[\\\\/]dist[\\\\/](esm[\\\\/])?shared[\\\\/]lib/,\n  /next[\\\\/]dist[\\\\/](esm[\\\\/])?client/,\n  /next[\\\\/]dist[\\\\/](esm[\\\\/])?pages/,\n  /[\\\\/](strip-ansi|ansi-regex|styled-jsx)[\\\\/]/,\n]\n\nconst browserNonTranspileModules = [\n  // Transpiling `process/browser` will trigger babel compilation error due to value replacement.\n  // TypeError: Property left of AssignmentExpression expected node to be of a type [\"LVal\"] but instead got \"BooleanLiteral\"\n  // e.g. `process.browser = true` will become `true = true`.\n  /[\\\\/]node_modules[\\\\/]process[\\\\/]browser/,\n  // Exclude precompiled react packages from browser compilation due to SWC helper insertion (#61791),\n  // We fixed the issue but it's safer to exclude them from compilation since they don't need to be re-compiled.\n  /[\\\\/]next[\\\\/]dist[\\\\/]compiled[\\\\/](react|react-dom|react-server-dom-webpack)(-experimental)?($|[\\\\/])/,\n]\nconst precompileRegex = /[\\\\/]next[\\\\/]dist[\\\\/]compiled[\\\\/]/\n\nconst asyncStoragesRegex =\n  /next[\\\\/]dist[\\\\/](esm[\\\\/])?server[\\\\/]app-render[\\\\/](work-async-storage|action-async-storage|dynamic-access-async-storage|work-unit-async-storage)/\n\n// Support for NODE_PATH\nconst nodePathList = (process.env.NODE_PATH || '')\n  .split(process.platform === 'win32' ? ';' : ':')\n  .filter((p) => !!p)\n\nconst baseWatchOptions: webpack.Configuration['watchOptions'] = Object.freeze({\n  aggregateTimeout: 5,\n  ignored:\n    // Matches **/node_modules/**, **/.git/** and **/.next/**\n    /^((?:[^/]*(?:\\/|$))*)(\\.(git|next)|node_modules)(\\/((?:[^/]*(?:\\/|$))*)(?:$|\\/))?/,\n})\n\nfunction isModuleCSS(module: { type: string }) {\n  return (\n    // mini-css-extract-plugin\n    module.type === `css/mini-extract` ||\n    // extract-css-chunks-webpack-plugin (old)\n    module.type === `css/extract-chunks` ||\n    // extract-css-chunks-webpack-plugin (new)\n    module.type === `css/extract-css-chunks`\n  )\n}\n\nconst devtoolRevertWarning = execOnce(\n  (devtool: webpack.Configuration['devtool']) => {\n    console.warn(\n      yellow(bold('Warning: ')) +\n        bold(`Reverting webpack devtool to '${devtool}'.\\n`) +\n        'Changing the webpack devtool in development mode will cause severe performance regressions.\\n' +\n        'Read more: https://nextjs.org/docs/messages/improper-devtool'\n    )\n  }\n)\n\nlet loggedSwcDisabled = false\nlet loggedIgnoredCompilerOptions = false\nconst reactRefreshLoaderName =\n  'next/dist/compiled/@next/react-refresh-utils/dist/loader'\n\nfunction getReactRefreshLoader() {\n  return process.env.NEXT_RSPACK\n    ? getRspackReactRefresh().loader\n    : require.resolve(reactRefreshLoaderName)\n}\n\nexport function attachReactRefresh(\n  webpackConfig: webpack.Configuration,\n  targetLoader: webpack.RuleSetUseItem\n) {\n  const reactRefreshLoader = getReactRefreshLoader()\n  webpackConfig.module?.rules?.forEach((rule) => {\n    if (rule && typeof rule === 'object' && 'use' in rule) {\n      const curr = rule.use\n      // When the user has configured `defaultLoaders.babel` for a input file:\n      if (curr === targetLoader) {\n        rule.use = [reactRefreshLoader, curr as webpack.RuleSetUseItem]\n      } else if (\n        Array.isArray(curr) &&\n        curr.some((r) => r === targetLoader) &&\n        // Check if loader already exists:\n        !curr.some(\n          (r) => r === reactRefreshLoader || r === reactRefreshLoaderName\n        )\n      ) {\n        const idx = curr.findIndex((r) => r === targetLoader)\n        // Clone to not mutate user input\n        rule.use = [...curr]\n\n        // inject / input: [other, babel] output: [other, refresh, babel]:\n        rule.use.splice(idx, 0, reactRefreshLoader)\n      }\n    }\n  })\n}\n\nexport const NODE_RESOLVE_OPTIONS = {\n  dependencyType: 'commonjs',\n  modules: ['node_modules'],\n  fallback: false,\n  exportsFields: ['exports'],\n  importsFields: ['imports'],\n  conditionNames: ['node', 'require'],\n  descriptionFiles: ['package.json'],\n  extensions: ['.js', '.json', '.node'],\n  enforceExtensions: false,\n  symlinks: true,\n  mainFields: ['main'],\n  mainFiles: ['index'],\n  roots: [],\n  fullySpecified: false,\n  preferRelative: false,\n  preferAbsolute: false,\n  restrictions: [],\n}\n\nexport const NODE_BASE_RESOLVE_OPTIONS = {\n  ...NODE_RESOLVE_OPTIONS,\n  alias: false,\n}\n\nexport const NODE_ESM_RESOLVE_OPTIONS = {\n  ...NODE_RESOLVE_OPTIONS,\n  alias: false,\n  dependencyType: 'esm',\n  conditionNames: ['node', 'import'],\n  fullySpecified: true,\n}\n\nexport const NODE_BASE_ESM_RESOLVE_OPTIONS = {\n  ...NODE_ESM_RESOLVE_OPTIONS,\n  alias: false,\n}\n\nexport const nextImageLoaderRegex =\n  /\\.(png|jpg|jpeg|gif|webp|avif|ico|bmp|svg)$/i\n\nexport async function loadProjectInfo({\n  dir,\n  config,\n  dev,\n}: {\n  dir: string\n  config: NextConfigComplete\n  dev: boolean\n}): Promise<{\n  jsConfig: JsConfig\n  jsConfigPath?: string\n  resolvedBaseUrl: ResolvedBaseUrl\n  supportedBrowsers: string[] | undefined\n}> {\n  const { jsConfig, jsConfigPath, resolvedBaseUrl } = await loadJsConfig(\n    dir,\n    config\n  )\n  const supportedBrowsers = getSupportedBrowsers(dir, dev)\n  return {\n    jsConfig,\n    jsConfigPath,\n    resolvedBaseUrl,\n    supportedBrowsers,\n  }\n}\n\nexport function hasExternalOtelApiPackage(): boolean {\n  try {\n    require('@opentelemetry/api') as typeof import('@opentelemetry/api')\n    return true\n  } catch {\n    return false\n  }\n}\n\nconst UNSAFE_CACHE_REGEX = /[\\\\/]pages[\\\\/][^\\\\/]+(?:$|\\?|#)/\n\nexport function getCacheDirectories(\n  configs: webpack.Configuration[]\n): Set<string> {\n  return new Set(\n    configs\n      .map((cfg) => {\n        if (typeof cfg.cache === 'object' && cfg.cache.type === 'filesystem') {\n          return cfg.cache.cacheDirectory\n        }\n        return null\n      })\n      .filter((dir) => dir != null)\n  )\n}\n\nexport default async function getBaseWebpackConfig(\n  dir: string,\n  {\n    buildId,\n    encryptionKey,\n    config,\n    compilerType,\n    dev = false,\n    entrypoints,\n    isDevFallback = false,\n    pagesDir,\n    reactProductionProfiling = false,\n    rewrites,\n    originalRewrites,\n    originalRedirects,\n    runWebpackSpan,\n    appDir,\n    middlewareMatchers,\n    noMangling,\n    jsConfig,\n    jsConfigPath,\n    resolvedBaseUrl,\n    supportedBrowsers,\n    clientRouterFilters,\n    fetchCacheKeyPrefix,\n    isCompileMode,\n    previewProps,\n  }: {\n    previewProps: NonNullable<(typeof NextBuildContext)['previewProps']>\n    isCompileMode?: boolean\n    buildId: string\n    encryptionKey: string\n    config: NextConfigComplete\n    compilerType: CompilerNameValues\n    dev?: boolean\n    entrypoints: webpack.EntryObject\n    isDevFallback?: boolean\n    pagesDir: string | undefined\n    reactProductionProfiling?: boolean\n    rewrites: CustomRoutes['rewrites']\n    originalRewrites: CustomRoutes['rewrites'] | undefined\n    originalRedirects: CustomRoutes['redirects'] | undefined\n    runWebpackSpan: Span\n    appDir: string | undefined\n    middlewareMatchers?: MiddlewareMatcher[]\n    noMangling?: boolean\n    jsConfig: any\n    jsConfigPath?: string\n    resolvedBaseUrl: ResolvedBaseUrl\n    supportedBrowsers: string[] | undefined\n    clientRouterFilters?: {\n      staticFilter: ReturnType<\n        import('../shared/lib/bloom-filter').BloomFilter['export']\n      >\n      dynamicFilter: ReturnType<\n        import('../shared/lib/bloom-filter').BloomFilter['export']\n      >\n    }\n    fetchCacheKeyPrefix?: string\n  }\n): Promise<webpack.Configuration> {\n  const bundler = getWebpackBundler()\n  const isClient = compilerType === COMPILER_NAMES.client\n  const isEdgeServer = compilerType === COMPILER_NAMES.edgeServer\n  const isNodeServer = compilerType === COMPILER_NAMES.server\n\n  const isRspack = Boolean(process.env.NEXT_RSPACK)\n\n  const FlightClientEntryPlugin =\n    isRspack && process.env.BUILTIN_FLIGHT_CLIENT_ENTRY_PLUGIN\n      ? RspackFlightClientEntryPlugin\n      : NextFlightClientEntryPlugin\n\n  // If the current compilation is aimed at server-side code instead of client-side code.\n  const isNodeOrEdgeCompilation = isNodeServer || isEdgeServer\n\n  const hasRewrites =\n    rewrites.beforeFiles.length > 0 ||\n    rewrites.afterFiles.length > 0 ||\n    rewrites.fallback.length > 0\n\n  const hasAppDir = !!appDir\n  const disableOptimizedLoading = true\n  const bundledReactChannel = needsExperimentalReact(config)\n    ? '-experimental'\n    : ''\n\n  const babelConfigFile = getBabelConfigFile(dir)\n\n  if (!dev && hasCustomExportOutput(config)) {\n    config.distDir = '.next'\n  }\n  const distDir = path.join(dir, config.distDir)\n\n  let useSWCLoader = !babelConfigFile || config.experimental.forceSwcTransforms\n  let SWCBinaryTarget: [Feature, boolean] | undefined = undefined\n  if (useSWCLoader) {\n    // TODO: we do not collect wasm target yet\n    const binaryTarget = (\n      require('./swc') as typeof import('./swc')\n    )?.getBinaryMetadata?.()?.target as SWC_TARGET_TRIPLE\n    SWCBinaryTarget = binaryTarget\n      ? [`swc/target/${binaryTarget}` as const, true]\n      : undefined\n  }\n\n  if (!loggedSwcDisabled && !useSWCLoader && babelConfigFile) {\n    Log.info(\n      `Disabled SWC as replacement for Babel because of custom Babel configuration \"${path.relative(\n        dir,\n        babelConfigFile\n      )}\" https://nextjs.org/docs/messages/swc-disabled`\n    )\n    loggedSwcDisabled = true\n  }\n\n  // eagerly load swc bindings instead of waiting for transform calls\n  if (!babelConfigFile && isClient) {\n    await loadBindings(config.experimental.useWasmBinary)\n  }\n\n  // since `pages` doesn't always bundle by default we need to\n  // auto-include optimizePackageImports in transpilePackages\n  const finalTranspilePackages: string[] = (\n    config.transpilePackages || []\n  ).concat(DEFAULT_TRANSPILED_PACKAGES)\n\n  for (const pkg of config.experimental.optimizePackageImports || []) {\n    if (!finalTranspilePackages.includes(pkg)) {\n      finalTranspilePackages.push(pkg)\n    }\n  }\n\n  if (!loggedIgnoredCompilerOptions && !useSWCLoader && config.compiler) {\n    Log.info(\n      '`compiler` options in `next.config.js` will be ignored while using Babel https://nextjs.org/docs/messages/ignored-compiler-options'\n    )\n    loggedIgnoredCompilerOptions = true\n  }\n\n  const excludeCache: Record<string, boolean> = {}\n  function exclude(excludePath: string): boolean {\n    const cached = excludeCache[excludePath]\n    if (cached !== undefined) {\n      return cached\n    }\n\n    const shouldExclude =\n      excludePath.includes('node_modules') &&\n      !babelIncludeRegexes.some((r) => r.test(excludePath)) &&\n      !isResourceInPackages(excludePath, finalTranspilePackages)\n\n    excludeCache[excludePath] = shouldExclude\n    return shouldExclude\n  }\n\n  const shouldIncludeExternalDirs =\n    config.experimental.externalDir || !!config.transpilePackages\n  const codeCondition = {\n    test: { or: [/\\.(tsx|ts|js|cjs|mjs|jsx)$/, /__barrel_optimize__/] },\n    ...(shouldIncludeExternalDirs\n      ? // Allowing importing TS/TSX files from outside of the root dir.\n        {}\n      : { include: [dir, ...babelIncludeRegexes] }),\n    exclude,\n  }\n\n  const babelLoader = getBabelLoader(\n    useSWCLoader,\n    babelConfigFile,\n    isNodeOrEdgeCompilation,\n    distDir,\n    pagesDir,\n    dir,\n    (appDir || pagesDir)!,\n    dev,\n    isClient,\n    config.experimental?.reactCompiler,\n    codeCondition.exclude\n  )\n\n  const reactCompilerLoader = babelLoader\n    ? undefined\n    : getReactCompilerLoader(\n        config.experimental?.reactCompiler,\n        dir,\n        dev,\n        isNodeOrEdgeCompilation,\n        codeCondition.exclude\n      )\n\n  let swcTraceProfilingInitialized = false\n  const getSwcLoader = (extraOptions: Partial<SWCLoaderOptions>) => {\n    if (\n      config?.experimental?.swcTraceProfiling &&\n      !swcTraceProfilingInitialized\n    ) {\n      // This will init subscribers once only in a single process lifecycle,\n      // even though it can be called multiple times.\n      // Subscriber need to be initialized _before_ any actual swc's call (transform, etcs)\n      // to collect correct trace spans when they are called.\n      swcTraceProfilingInitialized = true\n      ;(\n        require('./swc') as typeof import('./swc')\n      )?.initCustomTraceSubscriber?.(\n        path.join(distDir, `swc-trace-profile-${Date.now()}.json`)\n      )\n    }\n\n    const useBuiltinSwcLoader = process.env.BUILTIN_SWC_LOADER\n    if (isRspack && useBuiltinSwcLoader) {\n      return {\n        loader: 'builtin:next-swc-loader',\n        options: {\n          isServer: isNodeOrEdgeCompilation,\n          rootDir: dir,\n          pagesDir,\n          appDir,\n          hasReactRefresh: dev && isClient,\n          transpilePackages: finalTranspilePackages,\n          supportedBrowsers,\n          swcCacheDir: path.join(\n            dir,\n            config?.distDir ?? '.next',\n            'cache',\n            'swc'\n          ),\n          serverReferenceHashSalt: encryptionKey,\n\n          // rspack specific options\n          pnp: Boolean(process.versions.pnp),\n          optimizeServerReact: Boolean(config.experimental.optimizeServerReact),\n          modularizeImports: config.modularizeImports,\n          decorators: Boolean(\n            jsConfig?.compilerOptions?.experimentalDecorators\n          ),\n          emitDecoratorMetadata: Boolean(\n            jsConfig?.compilerOptions?.emitDecoratorMetadata\n          ),\n          regeneratorRuntimePath: require.resolve(\n            'next/dist/compiled/regenerator-runtime'\n          ),\n\n          ...extraOptions,\n        },\n      }\n    }\n\n    return {\n      loader: 'next-swc-loader',\n      options: {\n        isServer: isNodeOrEdgeCompilation,\n        compilerType,\n        rootDir: dir,\n        pagesDir,\n        appDir,\n        hasReactRefresh: dev && isClient,\n        nextConfig: config,\n        jsConfig,\n        transpilePackages: finalTranspilePackages,\n        supportedBrowsers,\n        swcCacheDir: path.join(dir, config?.distDir ?? '.next', 'cache', 'swc'),\n        serverReferenceHashSalt: encryptionKey,\n        ...extraOptions,\n      } satisfies SWCLoaderOptions,\n    }\n  }\n\n  // RSC loaders, prefer ESM, set `esm` to true\n  const swcServerLayerLoader = getSwcLoader({\n    serverComponents: true,\n    bundleLayer: WEBPACK_LAYERS.reactServerComponents,\n    esm: true,\n  })\n  const swcSSRLayerLoader = getSwcLoader({\n    serverComponents: true,\n    bundleLayer: WEBPACK_LAYERS.serverSideRendering,\n    esm: true,\n  })\n  const swcBrowserLayerLoader = getSwcLoader({\n    serverComponents: true,\n    bundleLayer: WEBPACK_LAYERS.appPagesBrowser,\n    esm: true,\n  })\n  // Default swc loaders for pages doesn't prefer ESM.\n  const swcDefaultLoader = getSwcLoader({\n    serverComponents: true,\n    esm: false,\n  })\n\n  const defaultLoaders = {\n    babel: useSWCLoader ? swcDefaultLoader : babelLoader!,\n  }\n\n  const appServerLayerLoaders = hasAppDir\n    ? [\n        // When using Babel, we will have to add the SWC loader\n        // as an additional pass to handle RSC correctly.\n        // This will cause some performance overhead but\n        // acceptable as Babel will not be recommended.\n        swcServerLayerLoader,\n        babelLoader,\n        reactCompilerLoader,\n      ].filter(Boolean)\n    : []\n\n  const instrumentLayerLoaders = [\n    'next-flight-loader',\n    // When using Babel, we will have to add the SWC loader\n    // as an additional pass to handle RSC correctly.\n    // This will cause some performance overhead but\n    // acceptable as Babel will not be recommended.\n    swcServerLayerLoader,\n    babelLoader,\n  ].filter(Boolean)\n\n  const middlewareLayerLoaders = [\n    'next-flight-loader',\n    // When using Babel, we will have to use SWC to do the optimization\n    // for middleware to tree shake the unused default optimized imports like \"next/server\".\n    // This will cause some performance overhead but\n    // acceptable as Babel will not be recommended.\n    getSwcLoader({\n      serverComponents: true,\n      bundleLayer: WEBPACK_LAYERS.middleware,\n    }),\n    babelLoader,\n  ].filter(Boolean)\n\n  const reactRefreshLoaders = dev && isClient ? [getReactRefreshLoader()] : []\n\n  // client components layers: SSR or browser\n  const createClientLayerLoader = ({\n    isBrowserLayer,\n    reactRefresh,\n  }: {\n    isBrowserLayer: boolean\n    reactRefresh: boolean\n  }) => [\n    ...(reactRefresh ? reactRefreshLoaders : []),\n    {\n      // This loader handles actions and client entries\n      // in the client layer.\n      loader: 'next-flight-client-module-loader',\n    },\n    ...(hasAppDir\n      ? [\n          // When using Babel, we will have to add the SWC loader\n          // as an additional pass to handle RSC correctly.\n          // This will cause some performance overhead but\n          // acceptable as Babel will not be recommended.\n          isBrowserLayer ? swcBrowserLayerLoader : swcSSRLayerLoader,\n          babelLoader,\n          reactCompilerLoader,\n        ].filter(Boolean)\n      : []),\n  ]\n\n  const appBrowserLayerLoaders = createClientLayerLoader({\n    isBrowserLayer: true,\n    // reactRefresh for browser layer is applied conditionally to user-land source\n    reactRefresh: false,\n  })\n  const appSSRLayerLoaders = createClientLayerLoader({\n    isBrowserLayer: false,\n    reactRefresh: true,\n  })\n\n  // Loader for API routes needs to be differently configured as it shouldn't\n  // have RSC transpiler enabled, so syntax checks such as invalid imports won't\n  // be performed.\n  const apiRoutesLayerLoaders = useSWCLoader\n    ? getSwcLoader({\n        serverComponents: false,\n        bundleLayer: WEBPACK_LAYERS.apiNode,\n      })\n    : defaultLoaders.babel\n\n  const pageExtensions = config.pageExtensions\n\n  const outputPath = isNodeOrEdgeCompilation\n    ? path.join(distDir, SERVER_DIRECTORY)\n    : distDir\n\n  const reactServerCondition = [\n    'react-server',\n    ...(isEdgeServer ? edgeConditionNames : []),\n    // inherits the default conditions\n    '...',\n  ]\n\n  const reactRefreshEntry = isRspack\n    ? getRspackReactRefresh().entry\n    : require.resolve(\n        `next/dist/compiled/@next/react-refresh-utils/dist/runtime`\n      )\n\n  const clientEntries = isClient\n    ? ({\n        // Backwards compatibility\n        'main.js': [],\n        ...(dev\n          ? {\n              [CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH]: reactRefreshEntry,\n              [CLIENT_STATIC_FILES_RUNTIME_AMP]:\n                `./` +\n                path\n                  .relative(\n                    dir,\n                    path.join(NEXT_PROJECT_ROOT_DIST_CLIENT, 'dev', 'amp-dev')\n                  )\n                  .replace(/\\\\/g, '/'),\n            }\n          : {}),\n        [CLIENT_STATIC_FILES_RUNTIME_MAIN]:\n          `./` +\n          path\n            .relative(\n              dir,\n              path.join(\n                NEXT_PROJECT_ROOT_DIST_CLIENT,\n                dev ? `next-dev.js` : 'next.js'\n              )\n            )\n            .replace(/\\\\/g, '/'),\n        ...(hasAppDir\n          ? {\n              [CLIENT_STATIC_FILES_RUNTIME_MAIN_APP]: dev\n                ? [\n                    reactRefreshEntry,\n                    `./` +\n                      path\n                        .relative(\n                          dir,\n                          path.join(\n                            NEXT_PROJECT_ROOT_DIST_CLIENT,\n                            'app-next-dev.js'\n                          )\n                        )\n                        .replace(/\\\\/g, '/'),\n                  ]\n                : [\n                    `./` +\n                      path\n                        .relative(\n                          dir,\n                          path.join(\n                            NEXT_PROJECT_ROOT_DIST_CLIENT,\n                            'app-next.js'\n                          )\n                        )\n                        .replace(/\\\\/g, '/'),\n                  ],\n            }\n          : {}),\n      } satisfies ClientEntries)\n    : undefined\n\n  const resolveConfig: webpack.Configuration['resolve'] = {\n    // Disable .mjs for node_modules bundling\n    extensions: ['.js', '.mjs', '.tsx', '.ts', '.jsx', '.json', '.wasm'],\n    extensionAlias: config.experimental.extensionAlias,\n    modules: [\n      'node_modules',\n      ...nodePathList, // Support for NODE_PATH environment variable\n    ],\n    alias: createWebpackAliases({\n      distDir,\n      isClient,\n      isEdgeServer,\n      dev,\n      config,\n      pagesDir,\n      appDir,\n      dir,\n      reactProductionProfiling,\n    }),\n    ...(isClient\n      ? {\n          fallback: {\n            process: require.resolve('./polyfills/process'),\n          },\n        }\n      : undefined),\n    // default main fields use pages dir ones, and customize app router ones in loaders.\n    mainFields: getMainField(compilerType, false),\n    ...(isEdgeServer && {\n      conditionNames: edgeConditionNames,\n    }),\n    plugins: [\n      isNodeServer ? new OptionalPeerDependencyResolverPlugin() : undefined,\n    ].filter(Boolean) as webpack.ResolvePluginInstance[],\n    ...((isRspack && jsConfigPath\n      ? {\n          // Skip paths that are routed to a .d.ts file\n          restrictions: [/^(?!.*\\.d\\.ts$).*$/],\n          tsConfig: {\n            configFile: jsConfigPath,\n          },\n        }\n      : {}) as any),\n  }\n\n  // Packages which will be split into the 'framework' chunk.\n  // Only top-level packages are included, e.g. nested copies like\n  // 'node_modules/meow/node_modules/object-assign' are not included.\n  const nextFrameworkPaths: string[] = []\n  const topLevelFrameworkPaths: string[] = []\n  const visitedFrameworkPackages = new Set<string>()\n  // Adds package-paths of dependencies recursively\n  const addPackagePath = (\n    packageName: string,\n    relativeToPath: string,\n    paths: string[]\n  ) => {\n    try {\n      if (visitedFrameworkPackages.has(packageName)) {\n        return\n      }\n      visitedFrameworkPackages.add(packageName)\n\n      const packageJsonPath = require.resolve(`${packageName}/package.json`, {\n        paths: [relativeToPath],\n      })\n\n      // Include a trailing slash so that a `.startsWith(packagePath)` check avoids false positives\n      // when one package name starts with the full name of a different package.\n      // For example:\n      //   \"node_modules/react-slider\".startsWith(\"node_modules/react\")  // true\n      //   \"node_modules/react-slider\".startsWith(\"node_modules/react/\") // false\n      const directory = path.join(packageJsonPath, '../')\n\n      // Returning from the function in case the directory has already been added and traversed\n      if (paths.includes(directory)) return\n      paths.push(directory)\n      const dependencies = require(packageJsonPath).dependencies || {}\n      for (const name of Object.keys(dependencies)) {\n        addPackagePath(name, directory, paths)\n      }\n    } catch (_) {\n      // don't error on failing to resolve framework packages\n    }\n  }\n\n  for (const packageName of [\n    'react',\n    'react-dom',\n    ...(hasAppDir\n      ? [\n          `next/dist/compiled/react${bundledReactChannel}`,\n          `next/dist/compiled/react-dom${bundledReactChannel}`,\n        ]\n      : []),\n  ]) {\n    addPackagePath(packageName, dir, topLevelFrameworkPaths)\n  }\n  addPackagePath('next', dir, nextFrameworkPaths)\n\n  const crossOrigin = config.crossOrigin\n\n  // The `serverExternalPackages` should not conflict with\n  // the `transpilePackages`.\n  if (config.serverExternalPackages && finalTranspilePackages) {\n    const externalPackageConflicts = finalTranspilePackages.filter((pkg) =>\n      config.serverExternalPackages?.includes(pkg)\n    )\n    if (externalPackageConflicts.length > 0) {\n      throw new Error(\n        `The packages specified in the 'transpilePackages' conflict with the 'serverExternalPackages': ${externalPackageConflicts.join(\n          ', '\n        )}`\n      )\n    }\n  }\n\n  // For original request, such as `package name`\n  const optOutBundlingPackages = EXTERNAL_PACKAGES.concat(\n    ...(config.serverExternalPackages || [])\n  ).filter((pkg) => !finalTranspilePackages?.includes(pkg))\n  // For resolved request, such as `absolute path/package name/foo/bar.js`\n  const optOutBundlingPackageRegex = new RegExp(\n    `[/\\\\\\\\]node_modules[/\\\\\\\\](${optOutBundlingPackages\n      .map((p) => p.replace(/\\//g, '[/\\\\\\\\]'))\n      .join('|')})[/\\\\\\\\]`\n  )\n\n  const transpilePackagesRegex = new RegExp(\n    `[/\\\\\\\\]node_modules[/\\\\\\\\](${finalTranspilePackages\n      ?.map((p) => p.replace(/\\//g, '[/\\\\\\\\]'))\n      .join('|')})[/\\\\\\\\]`\n  )\n\n  const handleExternals = makeExternalHandler({\n    config,\n    optOutBundlingPackageRegex,\n    transpiledPackages: finalTranspilePackages,\n    dir,\n  })\n\n  const pageExtensionsRegex = new RegExp(`\\\\.(${pageExtensions.join('|')})$`)\n\n  const aliasCodeConditionTest = [codeCondition.test, pageExtensionsRegex]\n\n  const builtinModules = (require('module') as typeof import('module'))\n    .builtinModules\n\n  const bunExternals = [\n    'bun:ffi',\n    'bun:jsc',\n    'bun:sqlite',\n    'bun:test',\n    'bun:wrap',\n    'bun',\n  ]\n\n  const shouldEnableSlowModuleDetection =\n    !!config.experimental.slowModuleDetection && dev\n\n  const getParallelism = () => {\n    const override = Number(process.env.NEXT_WEBPACK_PARALLELISM)\n    if (shouldEnableSlowModuleDetection) {\n      if (override) {\n        console.warn(\n          'NEXT_WEBPACK_PARALLELISM is specified but will be ignored due to experimental.slowModuleDetection being enabled.'\n        )\n      }\n      return 1\n    }\n    return override || undefined\n  }\n\n  const telemetryPlugin =\n    !dev &&\n    isClient &&\n    new (\n      require('./webpack/plugins/telemetry-plugin/telemetry-plugin') as typeof import('./webpack/plugins/telemetry-plugin/telemetry-plugin')\n    ).TelemetryPlugin(\n      new Map(\n        [\n          ['swcLoader', useSWCLoader],\n          ['swcRelay', !!config.compiler?.relay],\n          ['swcStyledComponents', !!config.compiler?.styledComponents],\n          [\n            'swcReactRemoveProperties',\n            !!config.compiler?.reactRemoveProperties,\n          ],\n          [\n            'swcExperimentalDecorators',\n            !!jsConfig?.compilerOptions?.experimentalDecorators,\n          ],\n          ['swcRemoveConsole', !!config.compiler?.removeConsole],\n          ['swcImportSource', !!jsConfig?.compilerOptions?.jsxImportSource],\n          ['swcEmotion', !!config.compiler?.emotion],\n          ['transpilePackages', !!config.transpilePackages],\n          ['skipMiddlewareUrlNormalize', !!config.skipMiddlewareUrlNormalize],\n          ['skipTrailingSlashRedirect', !!config.skipTrailingSlashRedirect],\n          ['modularizeImports', !!config.modularizeImports],\n          // If esmExternals is not same as default value, it represents customized usage\n          ['esmExternals', config.experimental.esmExternals !== true],\n          SWCBinaryTarget,\n        ].filter<[Feature, boolean]>(Boolean as any)\n      )\n    )\n\n  let webpackConfig: webpack.Configuration = {\n    parallelism: getParallelism(),\n    ...(isNodeServer ? { externalsPresets: { node: true } } : {}),\n    // @ts-ignore\n    externals:\n      isClient || isEdgeServer\n        ? // make sure importing \"next\" is handled gracefully for client\n          // bundles in case a user imported types and it wasn't removed\n          // TODO: should we warn/error for this instead?\n          [\n            'next',\n            ...(isEdgeServer\n              ? [\n                  {\n                    '@builder.io/partytown': '{}',\n                    'next/dist/compiled/etag': '{}',\n                  },\n                  getEdgePolyfilledModules(),\n                  handleWebpackExternalForEdgeRuntime,\n                ]\n              : []),\n          ]\n        : [\n            ...builtinModules,\n            ...bunExternals,\n            ({\n              context,\n              request,\n              dependencyType,\n              contextInfo,\n              getResolve,\n            }: {\n              context: string\n              request: string\n              dependencyType: string\n              contextInfo: {\n                issuer: string\n                issuerLayer: string | null\n                compiler: string\n              }\n              getResolve: (\n                options: any\n              ) => (\n                resolveContext: string,\n                resolveRequest: string,\n                callback: (\n                  err?: Error,\n                  result?: string,\n                  resolveData?: { descriptionFileData?: { type?: any } }\n                ) => void\n              ) => void\n            }) =>\n              handleExternals(\n                context,\n                request,\n                dependencyType,\n                contextInfo.issuerLayer as WebpackLayerName,\n                (options) => {\n                  const resolveFunction = getResolve(options)\n                  return (resolveContext: string, requestToResolve: string) =>\n                    new Promise((resolve, reject) => {\n                      resolveFunction(\n                        resolveContext,\n                        requestToResolve,\n                        (err, result, resolveData) => {\n                          if (err) return reject(err)\n                          if (!result) return resolve([null, false])\n                          const isEsm = /\\.js$/i.test(result)\n                            ? resolveData?.descriptionFileData?.type ===\n                              'module'\n                            : /\\.mjs$/i.test(result)\n                          resolve([result, isEsm])\n                        }\n                      )\n                    })\n                }\n              ),\n          ],\n\n    optimization: {\n      emitOnErrors: !dev,\n      checkWasmTypes: false,\n      nodeEnv: false,\n\n      splitChunks: (():\n        | Required<webpack.Configuration>['optimization']['splitChunks']\n        | false => {\n        // server chunking\n        if (dev) {\n          if (isNodeServer) {\n            /*\n              In development, we want to split code that comes from `node_modules` into their own chunks.\n              This is because in development, we often need to reload the user bundle due to changes in the code.\n              To work around this, we put all the vendor code into separate chunks so that we don't need to reload them.\n              This is safe because the vendor code doesn't change between reloads.\n            */\n            const extractRootNodeModule = (modulePath: string) => {\n              // This regex is used to extract the root node module name to be used as the chunk group name.\n              // example: ../../node_modules/.pnpm/next@10/foo/node_modules/bar -> next@10\n              const regex =\n                /node_modules(?:\\/|\\\\)\\.?(?:pnpm(?:\\/|\\\\))?([^/\\\\]+)/\n              const match = modulePath.match(regex)\n              return match ? match[1] : null\n            }\n            return {\n              cacheGroups: {\n                // this chunk configuration gives us a separate chunk for each top level module in node_modules\n                // or a hashed chunk if we can't extract the module name.\n                vendor: {\n                  chunks: 'all',\n                  reuseExistingChunk: true,\n                  test: /[\\\\/]node_modules[\\\\/]/,\n                  minSize: 0,\n                  minChunks: 1,\n                  maxAsyncRequests: 300,\n                  maxInitialRequests: 300,\n                  name: (module: webpack.Module) => {\n                    const moduleId = module.nameForCondition()!\n                    const rootModule = extractRootNodeModule(moduleId)\n                    if (rootModule) {\n                      return `vendor-chunks/${rootModule}`\n                    } else {\n                      const hash = crypto.createHash('sha1').update(moduleId)\n                      hash.update(moduleId)\n                      return `vendor-chunks/${hash.digest('hex')}`\n                    }\n                  },\n                },\n                // disable the default chunk groups\n                default: false,\n                defaultVendors: false,\n              },\n            }\n          }\n\n          return false\n        }\n\n        if (isNodeServer || isEdgeServer) {\n          return {\n            filename: `${isEdgeServer ? `edge-chunks/` : ''}[name].js`,\n            chunks: 'all',\n            minChunks: 2,\n          }\n        }\n\n        const frameworkCacheGroup = {\n          chunks: 'all' as const,\n          name: 'framework',\n          // Ensures the framework chunk is not created for App Router.\n          layer: isWebpackDefaultLayer,\n          test(module: any) {\n            const resource = module.nameForCondition?.()\n            return resource\n              ? topLevelFrameworkPaths.some((pkgPath) =>\n                  resource.startsWith(pkgPath)\n                )\n              : false\n          },\n          priority: 40,\n          // Don't let webpack eliminate this chunk (prevents this chunk from\n          // becoming a part of the commons chunk)\n          enforce: true,\n        }\n\n        const libCacheGroup = {\n          test(module: {\n            type: string\n            size: Function\n            nameForCondition: Function\n          }): boolean {\n            return (\n              !module.type?.startsWith('css') &&\n              module.size() > 160000 &&\n              /node_modules[/\\\\]/.test(module.nameForCondition() || '')\n            )\n          },\n          name(module: {\n            layer: string | null | undefined\n            type: string\n            libIdent?: Function\n            updateHash: (hash: crypto.Hash) => void\n          }): string {\n            const hash = crypto.createHash('sha1')\n            if (isModuleCSS(module)) {\n              module.updateHash(hash)\n            } else {\n              if (!module.libIdent) {\n                throw new Error(\n                  `Encountered unknown module type: ${module.type}. Please open an issue.`\n                )\n              }\n              hash.update(module.libIdent({ context: dir }))\n            }\n\n            // Ensures the name of the chunk is not the same between two modules in different layers\n            // E.g. if you import 'button-library' in App Router and Pages Router we don't want these to be bundled in the same chunk\n            // as they're never used on the same page.\n            if (module.layer) {\n              hash.update(module.layer)\n            }\n\n            return hash.digest('hex').substring(0, 8)\n          },\n          priority: 30,\n          minChunks: 1,\n          reuseExistingChunk: true,\n        }\n\n        // client chunking\n        return {\n          // Keep main and _app chunks unsplitted in webpack 5\n          // as we don't need a separate vendor chunk from that\n          // and all other chunk depend on them so there is no\n          // duplication that need to be pulled out.\n          chunks: isRspack\n            ? // using a function here causes noticable slowdown\n              // in rspack\n              /(?!polyfills|main|pages\\/_app)/\n            : (chunk: any) =>\n                !/^(polyfills|main|pages\\/_app)$/.test(chunk.name),\n\n          cacheGroups: isRspack\n            ? {\n                framework: {\n                  chunks: 'all' as const,\n                  name: 'framework',\n                  layer: RSPACK_DEFAULT_LAYERS_REGEX,\n                  test: /[/]node_modules[/](react|react-dom|next[/]dist[/]compiled[/](react|react-dom)(-experimental)?)[/]/,\n                  priority: 40,\n                  enforce: true,\n                },\n                lib: {\n                  test: /[/]node_modules[/](?!.*\\.(css|scss|sass|less|styl)$)/,\n                  name: 'lib',\n                  chunks: 'all',\n                  priority: 30,\n                  minChunks: 1,\n                  reuseExistingChunk: true,\n                },\n              }\n            : {\n                framework: frameworkCacheGroup,\n                lib: libCacheGroup,\n              },\n          maxInitialRequests: 25,\n          minSize: 20000,\n        }\n      })(),\n      runtimeChunk: isClient\n        ? { name: CLIENT_STATIC_FILES_RUNTIME_WEBPACK }\n        : undefined,\n\n      minimize:\n        !dev &&\n        (isClient ||\n          isEdgeServer ||\n          (isNodeServer && config.experimental.serverMinification)),\n      minimizer: [\n        // Minify JavaScript\n        isRspack\n          ? new (getRspackCore().SwcJsMinimizerRspackPlugin)({\n              // JS minimizer configuration\n              // options should align with crates/napi/src/minify.rs#patch_opts\n              minimizerOptions: {\n                compress: {\n                  inline: 2,\n                  global_defs: {\n                    'process.env.__NEXT_PRIVATE_MINIMIZE_MACRO_FALSE': false,\n                  },\n                },\n                mangle: !noMangling && {\n                  reserved: ['AbortSignal'],\n                  disableCharFreq: !isClient,\n                },\n              },\n            })\n          : (compiler: webpack.Compiler) => {\n              // @ts-ignore No typings yet\n              const { MinifyPlugin } =\n                require('./webpack/plugins/minify-webpack-plugin/src') as typeof import('./webpack/plugins/minify-webpack-plugin/src')\n              new MinifyPlugin({\n                noMangling,\n                disableCharFreq: !isClient,\n              }).apply(compiler)\n            },\n        // Minify CSS\n        // By default, Rspack uses LightningCSS for CSS minification.\n        // Rspack uses css-minimizer-plugin by default for compatibility.\n        isRspack &&\n        (process.env.__NEXT_TEST_MODE\n          ? config.experimental.useLightningcss\n          : config.experimental?.useLightningcss === undefined ||\n            config.experimental.useLightningcss)\n          ? new (getRspackCore().LightningCssMinimizerRspackPlugin)({\n              // CSS minimizer configuration\n              minimizerOptions: {\n                targets: supportedBrowsers,\n              },\n            })\n          : (compiler: webpack.Compiler) => {\n              const { CssMinimizerPlugin } =\n                require('./webpack/plugins/css-minimizer-plugin') as typeof import('./webpack/plugins/css-minimizer-plugin')\n              new CssMinimizerPlugin({\n                postcssOptions: {\n                  map: {\n                    // `inline: false` generates the source map in a separate file.\n                    // Otherwise, the CSS file is needlessly large.\n                    inline: false,\n                    // `annotation: false` skips appending the `sourceMappingURL`\n                    // to the end of the CSS file. Webpack already handles this.\n                    annotation: false,\n                  },\n                },\n              }).apply(compiler)\n            },\n      ],\n    },\n    context: dir,\n    // Kept as function to be backwards compatible\n    entry: async () => {\n      return {\n        ...(clientEntries ? clientEntries : {}),\n        ...entrypoints,\n      }\n    },\n    watchOptions: Object.freeze({\n      ...baseWatchOptions,\n      poll: config.watchOptions?.pollIntervalMs,\n    }),\n    output: {\n      // we must set publicPath to an empty value to override the default of\n      // auto which doesn't work in IE11\n      publicPath: `${\n        config.assetPrefix\n          ? config.assetPrefix.endsWith('/')\n            ? config.assetPrefix.slice(0, -1)\n            : config.assetPrefix\n          : ''\n      }/_next/`,\n      path: !dev && isNodeServer ? path.join(outputPath, 'chunks') : outputPath,\n      // On the server we don't use hashes\n      filename: isNodeOrEdgeCompilation\n        ? dev || isEdgeServer\n          ? `[name].js`\n          : `../[name].js`\n        : `static/chunks/${isDevFallback ? 'fallback/' : ''}[name]${\n            dev ? '' : '-[contenthash]'\n          }.js`,\n      library: isClient || isEdgeServer ? '_N_E' : undefined,\n      libraryTarget: isClient || isEdgeServer ? 'assign' : 'commonjs2',\n      hotUpdateChunkFilename: 'static/webpack/[id].[fullhash].hot-update.js',\n      hotUpdateMainFilename:\n        'static/webpack/[fullhash].[runtime].hot-update.json',\n      // This saves chunks with the name given via `import()`\n      chunkFilename: isNodeOrEdgeCompilation\n        ? '[name].js'\n        : `static/chunks/${isDevFallback ? 'fallback/' : ''}${\n            dev ? '[name]' : '[name].[contenthash]'\n          }.js`,\n      strictModuleExceptionHandling: true,\n      crossOriginLoading: crossOrigin,\n      // if `sources[number]` is not an absolute path, it's is resolved\n      // relative to the location of the source map file (https://tc39.es/source-map/#resolving-sources).\n      // However, Webpack's `resource-path` is relative to the app dir.\n      // TODO: Either `sourceRoot` should be populated with the root and then we can use `[resource-path]`\n      // or we need a way to resolve return `path.relative(sourceMapLocation, info.resourcePath)`\n      devtoolModuleFilenameTemplate: dev\n        ? '[absolute-resource-path]'\n        : undefined,\n      webassemblyModuleFilename: 'static/wasm/[modulehash].wasm',\n      hashFunction: 'xxhash64',\n      hashDigestLength: 16,\n    },\n    performance: false,\n    resolve: resolveConfig,\n    resolveLoader: {\n      // The loaders Next.js provides\n      alias: [\n        'error-loader',\n        'next-swc-loader',\n        'next-client-pages-loader',\n        'next-image-loader',\n        'next-metadata-image-loader',\n        'next-style-loader',\n        'next-flight-loader',\n        'next-flight-client-entry-loader',\n        'next-flight-action-entry-loader',\n        'next-flight-client-module-loader',\n        'next-flight-server-reference-proxy-loader',\n        'empty-loader',\n        'next-middleware-loader',\n        'next-edge-function-loader',\n        'next-edge-app-route-loader',\n        'next-edge-ssr-loader',\n        'next-middleware-asset-loader',\n        'next-middleware-wasm-loader',\n        'next-app-loader',\n        'next-route-loader',\n        'next-font-loader',\n        'next-invalid-import-error-loader',\n        'next-metadata-route-loader',\n        'modularize-import-loader',\n        'next-barrel-loader',\n        'next-error-browser-binary-loader',\n        'next-root-params-loader',\n      ].reduce(\n        (alias, loader) => {\n          // using multiple aliases to replace `resolveLoader.modules`\n          alias[loader] = path.join(__dirname, 'webpack', 'loaders', loader)\n\n          return alias\n        },\n        {} as Record<string, string>\n      ),\n      modules: [\n        'node_modules',\n        ...nodePathList, // Support for NODE_PATH environment variable\n      ],\n      plugins: [],\n    },\n    module: {\n      rules: [\n        // Alias server-only and client-only to proper exports based on bundling layers\n        {\n          issuerLayer: {\n            or: [\n              ...WEBPACK_LAYERS.GROUP.serverOnly,\n              ...WEBPACK_LAYERS.GROUP.neutralTarget,\n            ],\n          },\n          resolve: {\n            // Error on client-only but allow server-only\n            alias: createServerOnlyClientOnlyAliases(true),\n          },\n        },\n        {\n          issuerLayer: {\n            not: [\n              ...WEBPACK_LAYERS.GROUP.serverOnly,\n              ...WEBPACK_LAYERS.GROUP.neutralTarget,\n            ],\n          },\n          resolve: {\n            // Error on server-only but allow client-only\n            alias: createServerOnlyClientOnlyAliases(false),\n          },\n        },\n        // Detect server-only / client-only imports and error in build time\n        {\n          test: [\n            /^client-only$/,\n            /next[\\\\/]dist[\\\\/]compiled[\\\\/]client-only[\\\\/]error/,\n          ],\n          loader: 'next-invalid-import-error-loader',\n          issuerLayer: {\n            or: WEBPACK_LAYERS.GROUP.serverOnly,\n          },\n          options: {\n            message:\n              \"'client-only' cannot be imported from a Server Component module. It should only be used from a Client Component.\",\n          },\n        },\n        {\n          test: [\n            /^server-only$/,\n            /next[\\\\/]dist[\\\\/]compiled[\\\\/]server-only[\\\\/]index/,\n          ],\n          loader: 'next-invalid-import-error-loader',\n          issuerLayer: {\n            not: [\n              ...WEBPACK_LAYERS.GROUP.serverOnly,\n              ...WEBPACK_LAYERS.GROUP.neutralTarget,\n            ],\n          },\n          options: {\n            message:\n              \"'server-only' cannot be imported from a Client Component module. It should only be used from a Server Component.\",\n          },\n        },\n        // Potential the bundle introduced into middleware and api can be poisoned by client-only\n        // but not being used, so we disabled the `client-only` erroring on these layers.\n        // `server-only` is still available.\n        {\n          test: [\n            /^client-only$/,\n            /next[\\\\/]dist[\\\\/]compiled[\\\\/]client-only[\\\\/]error/,\n          ],\n          loader: 'empty-loader',\n          issuerLayer: {\n            or: WEBPACK_LAYERS.GROUP.neutralTarget,\n          },\n        },\n        ...(isNodeServer\n          ? []\n          : [\n              {\n                test: /[\\\\/].*?\\.node$/,\n                loader: 'next-error-browser-binary-loader',\n              },\n            ]),\n        ...(hasAppDir\n          ? [\n              {\n                // Make sure that AsyncLocalStorage module instance is shared between server and client\n                // layers.\n                layer: WEBPACK_LAYERS.shared,\n                test: asyncStoragesRegex,\n              },\n              // Convert metadata routes to separate layer\n              {\n                resourceQuery: new RegExp(\n                  WEBPACK_RESOURCE_QUERIES.metadataRoute\n                ),\n                layer: WEBPACK_LAYERS.reactServerComponents,\n              },\n              {\n                // Ensure that the app page module is in the client layers, this\n                // enables React to work correctly for RSC.\n                layer: WEBPACK_LAYERS.serverSideRendering,\n                test: /next[\\\\/]dist[\\\\/](esm[\\\\/])?server[\\\\/]route-modules[\\\\/]app-page[\\\\/]module/,\n              },\n              {\n                issuerLayer: isWebpackBundledLayer,\n                resolve: {\n                  alias: createNextApiEsmAliases(),\n                },\n              },\n              {\n                issuerLayer: shouldUseReactServerCondition,\n                resolve: {\n                  alias: createAppRouterApiAliases(true),\n                },\n              },\n              {\n                issuerLayer: isWebpackClientOnlyLayer,\n                resolve: {\n                  alias: createAppRouterApiAliases(false),\n                },\n              },\n            ]\n          : []),\n        ...(hasAppDir && !isClient\n          ? [\n              {\n                issuerLayer: shouldUseReactServerCondition,\n                test: {\n                  // Resolve it if it is a source code file, and it has NOT been\n                  // opted out of bundling.\n                  and: [\n                    aliasCodeConditionTest,\n                    {\n                      not: [optOutBundlingPackageRegex, asyncStoragesRegex],\n                    },\n                  ],\n                },\n                resourceQuery: {\n                  // Do not apply next-flight-loader to imports generated by the\n                  // next-metadata-image-loader, to avoid generating unnecessary\n                  // and conflicting entries in the flight client entry plugin.\n                  // These are already covered by the next-metadata-route-loader\n                  // entries.\n                  not: [\n                    new RegExp(WEBPACK_RESOURCE_QUERIES.metadata),\n                    new RegExp(WEBPACK_RESOURCE_QUERIES.metadataImageMeta),\n                  ],\n                },\n                resolve: {\n                  mainFields: getMainField(compilerType, true),\n                  conditionNames: reactServerCondition,\n                  // If missing the alias override here, the default alias will be used which aliases\n                  // react to the direct file path, not the package name. In that case the condition\n                  // will be ignored completely.\n                  alias: createVendoredReactAliases(bundledReactChannel, {\n                    // No server components profiling\n                    reactProductionProfiling,\n                    layer: WEBPACK_LAYERS.reactServerComponents,\n                    isBrowser: isClient,\n                    isEdgeServer,\n                  }),\n                },\n                use: 'next-flight-loader',\n              },\n            ]\n          : []),\n\n        ...getNextRootParamsRules({\n          isRootParamsEnabled:\n            config.experimental.rootParams ??\n            // `experimental.dynamicIO` implies `experimental.rootParams`.\n            config.experimental.cacheComponents ??\n            false,\n          isClient,\n          appDir,\n          pageExtensions,\n        }),\n\n        // TODO: FIXME: do NOT webpack 5 support with this\n        // x-ref: https://github.com/webpack/webpack/issues/11467\n        ...(!config.experimental.fullySpecified\n          ? [\n              {\n                test: /\\.m?js/,\n                resolve: {\n                  fullySpecified: false,\n                },\n              } as any,\n            ]\n          : []),\n        ...(hasAppDir && isEdgeServer\n          ? [\n              // The Edge bundle includes the server in its entrypoint, so it has to\n              // be in the SSR layer — here we convert the actual page request to\n              // the RSC layer via a webpack rule.\n              {\n                resourceQuery: new RegExp(\n                  WEBPACK_RESOURCE_QUERIES.edgeSSREntry\n                ),\n                layer: WEBPACK_LAYERS.reactServerComponents,\n              },\n            ]\n          : []),\n        ...(hasAppDir\n          ? [\n              {\n                // Alias react-dom for ReactDOM.preload usage.\n                // Alias react for switching between default set and share subset.\n                oneOf: [\n                  {\n                    issuerLayer: shouldUseReactServerCondition,\n                    test: {\n                      // Resolve it if it is a source code file, and it has NOT been\n                      // opted out of bundling.\n                      and: [\n                        aliasCodeConditionTest,\n                        {\n                          not: [optOutBundlingPackageRegex, asyncStoragesRegex],\n                        },\n                      ],\n                    },\n                    resolve: {\n                      // It needs `conditionNames` here to require the proper asset,\n                      // when react is acting as dependency of compiled/react-dom.\n                      alias: createVendoredReactAliases(bundledReactChannel, {\n                        reactProductionProfiling,\n                        layer: WEBPACK_LAYERS.reactServerComponents,\n                        isBrowser: isClient,\n                        isEdgeServer,\n                      }),\n                    },\n                  },\n                  {\n                    test: aliasCodeConditionTest,\n                    issuerLayer: WEBPACK_LAYERS.serverSideRendering,\n                    resolve: {\n                      alias: createVendoredReactAliases(bundledReactChannel, {\n                        reactProductionProfiling,\n                        layer: WEBPACK_LAYERS.serverSideRendering,\n                        isBrowser: isClient,\n                        isEdgeServer,\n                      }),\n                    },\n                  },\n                ],\n              },\n              {\n                test: aliasCodeConditionTest,\n                issuerLayer: WEBPACK_LAYERS.appPagesBrowser,\n                resolve: {\n                  alias: createVendoredReactAliases(bundledReactChannel, {\n                    reactProductionProfiling,\n                    layer: WEBPACK_LAYERS.appPagesBrowser,\n                    isBrowser: isClient,\n                    isEdgeServer,\n                  }),\n                },\n              },\n            ]\n          : []),\n        // Do not apply react-refresh-loader to node_modules for app router browser layer\n        ...(hasAppDir && dev && isClient\n          ? [\n              {\n                test: codeCondition.test,\n                exclude: [\n                  // exclude unchanged modules from react-refresh\n                  codeCondition.exclude,\n                  transpilePackagesRegex,\n                  precompileRegex,\n                ],\n                issuerLayer: WEBPACK_LAYERS.appPagesBrowser,\n                use: reactRefreshLoaders,\n                resolve: {\n                  mainFields: getMainField(compilerType, true),\n                },\n              },\n            ]\n          : []),\n        {\n          oneOf: [\n            {\n              ...codeCondition,\n              issuerLayer: WEBPACK_LAYERS.apiNode,\n              use: apiRoutesLayerLoaders,\n              // In Node.js, switch back to normal URL handling.\n              // We won't bundle `new URL()` cases in Node.js bundler layer.\n              parser: {\n                url: true,\n              },\n            },\n            {\n              ...codeCondition,\n              issuerLayer: WEBPACK_LAYERS.apiEdge,\n              use: apiRoutesLayerLoaders,\n              // In Edge runtime, we leave the url handling by default.\n              // The new URL assets will be converted into edge assets through assets loader.\n            },\n            {\n              test: codeCondition.test,\n              issuerLayer: WEBPACK_LAYERS.middleware,\n              use: middlewareLayerLoaders,\n              resolve: {\n                mainFields: getMainField(compilerType, true),\n                conditionNames: reactServerCondition,\n                alias: createVendoredReactAliases(bundledReactChannel, {\n                  reactProductionProfiling,\n                  layer: WEBPACK_LAYERS.middleware,\n                  isBrowser: isClient,\n                  isEdgeServer,\n                }),\n              },\n            },\n            {\n              test: codeCondition.test,\n              issuerLayer: WEBPACK_LAYERS.instrument,\n              use: instrumentLayerLoaders,\n              resolve: {\n                mainFields: getMainField(compilerType, true),\n                conditionNames: reactServerCondition,\n                alias: createVendoredReactAliases(bundledReactChannel, {\n                  reactProductionProfiling,\n                  layer: WEBPACK_LAYERS.instrument,\n                  isBrowser: isClient,\n                  isEdgeServer,\n                }),\n              },\n            },\n            ...(hasAppDir\n              ? [\n                  {\n                    test: codeCondition.test,\n                    issuerLayer: shouldUseReactServerCondition,\n                    exclude: asyncStoragesRegex,\n                    use: appServerLayerLoaders,\n                  },\n                  {\n                    test: codeCondition.test,\n                    resourceQuery: new RegExp(\n                      WEBPACK_RESOURCE_QUERIES.edgeSSREntry\n                    ),\n                    use: appServerLayerLoaders,\n                  },\n                  {\n                    test: codeCondition.test,\n                    issuerLayer: WEBPACK_LAYERS.appPagesBrowser,\n                    // Exclude the transpilation of the app layer due to compilation issues\n                    exclude: browserNonTranspileModules,\n                    use: appBrowserLayerLoaders,\n                    resolve: {\n                      mainFields: getMainField(compilerType, true),\n                    },\n                  },\n                  {\n                    test: codeCondition.test,\n                    issuerLayer: WEBPACK_LAYERS.serverSideRendering,\n                    exclude: asyncStoragesRegex,\n                    use: appSSRLayerLoaders,\n                    resolve: {\n                      mainFields: getMainField(compilerType, true),\n                    },\n                  },\n                ]\n              : []),\n            {\n              ...codeCondition,\n              use: [\n                ...reactRefreshLoaders,\n                defaultLoaders.babel,\n                reactCompilerLoader,\n              ].filter(Boolean),\n            },\n          ],\n        },\n\n        ...(!config.images.disableStaticImages\n          ? [\n              {\n                test: nextImageLoaderRegex,\n                loader: 'next-image-loader',\n                issuer: { not: regexLikeCss },\n                dependency: { not: ['url'] },\n                resourceQuery: {\n                  not: [\n                    new RegExp(WEBPACK_RESOURCE_QUERIES.metadata),\n                    new RegExp(WEBPACK_RESOURCE_QUERIES.metadataRoute),\n                    new RegExp(WEBPACK_RESOURCE_QUERIES.metadataImageMeta),\n                  ],\n                },\n                options: {\n                  isDev: dev,\n                  compilerType,\n                  basePath: config.basePath,\n                  assetPrefix: config.assetPrefix,\n                },\n              },\n            ]\n          : []),\n        ...(isEdgeServer\n          ? [\n              {\n                resolve: {\n                  fallback: {\n                    process: require.resolve('./polyfills/process'),\n                  },\n                },\n              },\n            ]\n          : isClient\n            ? [\n                {\n                  resolve: {\n                    fallback:\n                      config.experimental.fallbackNodePolyfills === false\n                        ? {\n                            assert: false,\n                            buffer: false,\n                            constants: false,\n                            crypto: false,\n                            domain: false,\n                            http: false,\n                            https: false,\n                            os: false,\n                            path: false,\n                            punycode: false,\n                            process: false,\n                            querystring: false,\n                            stream: false,\n                            string_decoder: false,\n                            sys: false,\n                            timers: false,\n                            tty: false,\n                            util: false,\n                            vm: false,\n                            zlib: false,\n                            events: false,\n                            setImmediate: false,\n                          }\n                        : {\n                            assert: require.resolve(\n                              'next/dist/compiled/assert'\n                            ),\n                            buffer: require.resolve(\n                              'next/dist/compiled/buffer'\n                            ),\n                            constants: require.resolve(\n                              'next/dist/compiled/constants-browserify'\n                            ),\n                            crypto: require.resolve(\n                              'next/dist/compiled/crypto-browserify'\n                            ),\n                            domain: require.resolve(\n                              'next/dist/compiled/domain-browser'\n                            ),\n                            http: require.resolve(\n                              'next/dist/compiled/stream-http'\n                            ),\n                            https: require.resolve(\n                              'next/dist/compiled/https-browserify'\n                            ),\n                            os: require.resolve(\n                              'next/dist/compiled/os-browserify'\n                            ),\n                            path: require.resolve(\n                              'next/dist/compiled/path-browserify'\n                            ),\n                            punycode: require.resolve(\n                              'next/dist/compiled/punycode'\n                            ),\n                            process: require.resolve('./polyfills/process'),\n                            // Handled in separate alias\n                            querystring: require.resolve(\n                              'next/dist/compiled/querystring-es3'\n                            ),\n                            stream: require.resolve(\n                              'next/dist/compiled/stream-browserify'\n                            ),\n                            string_decoder: require.resolve(\n                              'next/dist/compiled/string_decoder'\n                            ),\n                            sys: require.resolve('next/dist/compiled/util'),\n                            timers: require.resolve(\n                              'next/dist/compiled/timers-browserify'\n                            ),\n                            tty: require.resolve(\n                              'next/dist/compiled/tty-browserify'\n                            ),\n                            // Handled in separate alias\n                            // url: require.resolve('url'),\n                            util: require.resolve('next/dist/compiled/util'),\n                            vm: require.resolve(\n                              'next/dist/compiled/vm-browserify'\n                            ),\n                            zlib: require.resolve(\n                              'next/dist/compiled/browserify-zlib'\n                            ),\n                            events: require.resolve(\n                              'next/dist/compiled/events'\n                            ),\n                            setImmediate: require.resolve(\n                              'next/dist/compiled/setimmediate'\n                            ),\n                          },\n                  },\n                },\n              ]\n            : []),\n        {\n          // Mark `image-response.js` as side-effects free to make sure we can\n          // tree-shake it if not used.\n          test: /[\\\\/]next[\\\\/]dist[\\\\/](esm[\\\\/])?server[\\\\/]og[\\\\/]image-response\\.js/,\n          sideEffects: false,\n        },\n        // Mark the action-client-wrapper module as side-effects free to make sure\n        // the individual transformed module of client action can be tree-shaken.\n        // This will make modules processed by `next-flight-server-reference-proxy-loader` become side-effects free,\n        // then on client side the module ids will become tree-shakable.\n        // e.g. the output of client action module will look like:\n        // `export { a } from 'next-flight-server-reference-proxy-loader?id=idOfA&name=a!\n        // `export { b } from 'next-flight-server-reference-proxy-loader?id=idOfB&name=b!\n        {\n          test: /[\\\\/]next[\\\\/]dist[\\\\/](esm[\\\\/])?build[\\\\/]webpack[\\\\/]loaders[\\\\/]next-flight-loader[\\\\/]action-client-wrapper\\.js/,\n          sideEffects: false,\n        },\n        {\n          // This loader rule should be before other rules, as it can output code\n          // that still contains `\"use client\"` or `\"use server\"` statements that\n          // needs to be re-transformed by the RSC compilers.\n          // This loader rule works like a bridge between user's import and\n          // the target module behind a package's barrel file. It reads SWC's\n          // analysis result from the previous loader, and directly returns the\n          // code that only exports values that are asked by the user.\n          test: /__barrel_optimize__/,\n          use: ({ resourceQuery }: { resourceQuery: string }) => {\n            const names = (\n              resourceQuery.match(/\\?names=([^&]+)/)?.[1] || ''\n            ).split(',')\n\n            return [\n              {\n                loader: 'next-barrel-loader',\n                options: {\n                  names,\n                  swcCacheDir: path.join(\n                    dir,\n                    config?.distDir ?? '.next',\n                    'cache',\n                    'swc'\n                  ),\n                },\n                // This is part of the request value to serve as the module key.\n                // The barrel loader are no-op re-exported modules keyed by\n                // export names.\n                ident: 'next-barrel-loader:' + resourceQuery,\n              },\n            ]\n          },\n        },\n        {\n          resolve: {\n            alias: {\n              next: NEXT_PROJECT_ROOT,\n            },\n          },\n        },\n      ],\n    },\n    plugins: [\n      isNodeServer &&\n        new bundler.NormalModuleReplacementPlugin(\n          /\\.\\/(.+)\\.shared-runtime$/,\n          function (resource) {\n            const moduleName = path.basename(\n              resource.request,\n              '.shared-runtime'\n            )\n            const layer = resource.contextInfo.issuerLayer\n            let runtime\n\n            switch (layer) {\n              case WEBPACK_LAYERS.serverSideRendering:\n              case WEBPACK_LAYERS.reactServerComponents:\n              case WEBPACK_LAYERS.appPagesBrowser:\n              case WEBPACK_LAYERS.actionBrowser:\n                runtime = 'app-page'\n                break\n              case null:\n              case undefined:\n              default:\n                runtime = 'pages'\n            }\n            resource.request = `next/dist/server/route-modules/${runtime}/vendored/contexts/${moduleName}`\n          }\n        ),\n      dev && new MemoryWithGcCachePlugin({ maxGenerations: 5 }),\n      dev &&\n        isClient &&\n        (isRspack\n          ? // eslint-disable-next-line\n            new (getRspackReactRefresh() as any)({\n              injectLoader: false,\n              injectEntry: false,\n              overlay: false,\n            })\n          : new ReactRefreshWebpackPlugin(webpack)),\n      // Makes sure `Buffer` and `process` are polyfilled in client and flight bundles (same behavior as webpack 4)\n      (isClient || isEdgeServer) &&\n        new bundler.ProvidePlugin({\n          // Buffer is used by getInlineScriptSource\n          Buffer: [require.resolve('buffer'), 'Buffer'],\n          // Avoid process being overridden when in web run time\n          ...(isClient && { process: [require.resolve('process')] }),\n        }),\n\n      new (getWebpackBundler().DefinePlugin)(\n        getDefineEnv({\n          isTurbopack: false,\n          config,\n          dev,\n          distDir,\n          projectPath: dir,\n          fetchCacheKeyPrefix,\n          hasRewrites,\n          isClient,\n          isEdgeServer,\n          isNodeServer,\n          middlewareMatchers,\n          omitNonDeterministic: isCompileMode,\n          rewrites,\n        })\n      ),\n      isClient &&\n        new ReactLoadablePlugin({\n          filename: REACT_LOADABLE_MANIFEST,\n          pagesDir,\n          appDir,\n          runtimeAsset: `server/${MIDDLEWARE_REACT_LOADABLE_MANIFEST}.js`,\n          dev,\n        }),\n      // rspack doesn't support the parser hooks used here\n      !isRspack && (isClient || isEdgeServer) && new DropClientPage(),\n      isNodeServer &&\n        !dev &&\n        new ((\n          require('./webpack/plugins/next-trace-entrypoints-plugin') as typeof import('./webpack/plugins/next-trace-entrypoints-plugin')\n        )\n          .TraceEntryPointsPlugin as typeof import('./webpack/plugins/next-trace-entrypoints-plugin').TraceEntryPointsPlugin)(\n          {\n            rootDir: dir,\n            appDir: appDir,\n            pagesDir: pagesDir,\n            esmExternals: config.experimental.esmExternals,\n            outputFileTracingRoot: config.outputFileTracingRoot,\n            appDirEnabled: hasAppDir,\n            traceIgnores: [],\n            compilerType,\n          }\n        ),\n      // Moment.js is an extremely popular library that bundles large locale files\n      // by default due to how Webpack interprets its code. This is a practical\n      // solution that requires the user to opt into importing specific locales.\n      // https://github.com/jmblog/how-to-optimize-momentjs-with-webpack\n      config.excludeDefaultMomentLocales &&\n        new bundler.IgnorePlugin({\n          resourceRegExp: /^\\.\\/locale$/,\n          contextRegExp: /moment$/,\n        }),\n      ...(dev\n        ? (() => {\n            // Even though require.cache is server only we have to clear assets from both compilations\n            // This is because the client compilation generates the build manifest that's used on the server side\n            const { NextJsRequireCacheHotReloader } =\n              require('./webpack/plugins/nextjs-require-cache-hot-reloader') as typeof import('./webpack/plugins/nextjs-require-cache-hot-reloader')\n            const devPlugins: any[] = [\n              new NextJsRequireCacheHotReloader({\n                serverComponents: hasAppDir,\n              }),\n            ]\n\n            if (isClient || isEdgeServer) {\n              devPlugins.push(new bundler.HotModuleReplacementPlugin())\n            }\n\n            return devPlugins\n          })()\n        : []),\n      !dev &&\n        new bundler.IgnorePlugin({\n          resourceRegExp: /react-is/,\n          contextRegExp: /next[\\\\/]dist[\\\\/]/,\n        }),\n      isNodeOrEdgeCompilation &&\n        new PagesManifestPlugin({\n          dev,\n          appDirEnabled: hasAppDir,\n          isEdgeRuntime: isEdgeServer,\n          distDir: !dev ? distDir : undefined,\n        }),\n      // MiddlewarePlugin should be after DefinePlugin so NEXT_PUBLIC_*\n      // replacement is done before its process.env.* handling\n      isEdgeServer &&\n        new MiddlewarePlugin({\n          dev,\n          sriEnabled: !dev && !!config.experimental.sri?.algorithm,\n          rewrites,\n          edgeEnvironments: {\n            __NEXT_BUILD_ID: buildId,\n            NEXT_SERVER_ACTIONS_ENCRYPTION_KEY: encryptionKey,\n            __NEXT_PREVIEW_MODE_ID: previewProps.previewModeId,\n            __NEXT_PREVIEW_MODE_SIGNING_KEY: previewProps.previewModeSigningKey,\n            __NEXT_PREVIEW_MODE_ENCRYPTION_KEY:\n              previewProps.previewModeEncryptionKey,\n          },\n        }),\n      isClient &&\n        new BuildManifestPlugin({\n          buildId,\n          rewrites,\n          isDevFallback,\n          appDirEnabled: hasAppDir,\n          clientRouterFilters,\n        }),\n      isRspack\n        ? new RspackProfilingPlugin({ runWebpackSpan })\n        : new ProfilingPlugin({ runWebpackSpan, rootDir: dir }),\n      new WellKnownErrorsPlugin(),\n      isClient &&\n        new CopyFilePlugin({\n          // file path to build output of `@next/polyfill-nomodule`\n          filePath: require.resolve('./polyfills/polyfill-nomodule'),\n          cacheKey: process.env.__NEXT_VERSION as string,\n          name: `static/chunks/polyfills${dev ? '' : '-[hash]'}.js`,\n          minimize: false,\n          info: {\n            [CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL]: 1,\n            // This file is already minified\n            minimized: true,\n          },\n        }),\n      hasAppDir && isClient && new AppBuildManifestPlugin({ dev }),\n      hasAppDir &&\n        (isClient\n          ? new ClientReferenceManifestPlugin({\n              dev,\n              appDir,\n              experimentalInlineCss: !!config.experimental.inlineCss,\n            })\n          : new FlightClientEntryPlugin({\n              appDir,\n              dev,\n              isEdgeServer,\n              encryptionKey,\n            })),\n      hasAppDir &&\n        !isClient &&\n        new NextTypesPlugin({\n          dir,\n          distDir: config.distDir,\n          appDir,\n          dev,\n          isEdgeServer,\n          pageExtensions: config.pageExtensions,\n          cacheLifeConfig: config.experimental.cacheLife,\n          originalRewrites,\n          originalRedirects,\n        }),\n      !dev &&\n        isClient &&\n        !!config.experimental.sri?.algorithm &&\n        new SubresourceIntegrityPlugin(config.experimental.sri.algorithm),\n      isClient &&\n        new NextFontManifestPlugin({\n          appDir,\n        }),\n      !dev &&\n        isClient &&\n        config.experimental.cssChunking &&\n        (isRspack\n          ? new (getRspackCore().experiments.CssChunkingPlugin)({\n              strict: config.experimental.cssChunking === 'strict',\n              nextjs: true,\n            })\n          : new CssChunkingPlugin(\n              config.experimental.cssChunking === 'strict'\n            )),\n      telemetryPlugin,\n      !dev &&\n        isNodeServer &&\n        new (\n          require('./webpack/plugins/telemetry-plugin/telemetry-plugin') as typeof import('./webpack/plugins/telemetry-plugin/telemetry-plugin')\n        ).TelemetryPlugin(new Map()),\n      shouldEnableSlowModuleDetection &&\n        new (\n          require('./webpack/plugins/slow-module-detection-plugin') as typeof import('./webpack/plugins/slow-module-detection-plugin')\n        ).default({\n          compilerType,\n          ...config.experimental.slowModuleDetection!,\n        }),\n    ].filter(Boolean as any as ExcludesFalse),\n  }\n\n  // Support tsconfig and jsconfig baseUrl\n  // Only add the baseUrl if it's explicitly set in tsconfig/jsconfig\n  if (resolvedBaseUrl && !resolvedBaseUrl.isImplicit) {\n    webpackConfig.resolve?.modules?.push(resolvedBaseUrl.baseUrl)\n  }\n\n  // always add JsConfigPathsPlugin to allow hot-reloading\n  // if the config is added/removed\n  webpackConfig.resolve?.plugins?.unshift(\n    new JsConfigPathsPlugin(\n      jsConfig?.compilerOptions?.paths || {},\n      resolvedBaseUrl\n    )\n  )\n\n  const webpack5Config = webpackConfig as webpack.Configuration\n\n  if (isEdgeServer) {\n    webpack5Config.module?.rules?.unshift({\n      test: /\\.wasm$/,\n      loader: 'next-middleware-wasm-loader',\n      type: 'javascript/auto',\n      resourceQuery: /module/i,\n    })\n    webpack5Config.module?.rules?.unshift({\n      dependency: 'url',\n      loader: 'next-middleware-asset-loader',\n      type: 'javascript/auto',\n      layer: WEBPACK_LAYERS.edgeAsset,\n    })\n    webpack5Config.module?.rules?.unshift({\n      issuerLayer: WEBPACK_LAYERS.edgeAsset,\n      type: 'asset/source',\n    })\n  }\n\n  webpack5Config.experiments = {\n    layers: true,\n    cacheUnaffected: true,\n    buildHttp: Array.isArray(config.experimental.urlImports)\n      ? {\n          allowedUris: config.experimental.urlImports,\n          cacheLocation: path.join(dir, 'next.lock/data'),\n          lockfileLocation: path.join(dir, 'next.lock/lock.json'),\n        }\n      : config.experimental.urlImports\n        ? {\n            cacheLocation: path.join(dir, 'next.lock/data'),\n            lockfileLocation: path.join(dir, 'next.lock/lock.json'),\n            ...config.experimental.urlImports,\n          }\n        : undefined,\n  }\n\n  webpack5Config.module!.parser = {\n    javascript: {\n      url: 'relative',\n    },\n  }\n  webpack5Config.module!.generator = {\n    asset: {\n      filename: 'static/media/[name].[hash:8][ext]',\n    },\n  }\n\n  if (!webpack5Config.output) {\n    webpack5Config.output = {}\n  }\n  if (isClient) {\n    webpack5Config.output.trustedTypes = 'nextjs#bundler'\n  }\n\n  if (isClient || isEdgeServer) {\n    webpack5Config.output.enabledLibraryTypes = ['assign']\n  }\n\n  // This enables managedPaths for all node_modules\n  // and also for the unplugged folder when using yarn pnp\n  // It also add the yarn cache to the immutable paths\n  webpack5Config.snapshot = {}\n  if (process.versions.pnp === '3') {\n    webpack5Config.snapshot.managedPaths = [\n      /^(.+?(?:[\\\\/]\\.yarn[\\\\/]unplugged[\\\\/][^\\\\/]+)?[\\\\/]node_modules[\\\\/])/,\n    ]\n  } else {\n    webpack5Config.snapshot.managedPaths = [/^(.+?[\\\\/]node_modules[\\\\/])/]\n  }\n  if (process.versions.pnp === '3') {\n    webpack5Config.snapshot.immutablePaths = [\n      /^(.+?[\\\\/]cache[\\\\/][^\\\\/]+\\.zip[\\\\/]node_modules[\\\\/])/,\n    ]\n  }\n\n  if (dev) {\n    if (!webpack5Config.optimization) {\n      webpack5Config.optimization = {}\n    }\n\n    // For Server Components, it's necessary to have provided exports collected\n    // to generate the correct flight manifest.\n    if (!hasAppDir) {\n      webpack5Config.optimization.providedExports = false\n    }\n    webpack5Config.optimization.usedExports = false\n  }\n\n  const configVars = JSON.stringify({\n    optimizePackageImports: config?.experimental?.optimizePackageImports,\n    crossOrigin: config.crossOrigin,\n    pageExtensions: pageExtensions,\n    trailingSlash: config.trailingSlash,\n    buildActivityPosition:\n      config.devIndicators === false\n        ? undefined\n        : config.devIndicators.position,\n    productionBrowserSourceMaps: !!config.productionBrowserSourceMaps,\n    reactStrictMode: config.reactStrictMode,\n    optimizeCss: config.experimental.optimizeCss,\n    nextScriptWorkers: config.experimental.nextScriptWorkers,\n    scrollRestoration: config.experimental.scrollRestoration,\n    basePath: config.basePath,\n    excludeDefaultMomentLocales: config.excludeDefaultMomentLocales,\n    assetPrefix: config.assetPrefix,\n    disableOptimizedLoading,\n    isEdgeRuntime: isEdgeServer,\n    reactProductionProfiling,\n    webpack: !!config.webpack,\n    hasRewrites,\n    swcLoader: useSWCLoader,\n    removeConsole: config.compiler?.removeConsole,\n    reactRemoveProperties: config.compiler?.reactRemoveProperties,\n    styledComponents: config.compiler?.styledComponents,\n    relay: config.compiler?.relay,\n    emotion: config.compiler?.emotion,\n    modularizeImports: config.modularizeImports,\n    imageLoaderFile: config.images.loaderFile,\n    clientTraceMetadata: config.experimental.clientTraceMetadata,\n    serverSourceMaps: config.experimental.serverSourceMaps,\n    serverReferenceHashSalt: encryptionKey,\n  })\n\n  const cache: any = {\n    type: 'filesystem',\n    // Disable memory cache in development in favor of our own MemoryWithGcCachePlugin.\n    maxMemoryGenerations: dev ? 0 : Infinity, // Infinity is default value for production in webpack currently.\n    // Includes:\n    //  - Next.js location on disk (some loaders use absolute paths and some resolve options depend on absolute paths)\n    //  - Next.js version\n    //  - next.config.js keys that affect compilation\n    version: `${__dirname}|${process.env.__NEXT_VERSION}|${configVars}`,\n    cacheDirectory: path.join(distDir, 'cache', 'webpack'),\n    // For production builds, it's more efficient to compress all cache files together instead of compression each one individually.\n    // So we disable compression here and allow the build runner to take care of compressing the cache as a whole.\n    // For local development, we still want to compress the cache files individually to avoid I/O bottlenecks\n    // as we are seeing 1~10 seconds of fs I/O time from user reports.\n    compression: dev ? 'gzip' : false,\n  }\n\n  // Adds `next.config.js` as a buildDependency when custom webpack config is provided\n  if (config.webpack && config.configFile) {\n    cache.buildDependencies = {\n      config: [config.configFile],\n      // We don't want to use the webpack default buildDependencies as we already include the next.js version\n      defaultWebpack: [],\n    }\n  } else {\n    cache.buildDependencies = {\n      // We don't want to use the webpack default buildDependencies as we already include the next.js version\n      defaultWebpack: [],\n    }\n  }\n  webpack5Config.plugins?.push((compiler) => {\n    compiler.hooks.done.tap('next-build-dependencies', (stats) => {\n      const buildDependencies = stats.compilation.buildDependencies\n      const nextPackage = path.dirname(require.resolve('next/package.json'))\n      // Remove all next.js build dependencies, they are already covered by the cacheVersion\n      // and next.js also imports the output files which leads to broken caching.\n      for (const dep of buildDependencies) {\n        if (dep.startsWith(nextPackage)) {\n          buildDependencies.delete(dep)\n        }\n      }\n    })\n  })\n\n  webpack5Config.cache = cache\n\n  if (process.env.NEXT_WEBPACK_LOGGING) {\n    const infra = process.env.NEXT_WEBPACK_LOGGING.includes('infrastructure')\n    const profileClient =\n      process.env.NEXT_WEBPACK_LOGGING.includes('profile-client')\n    const profileServer =\n      process.env.NEXT_WEBPACK_LOGGING.includes('profile-server')\n    const summaryClient =\n      process.env.NEXT_WEBPACK_LOGGING.includes('summary-client')\n    const summaryServer =\n      process.env.NEXT_WEBPACK_LOGGING.includes('summary-server')\n\n    const profile =\n      (profileClient && isClient) || (profileServer && isNodeOrEdgeCompilation)\n    const summary =\n      (summaryClient && isClient) || (summaryServer && isNodeOrEdgeCompilation)\n\n    const logDefault = !infra && !profile && !summary\n\n    if (logDefault || infra) {\n      webpack5Config.infrastructureLogging = {\n        level: 'verbose',\n        debug: /FileSystemInfo/,\n      }\n    }\n\n    if (logDefault || profile) {\n      webpack5Config.plugins!.push((compiler: webpack.Compiler) => {\n        compiler.hooks.done.tap('next-webpack-logging', (stats) => {\n          console.log(\n            stats.toString({\n              colors: true,\n              logging: logDefault ? 'log' : 'verbose',\n            })\n          )\n        })\n      })\n    } else if (summary) {\n      webpack5Config.plugins!.push((compiler: webpack.Compiler) => {\n        compiler.hooks.done.tap('next-webpack-logging', (stats) => {\n          console.log(\n            stats.toString({\n              preset: 'summary',\n              colors: true,\n              timings: true,\n            })\n          )\n        })\n      })\n    }\n\n    if (profile) {\n      const ProgressPlugin =\n        webpack.ProgressPlugin as unknown as typeof webpack.ProgressPlugin\n      webpack5Config.plugins!.push(\n        new ProgressPlugin({\n          profile: true,\n        })\n      )\n      webpack5Config.profile = true\n    }\n  }\n\n  webpackConfig = await buildConfiguration(webpackConfig, {\n    supportedBrowsers,\n    rootDirectory: dir,\n    customAppFile: pagesDir\n      ? new RegExp(escapeStringRegexp(path.join(pagesDir, `_app`)))\n      : undefined,\n    hasAppDir,\n    isDevelopment: dev,\n    isServer: isNodeOrEdgeCompilation,\n    isEdgeRuntime: isEdgeServer,\n    targetWeb: isClient || isEdgeServer,\n    assetPrefix: config.assetPrefix || '',\n    deploymentId: config.deploymentId,\n    sassOptions: config.sassOptions,\n    productionBrowserSourceMaps: config.productionBrowserSourceMaps,\n    future: config.future,\n    experimental: config.experimental,\n    disableStaticImages: config.images.disableStaticImages,\n    transpilePackages: config.transpilePackages,\n    serverSourceMaps: config.experimental.serverSourceMaps,\n  })\n\n  // @ts-ignore Cache exists\n  webpackConfig.cache.name = `${webpackConfig.name}-${webpackConfig.mode}${\n    isDevFallback ? '-fallback' : ''\n  }`\n\n  if (dev) {\n    if (webpackConfig.module) {\n      webpackConfig.module.unsafeCache = (module: any) =>\n        !UNSAFE_CACHE_REGEX.test(module.resource)\n    } else {\n      webpackConfig.module = {\n        unsafeCache: (module: any) => !UNSAFE_CACHE_REGEX.test(module.resource),\n      }\n    }\n  }\n\n  let originalDevtool = webpackConfig.devtool\n  if (typeof config.webpack === 'function') {\n    const pluginCountBefore = webpackConfig.plugins?.length\n\n    webpackConfig = config.webpack(webpackConfig, {\n      dir,\n      dev,\n      isServer: isNodeOrEdgeCompilation,\n      buildId,\n      config,\n      defaultLoaders,\n      totalPages: Object.keys(entrypoints).length,\n      webpack: bundler,\n      ...(isNodeOrEdgeCompilation\n        ? {\n            nextRuntime: isEdgeServer ? 'edge' : 'nodejs',\n          }\n        : {}),\n    })\n\n    if (telemetryPlugin && pluginCountBefore) {\n      const pluginCountAfter = webpackConfig.plugins?.length\n      if (pluginCountAfter) {\n        const pluginsChanged = pluginCountAfter !== pluginCountBefore\n        telemetryPlugin.addUsage('webpackPlugins', pluginsChanged ? 1 : 0)\n      }\n    }\n\n    if (!webpackConfig) {\n      throw new Error(\n        `Webpack config is undefined. You may have forgot to return properly from within the \"webpack\" method of your ${config.configFileName}.\\n` +\n          'See more info here https://nextjs.org/docs/messages/undefined-webpack-config'\n      )\n    }\n\n    if (dev && originalDevtool !== webpackConfig.devtool) {\n      webpackConfig.devtool = originalDevtool\n      devtoolRevertWarning(originalDevtool)\n    }\n\n    // eslint-disable-next-line @typescript-eslint/no-shadow\n    const webpack5Config = webpackConfig as webpack.Configuration\n\n    // disable lazy compilation of entries as next.js has it's own method here\n    if (webpack5Config.experiments?.lazyCompilation === true) {\n      webpack5Config.experiments.lazyCompilation = {\n        entries: false,\n      }\n    } else if (\n      typeof webpack5Config.experiments?.lazyCompilation === 'object' &&\n      webpack5Config.experiments.lazyCompilation.entries !== false\n    ) {\n      webpack5Config.experiments.lazyCompilation.entries = false\n    }\n\n    if (typeof (webpackConfig as any).then === 'function') {\n      console.warn(\n        '> Promise returned in next config. https://nextjs.org/docs/messages/promise-in-next-config'\n      )\n    }\n  }\n  const rules = webpackConfig.module?.rules || []\n\n  const customSvgRule = rules.find(\n    (rule): rule is webpack.RuleSetRule =>\n      (rule &&\n        typeof rule === 'object' &&\n        rule.loader !== 'next-image-loader' &&\n        'test' in rule &&\n        rule.test instanceof RegExp &&\n        rule.test.test('.svg')) ||\n      false\n  )\n\n  if (customSvgRule && hasAppDir) {\n    // Create React aliases for SVG components that were transformed using a\n    // custom webpack config with e.g. the `@svgr/webpack` loader, or the\n    // `babel-plugin-inline-react-svg` plugin.\n    rules.push({\n      test: customSvgRule.test,\n      oneOf: [\n        WEBPACK_LAYERS.reactServerComponents,\n        WEBPACK_LAYERS.serverSideRendering,\n        WEBPACK_LAYERS.appPagesBrowser,\n      ].map((layer) => ({\n        issuerLayer: layer,\n        resolve: {\n          alias: createVendoredReactAliases(bundledReactChannel, {\n            reactProductionProfiling,\n            layer,\n            isBrowser: isClient,\n            isEdgeServer,\n          }),\n        },\n      })),\n    })\n  }\n\n  if (!config.images.disableStaticImages) {\n    const nextImageRule = rules.find(\n      (rule) =>\n        rule && typeof rule === 'object' && rule.loader === 'next-image-loader'\n    )\n    if (customSvgRule && nextImageRule && typeof nextImageRule === 'object') {\n      // Exclude svg if the user already defined it in custom\n      // webpack config such as the `@svgr/webpack` loader, or\n      // the `babel-plugin-inline-react-svg` plugin.\n      nextImageRule.test = /\\.(png|jpg|jpeg|gif|webp|avif|ico|bmp)$/i\n    }\n  }\n\n  if (\n    config.experimental.craCompat &&\n    webpackConfig.module?.rules &&\n    webpackConfig.plugins\n  ) {\n    // CRA allows importing non-webpack handled files with file-loader\n    // these need to be the last rule to prevent catching other items\n    // https://github.com/facebook/create-react-app/blob/fddce8a9e21bf68f37054586deb0c8636a45f50b/packages/react-scripts/config/webpack.config.js#L594\n    const fileLoaderExclude = [/\\.(js|mjs|jsx|ts|tsx|json)$/]\n    const fileLoader = {\n      exclude: fileLoaderExclude,\n      issuer: fileLoaderExclude,\n      type: 'asset/resource',\n    }\n\n    const topRules = []\n    const innerRules = []\n\n    for (const rule of webpackConfig.module.rules) {\n      if (!rule || typeof rule !== 'object') continue\n      if (rule.resolve) {\n        topRules.push(rule)\n      } else {\n        if (\n          rule.oneOf &&\n          !(rule.test || rule.exclude || rule.resource || rule.issuer)\n        ) {\n          rule.oneOf.forEach((r) => innerRules.push(r))\n        } else {\n          innerRules.push(rule)\n        }\n      }\n    }\n\n    webpackConfig.module.rules = [\n      ...(topRules as any),\n      {\n        oneOf: [...innerRules, fileLoader],\n      },\n    ]\n  }\n\n  // Backwards compat with webpack-dev-middleware options object\n  if (typeof config.webpackDevMiddleware === 'function') {\n    const options = config.webpackDevMiddleware({\n      watchOptions: webpackConfig.watchOptions,\n    })\n    if (options.watchOptions) {\n      webpackConfig.watchOptions = options.watchOptions\n    }\n  }\n\n  function canMatchCss(rule: webpack.RuleSetCondition | undefined): boolean {\n    if (!rule) {\n      return false\n    }\n\n    const fileNames = [\n      '/tmp/NEXTJS_CSS_DETECTION_FILE.css',\n      '/tmp/NEXTJS_CSS_DETECTION_FILE.scss',\n      '/tmp/NEXTJS_CSS_DETECTION_FILE.sass',\n      '/tmp/NEXTJS_CSS_DETECTION_FILE.less',\n      '/tmp/NEXTJS_CSS_DETECTION_FILE.styl',\n    ]\n\n    if (rule instanceof RegExp && fileNames.some((input) => rule.test(input))) {\n      return true\n    }\n\n    if (typeof rule === 'function') {\n      if (\n        fileNames.some((input) => {\n          try {\n            if (rule(input)) {\n              return true\n            }\n          } catch {}\n          return false\n        })\n      ) {\n        return true\n      }\n    }\n\n    if (Array.isArray(rule) && rule.some(canMatchCss)) {\n      return true\n    }\n\n    return false\n  }\n\n  const hasUserCssConfig =\n    webpackConfig.module?.rules?.some(\n      (rule: any) => canMatchCss(rule.test) || canMatchCss(rule.include)\n    ) ?? false\n\n  if (hasUserCssConfig) {\n    // only show warning for one build\n    if (isNodeOrEdgeCompilation) {\n      console.warn(\n        yellow(bold('Warning: ')) +\n          bold(\n            'Built-in CSS support is being disabled due to custom CSS configuration being detected.\\n'\n          ) +\n          'See here for more info: https://nextjs.org/docs/messages/built-in-css-disabled\\n'\n      )\n    }\n\n    if (webpackConfig.module?.rules?.length) {\n      // Remove default CSS Loaders\n      webpackConfig.module.rules.forEach((r) => {\n        if (!r || typeof r !== 'object') return\n        if (Array.isArray(r.oneOf)) {\n          r.oneOf = r.oneOf.filter(\n            (o) => (o as any)[Symbol.for('__next_css_remove')] !== true\n          )\n        }\n      })\n    }\n    if (webpackConfig.plugins?.length) {\n      // Disable CSS Extraction Plugin\n      webpackConfig.plugins = webpackConfig.plugins.filter(\n        (p) => (p as any).__next_css_remove !== true\n      )\n    }\n    if (webpackConfig.optimization?.minimizer?.length) {\n      // Disable CSS Minifier\n      webpackConfig.optimization.minimizer =\n        webpackConfig.optimization.minimizer.filter(\n          (e) => (e as any).__next_css_remove !== true\n        )\n    }\n  }\n\n  // Inject missing React Refresh loaders so that development mode is fast:\n  if (dev && isClient) {\n    attachReactRefresh(webpackConfig, defaultLoaders.babel)\n  }\n\n  // Backwards compat for `main.js` entry key\n  // and setup of dependencies between entries\n  // we can't do that in the initial entry for\n  // backward-compat reasons\n  const originalEntry: any = webpackConfig.entry\n  if (typeof originalEntry !== 'undefined') {\n    const updatedEntry = async () => {\n      const entry: webpack.EntryObject =\n        typeof originalEntry === 'function'\n          ? await originalEntry()\n          : originalEntry\n      // Server compilation doesn't have main.js\n      if (\n        clientEntries &&\n        Array.isArray(entry['main.js']) &&\n        entry['main.js'].length > 0\n      ) {\n        const originalFile = clientEntries[\n          CLIENT_STATIC_FILES_RUNTIME_MAIN\n        ] as string\n        entry[CLIENT_STATIC_FILES_RUNTIME_MAIN] = [\n          ...entry['main.js'],\n          originalFile,\n        ]\n      }\n      delete entry['main.js']\n\n      for (const name of Object.keys(entry)) {\n        entry[name] = finalizeEntrypoint({\n          value: entry[name],\n          compilerType,\n          name,\n          hasAppDir,\n        })\n      }\n\n      return entry\n    }\n    // @ts-ignore webpack 5 typings needed\n    webpackConfig.entry = updatedEntry\n  }\n\n  if (!dev && typeof webpackConfig.entry === 'function') {\n    // entry is always a function\n    webpackConfig.entry = await webpackConfig.entry()\n  }\n\n  return webpackConfig\n}\n\nfunction getNextRootParamsRules({\n  isRootParamsEnabled,\n  isClient,\n  appDir,\n  pageExtensions,\n}: {\n  isRootParamsEnabled: boolean\n  isClient: boolean\n  appDir: string | undefined\n  pageExtensions: string[]\n}): webpack.RuleSetRule[] {\n  // Match resolved import of 'next/root-params'\n  const nextRootParamsModule = path.join(NEXT_PROJECT_ROOT, 'root-params.js')\n\n  const createInvalidImportRule = (message: string) => {\n    return {\n      resource: nextRootParamsModule,\n      loader: 'next-invalid-import-error-loader',\n      options: {\n        message,\n      } satisfies InvalidImportLoaderOpts,\n    } satisfies webpack.RuleSetRule\n  }\n\n  // Hard-error if the flag is not enabled, regardless of if we're on the server or on the client.\n  if (!isRootParamsEnabled) {\n    return [\n      createInvalidImportRule(\n        \"'next/root-params' can only be imported when `experimental.rootParams` is enabled.\"\n      ),\n    ]\n  }\n\n  // If there's no app-dir (and thus no layouts), there's no sensible way to use 'next/root-params',\n  // because we wouldn't generate any getters.\n  if (!appDir) {\n    return [\n      createInvalidImportRule(\n        \"'next/root-params' can only be used with the App Directory.\"\n      ),\n    ]\n  }\n\n  // In general, the compiler should prevent importing 'next/root-params' from client modules, but it doesn't catch everything.\n  // If an import slips through our validation, make it error.\n  const invalidClientImportRule = createInvalidImportRule(\n    \"'next/root-params' cannot be imported from a Client Component module. It should only be used from a Server Component.\"\n  )\n\n  // in the browser compilation we can skip the server rules, because we know all imports will be invalid.\n  if (isClient) {\n    return [invalidClientImportRule]\n  }\n\n  return [\n    {\n      oneOf: [\n        {\n          resource: nextRootParamsModule,\n          issuerLayer: shouldUseReactServerCondition as (\n            layer: string\n          ) => boolean,\n          loader: 'next-root-params-loader',\n          options: {\n            appDir,\n            pageExtensions,\n          } satisfies RootParamsLoaderOpts,\n        },\n        // if the rule above didn't match, we're in the SSR layer (or something else that isn't server-only).\n        invalidClientImportRule,\n      ],\n    },\n  ]\n}\n"], "names": ["React", "ReactRefreshWebpackPlugin", "yellow", "bold", "crypto", "webpack", "path", "getDefineEnv", "escapeStringRegexp", "WEBPACK_LAYERS", "WEBPACK_RESOURCE_QUERIES", "isWebpackBundledLayer", "isWebpackClientOnlyLayer", "shouldUseReactServerCondition", "isWebpackDefaultLayer", "RSPACK_DEFAULT_LAYERS_REGEX", "CLIENT_STATIC_FILES_RUNTIME_AMP", "CLIENT_STATIC_FILES_RUNTIME_MAIN", "CLIENT_STATIC_FILES_RUNTIME_MAIN_APP", "CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL", "CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH", "CLIENT_STATIC_FILES_RUNTIME_WEBPACK", "MIDDLEWARE_REACT_LOADABLE_MANIFEST", "REACT_LOADABLE_MANIFEST", "SERVER_DIRECTORY", "COMPILER_NAMES", "execOnce", "finalizeEntrypoint", "Log", "buildConfiguration", "MiddlewarePlugin", "getEdgePolyfilledModules", "handleWebpackExternalForEdgeRuntime", "BuildManifestPlugin", "JsConfigPathsPlugin", "DropClientPage", "PagesManifestPlugin", "Profiling<PERSON><PERSON><PERSON>", "ReactLoadablePlugin", "WellKnownErrorsPlugin", "regexLikeCss", "CopyFilePlugin", "ClientReferenceManifestPlugin", "FlightClientEntryPlugin", "NextFlightClientEntryPlugin", "RspackFlightClientEntryPlugin", "NextTypesPlugin", "loadJsConfig", "loadBindings", "AppBuildManifestPlugin", "SubresourceIntegrityPlugin", "NextFontManifestPlugin", "getSupportedBrowsers", "MemoryWithGcCachePlugin", "getBabelConfigFile", "needsExperimentalReact", "isResourceInPackages", "makeExternalHandler", "getMainField", "edgeConditionNames", "OptionalPeerDependencyResolverPlugin", "createWebpackAliases", "createServerOnlyClientOnlyAliases", "createVendoredReactAliases", "createNextApiEsmAliases", "createAppRouterApiAliases", "hasCustomExportOutput", "CssChunkingPlugin", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getReactCompilerLoader", "NEXT_PROJECT_ROOT", "NEXT_PROJECT_ROOT_DIST_CLIENT", "getRspackCore", "getRspackReactRefresh", "RspackProfilingPlugin", "getWebpackBundler", "EXTERNAL_PACKAGES", "require", "DEFAULT_TRANSPILED_PACKAGES", "parseInt", "version", "Error", "babelIncludeRegexes", "browserNonTranspileModules", "precompileRegex", "asyncStoragesRegex", "nodePathList", "process", "env", "NODE_PATH", "split", "platform", "filter", "p", "baseWatchOptions", "Object", "freeze", "aggregateTimeout", "ignored", "isModuleCSS", "module", "type", "devtoolRevertWarning", "devtool", "console", "warn", "loggedSwcDisabled", "loggedIgnoredCompilerOptions", "reactRefreshLoaderName", "getReactRefreshLoader", "NEXT_RSPACK", "loader", "resolve", "attachReactRefresh", "webpackConfig", "target<PERSON><PERSON><PERSON>", "reactRefreshLoader", "rules", "for<PERSON>ach", "rule", "curr", "use", "Array", "isArray", "some", "r", "idx", "findIndex", "splice", "NODE_RESOLVE_OPTIONS", "dependencyType", "modules", "fallback", "exportsFields", "importsFields", "conditionNames", "descriptionFiles", "extensions", "enforceExtensions", "symlinks", "mainFields", "mainFiles", "roots", "fullySpecified", "preferRelative", "preferAbsolute", "restrictions", "NODE_BASE_RESOLVE_OPTIONS", "alias", "NODE_ESM_RESOLVE_OPTIONS", "NODE_BASE_ESM_RESOLVE_OPTIONS", "nextImageLoaderRegex", "loadProjectInfo", "dir", "config", "dev", "jsConfig", "jsConfigPath", "resolvedBaseUrl", "supportedBrowsers", "hasExternalOtelApiPackage", "UNSAFE_CACHE_REGEX", "getCacheDirectories", "configs", "Set", "map", "cfg", "cache", "cacheDirectory", "getBaseWebpackConfig", "buildId", "<PERSON><PERSON><PERSON>", "compilerType", "entrypoints", "isDev<PERSON><PERSON><PERSON>", "pagesDir", "reactProductionProfiling", "rewrites", "originalRewrites", "originalRedirects", "runWebpackSpan", "appDir", "middlewareMatchers", "noMangling", "clientRouterFilters", "fetchCacheKeyPrefix", "isCompileMode", "previewProps", "webpack5Config", "bundler", "isClient", "client", "isEdgeServer", "edgeServer", "isNodeServer", "server", "isRspack", "Boolean", "BUILTIN_FLIGHT_CLIENT_ENTRY_PLUGIN", "isNodeOrEdgeCompilation", "hasRewrites", "beforeFiles", "length", "afterFiles", "hasAppDir", "disableOptimizedLoading", "bundledReactChannel", "babelConfigFile", "distDir", "join", "useSWCLoader", "experimental", "forceSwcTransforms", "SWCBinaryTarget", "undefined", "binaryTarget", "getBinaryMetadata", "target", "info", "relative", "useWasmBinary", "finalTranspilePackages", "transpilePackages", "concat", "pkg", "optimizePackageImports", "includes", "push", "compiler", "excludeCache", "exclude", "excludePath", "cached", "shouldExclude", "test", "shouldIncludeExternalDirs", "externalDir", "codeCondition", "or", "include", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reactCompiler", "reactCompilerLoader", "swcTraceProfilingInitialized", "getSwcLoader", "extraOptions", "swcTraceProfiling", "initCustomTraceSubscriber", "Date", "now", "useBuiltinSwcLoader", "BUILTIN_SWC_LOADER", "options", "isServer", "rootDir", "hasReactRefresh", "swcCacheDir", "serverReferenceHashSalt", "pnp", "versions", "optimizeServerReact", "modularizeImports", "decorators", "compilerOptions", "experimentalDecorators", "emitDecoratorMetadata", "regeneratorRuntimePath", "nextConfig", "swcServerLayerLoader", "serverComponents", "bundleLayer", "reactServerComponents", "esm", "swcSS<PERSON>ayer<PERSON><PERSON>der", "serverSideRendering", "swcBrowser<PERSON><PERSON><PERSON><PERSON><PERSON>der", "appPagesBrowser", "swcDefaultLoader", "defaultLoaders", "babel", "appServerLayerLoaders", "instrumentLayerLoaders", "middlewareLayerLoaders", "middleware", "reactRefreshLoaders", "createClientLayerLoader", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reactRefresh", "appBrowserLayerLoaders", "appSSRLayerLoaders", "apiRoutesLayerLoaders", "apiNode", "pageExtensions", "outputPath", "reactServerCondition", "reactRefreshEntry", "entry", "clientEntries", "replace", "resolveConfig", "extensionAlias", "plugins", "tsConfig", "configFile", "<PERSON><PERSON><PERSON>eworkP<PERSON><PERSON>", "topLevelFrameworkPaths", "visitedFrameworkPackages", "addPackagePath", "packageName", "relativeToPath", "paths", "has", "add", "packageJsonPath", "directory", "dependencies", "name", "keys", "_", "crossOrigin", "serverExternalPackages", "externalPackageConflicts", "optOutBundlingPackages", "optOutBundlingPackageRegex", "RegExp", "transpilePackagesRegex", "handleExternals", "transpiledPackages", "pageExtensionsRegex", "aliasCodeConditionTest", "builtinModules", "bunExtern<PERSON>", "shouldEnableSlowModuleDetection", "slowModuleDetection", "getParallelism", "override", "Number", "NEXT_WEBPACK_PARALLELISM", "telemetryPlugin", "TelemetryPlugin", "Map", "relay", "styledComponents", "reactRemoveProperties", "removeConsole", "jsxImportSource", "emotion", "skipMiddlewareUrlNormalize", "skipTrailingSlashRedirect", "esmExternals", "parallelism", "externalsPresets", "node", "externals", "context", "request", "contextInfo", "getResolve", "issuer<PERSON><PERSON>er", "resolveFunction", "resolveContext", "requestToResolve", "Promise", "reject", "err", "result", "resolveData", "isEsm", "descriptionFileData", "optimization", "emitOnErrors", "checkWasmTypes", "nodeEnv", "splitChunks", "extractRootNodeModule", "modulePath", "regex", "match", "cacheGroups", "vendor", "chunks", "reuseExistingChunk", "minSize", "minChunks", "maxAsyncRequests", "maxInitialRequests", "moduleId", "nameForCondition", "rootModule", "hash", "createHash", "update", "digest", "default", "defaultVendors", "filename", "frameworkCacheGroup", "layer", "resource", "pkgPath", "startsWith", "priority", "enforce", "libCacheGroup", "size", "updateHash", "libIdent", "substring", "chunk", "framework", "lib", "runtimeChunk", "minimize", "serverMinification", "minimizer", "SwcJsMinimizerRspackPlugin", "minimizerOptions", "compress", "inline", "global_defs", "mangle", "reserved", "disableCharFreq", "MinifyPlugin", "apply", "__NEXT_TEST_MODE", "useLightningcss", "LightningCssMinimizerRspackPlugin", "targets", "CssMinimizerPlugin", "postcssOptions", "annotation", "watchOptions", "poll", "pollIntervalMs", "output", "publicPath", "assetPrefix", "endsWith", "slice", "library", "libraryTarget", "hotUpdateChunkFilename", "hotUpdateMainFilename", "chunkFilename", "strictModuleExceptionHandling", "crossOriginLoading", "devtoolModuleFilenameTemplate", "webassemblyModuleFilename", "hashFunction", "hashDigestLength", "performance", "<PERSON><PERSON><PERSON><PERSON>", "reduce", "__dirname", "GROUP", "serverOnly", "neutralTarget", "not", "message", "shared", "resourceQuery", "metadataRoute", "and", "metadata", "metadataImageMeta", "<PERSON><PERSON><PERSON><PERSON>", "getNextRootParamsRules", "isRootParamsEnabled", "rootParams", "cacheComponents", "edgeSSREntry", "oneOf", "parser", "url", "apiEdge", "instrument", "images", "disableStaticImages", "issuer", "dependency", "isDev", "basePath", "fallbackNodePolyfills", "assert", "buffer", "constants", "domain", "http", "https", "os", "punycode", "querystring", "stream", "string_decoder", "sys", "timers", "tty", "util", "vm", "zlib", "events", "setImmediate", "sideEffects", "names", "ident", "next", "NormalModuleReplacementPlugin", "moduleName", "basename", "runtime", "<PERSON><PERSON><PERSON><PERSON>", "maxGenerations", "injectLoader", "injectEntry", "overlay", "ProvidePlugin", "<PERSON><PERSON><PERSON>", "DefinePlugin", "isTurbopack", "projectPath", "omitNonDeterministic", "runtimeAsset", "TraceEntryPointsPlugin", "outputFileTracingRoot", "appDirEnabled", "traceIgnores", "excludeDefaultMomentLocales", "IgnorePlugin", "resourceRegExp", "contextRegExp", "NextJsRequireCacheHotReloader", "devP<PERSON><PERSON>", "HotModuleReplacementPlugin", "isEdgeRuntime", "sriEnabled", "sri", "algorithm", "edgeEnvironments", "__NEXT_BUILD_ID", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY", "__NEXT_PREVIEW_MODE_ID", "previewModeId", "__NEXT_PREVIEW_MODE_SIGNING_KEY", "previewModeSigningKey", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY", "previewModeEncryptionKey", "filePath", "cache<PERSON>ey", "__NEXT_VERSION", "minimized", "experimentalInlineCss", "inlineCss", "cacheLifeConfig", "cacheLife", "cssChunking", "experiments", "strict", "nextjs", "isImplicit", "baseUrl", "unshift", "edgeAsset", "layers", "cacheUnaffected", "buildHttp", "urlImports", "<PERSON><PERSON><PERSON>", "cacheLocation", "lockfileLocation", "javascript", "generator", "asset", "trustedTypes", "enabledLibraryTypes", "snapshot", "managedPaths", "immutablePaths", "providedExports", "usedExports", "configVars", "JSON", "stringify", "trailingSlash", "buildActivityPosition", "devIndicators", "position", "productionBrowserSourceMaps", "reactStrictMode", "optimizeCss", "nextScriptWorkers", "scrollRestoration", "sw<PERSON><PERSON><PERSON><PERSON>", "imageLoaderFile", "loaderFile", "clientTraceMetadata", "serverSourceMaps", "maxMemoryGenerations", "Infinity", "compression", "buildDependencies", "defaultWebpack", "hooks", "done", "tap", "stats", "compilation", "nextPackage", "dirname", "dep", "delete", "NEXT_WEBPACK_LOGGING", "infra", "profileClient", "profileServer", "summaryClient", "summaryServer", "profile", "summary", "logDefault", "infrastructureLogging", "level", "debug", "log", "toString", "colors", "logging", "preset", "timings", "ProgressPlugin", "rootDirectory", "customAppFile", "isDevelopment", "targetWeb", "deploymentId", "sassOptions", "future", "mode", "unsafeCache", "originalDevtool", "pluginCountBefore", "totalPages", "nextRuntime", "pluginCountAfter", "pluginsChanged", "addUsage", "configFileName", "lazyCompilation", "entries", "then", "customSvgRule", "find", "nextImageRule", "craCompat", "fileLoaderExclude", "fileLoader", "topRules", "innerRules", "webpackDevMiddleware", "canMatchCss", "fileNames", "input", "hasUserCssConfig", "o", "Symbol", "for", "__next_css_remove", "e", "originalEntry", "updatedEntry", "originalFile", "value", "nextRootParamsModule", "createInvalidImportRule", "invalidClientImportRule"], "mappings": "AAAA,OAAOA,WAAW,QAAO;AACzB,OAAOC,+BAA+B,8EAA6E;AACnH,SAASC,MAAM,EAAEC,IAAI,QAAQ,oBAAmB;AAChD,OAAOC,YAAY,SAAQ;AAC3B,SAASC,OAAO,QAAQ,qCAAoC;AAC5D,OAAOC,UAAU,OAAM;AAEvB,SAASC,YAAY,QAAQ,eAAc;AAC3C,SAASC,kBAAkB,QAAQ,8BAA6B;AAChE,SAASC,cAAc,EAAEC,wBAAwB,QAAQ,mBAAkB;AAE3E,SACEC,qBAAqB,EACrBC,wBAAwB,EACxBC,6BAA6B,EAC7BC,qBAAqB,EACrBC,2BAA2B,QACtB,UAAS;AAEhB,SACEC,+BAA+B,EAC/BC,gCAAgC,EAChCC,oCAAoC,EACpCC,4CAA4C,EAC5CC,yCAAyC,EACzCC,mCAAmC,EACnCC,kCAAkC,EAClCC,uBAAuB,EACvBC,gBAAgB,EAChBC,cAAc,QACT,0BAAyB;AAEhC,SAASC,QAAQ,QAAQ,sBAAqB;AAE9C,SAASC,kBAAkB,QAAQ,YAAW;AAC9C,YAAYC,SAAS,eAAc;AACnC,SAASC,kBAAkB,QAAQ,mBAAkB;AACrD,OAAOC,oBACLC,wBAAwB,EACxBC,mCAAmC,QAC9B,sCAAqC;AAC5C,OAAOC,yBAAyB,0CAAyC;AACzE,SAASC,mBAAmB,QAAQ,0CAAyC;AAC7E,SAASC,cAAc,QAAQ,iDAAgD;AAC/E,OAAOC,yBAAyB,0CAAyC;AACzE,SAASC,eAAe,QAAQ,qCAAoC;AACpE,SAASC,mBAAmB,QAAQ,0CAAyC;AAC7E,SAASC,qBAAqB,QAAQ,4CAA2C;AACjF,SAASC,YAAY,QAAQ,8BAA6B;AAC1D,SAASC,cAAc,QAAQ,qCAAoC;AACnE,SAASC,6BAA6B,QAAQ,2CAA0C;AACxF,SAASC,2BAA2BC,2BAA2B,QAAQ,+CAA8C;AACrH,SAASC,6BAA6B,QAAQ,sDAAqD;AACnG,SAASC,eAAe,QAAQ,sCAAqC;AAOrE,OAAOC,kBAGA,kBAAiB;AACxB,SAASC,YAAY,QAAQ,QAAO;AACpC,SAASC,sBAAsB,QAAQ,8CAA6C;AACpF,SAASC,0BAA0B,QAAQ,iDAAgD;AAC3F,SAASC,sBAAsB,QAAQ,8CAA6C;AACpF,SAASC,oBAAoB,QAAQ,UAAS;AAC9C,SAASC,uBAAuB,QAAQ,gDAA+C;AACvF,SAASC,kBAAkB,QAAQ,0BAAyB;AAC5D,SAASC,sBAAsB,QAAQ,kCAAiC;AAExE,SAASC,oBAAoB,EAAEC,mBAAmB,QAAQ,qBAAoB;AAC9E,SACEC,YAAY,EACZC,kBAAkB,QACb,iCAAgC;AACvC,SAASC,oCAAoC,QAAQ,4DAA2D;AAChH,SACEC,oBAAoB,EACpBC,iCAAiC,EACjCC,0BAA0B,EAC1BC,uBAAuB,EACvBC,yBAAyB,QACpB,4BAA2B;AAClC,SAASC,qBAAqB,QAAQ,kBAAiB;AACvD,SAASC,iBAAiB,QAAQ,wCAAuC;AACzE,SACEC,cAAc,EACdC,sBAAsB,QACjB,4BAA2B;AAClC,SACEC,iBAAiB,EACjBC,6BAA6B,QACxB,mBAAkB;AACzB,SAASC,aAAa,EAAEC,qBAAqB,QAAQ,2BAA0B;AAC/E,SAASC,qBAAqB,QAAQ,4CAA2C;AACjF,OAAOC,uBAAuB,oCAAmC;AAUjE,MAAMC,oBACJC,QAAQ;AAEV,MAAMC,8BACJD,QAAQ;AAEV,IAAIE,SAAS/E,MAAMgF,OAAO,IAAI,IAAI;IAChC,MAAM,qBAA8D,CAA9D,IAAIC,MAAM,sDAAV,qBAAA;eAAA;oBAAA;sBAAA;IAA6D;AACrE;AAEA,OAAO,MAAMC,sBAAgC;IAC3C;IACA;IACA;IACA;CACD,CAAA;AAED,MAAMC,6BAA6B;IACjC,+FAA+F;IAC/F,2HAA2H;IAC3H,2DAA2D;IAC3D;IACA,oGAAoG;IACpG,8GAA8G;IAC9G;CACD;AACD,MAAMC,kBAAkB;AAExB,MAAMC,qBACJ;AAEF,wBAAwB;AACxB,MAAMC,eAAe,AAACC,CAAAA,QAAQC,GAAG,CAACC,SAAS,IAAI,EAAC,EAC7CC,KAAK,CAACH,QAAQI,QAAQ,KAAK,UAAU,MAAM,KAC3CC,MAAM,CAAC,CAACC,IAAM,CAAC,CAACA;AAEnB,MAAMC,mBAA0DC,OAAOC,MAAM,CAAC;IAC5EC,kBAAkB;IAClBC,SACE,yDAAyD;IACzD;AACJ;AAEA,SAASC,YAAYC,MAAwB;IAC3C,OACE,0BAA0B;IAC1BA,OAAOC,IAAI,KAAK,CAAC,gBAAgB,CAAC,IAClC,0CAA0C;IAC1CD,OAAOC,IAAI,KAAK,CAAC,kBAAkB,CAAC,IACpC,0CAA0C;IAC1CD,OAAOC,IAAI,KAAK,CAAC,sBAAsB,CAAC;AAE5C;AAEA,MAAMC,uBAAuB5E,SAC3B,CAAC6E;IACCC,QAAQC,IAAI,CACVvG,OAAOC,KAAK,gBACVA,KAAK,CAAC,8BAA8B,EAAEoG,QAAQ,IAAI,CAAC,IACnD,kGACA;AAEN;AAGF,IAAIG,oBAAoB;AACxB,IAAIC,+BAA+B;AACnC,MAAMC,yBACJ;AAEF,SAASC;IACP,OAAOtB,QAAQC,GAAG,CAACsB,WAAW,GAC1BrC,wBAAwBsC,MAAM,GAC9BlC,QAAQmC,OAAO,CAACJ;AACtB;AAEA,OAAO,SAASK,mBACdC,aAAoC,EACpCC,YAAoC;QAGpCD,6BAAAA;IADA,MAAME,qBAAqBP;KAC3BK,wBAAAA,cAAcd,MAAM,sBAApBc,8BAAAA,sBAAsBG,KAAK,qBAA3BH,4BAA6BI,OAAO,CAAC,CAACC;QACpC,IAAIA,QAAQ,OAAOA,SAAS,YAAY,SAASA,MAAM;YACrD,MAAMC,OAAOD,KAAKE,GAAG;YACrB,wEAAwE;YACxE,IAAID,SAASL,cAAc;gBACzBI,KAAKE,GAAG,GAAG;oBAACL;oBAAoBI;iBAA+B;YACjE,OAAO,IACLE,MAAMC,OAAO,CAACH,SACdA,KAAKI,IAAI,CAAC,CAACC,IAAMA,MAAMV,iBACvB,kCAAkC;YAClC,CAACK,KAAKI,IAAI,CACR,CAACC,IAAMA,MAAMT,sBAAsBS,MAAMjB,yBAE3C;gBACA,MAAMkB,MAAMN,KAAKO,SAAS,CAAC,CAACF,IAAMA,MAAMV;gBACxC,iCAAiC;gBACjCI,KAAKE,GAAG,GAAG;uBAAID;iBAAK;gBAEpB,kEAAkE;gBAClED,KAAKE,GAAG,CAACO,MAAM,CAACF,KAAK,GAAGV;YAC1B;QACF;IACF;AACF;AAEA,OAAO,MAAMa,uBAAuB;IAClCC,gBAAgB;IAChBC,SAAS;QAAC;KAAe;IACzBC,UAAU;IACVC,eAAe;QAAC;KAAU;IAC1BC,eAAe;QAAC;KAAU;IAC1BC,gBAAgB;QAAC;QAAQ;KAAU;IACnCC,kBAAkB;QAAC;KAAe;IAClCC,YAAY;QAAC;QAAO;QAAS;KAAQ;IACrCC,mBAAmB;IACnBC,UAAU;IACVC,YAAY;QAAC;KAAO;IACpBC,WAAW;QAAC;KAAQ;IACpBC,OAAO,EAAE;IACTC,gBAAgB;IAChBC,gBAAgB;IAChBC,gBAAgB;IAChBC,cAAc,EAAE;AAClB,EAAC;AAED,OAAO,MAAMC,4BAA4B;IACvC,GAAGlB,oBAAoB;IACvBmB,OAAO;AACT,EAAC;AAED,OAAO,MAAMC,2BAA2B;IACtC,GAAGpB,oBAAoB;IACvBmB,OAAO;IACPlB,gBAAgB;IAChBK,gBAAgB;QAAC;QAAQ;KAAS;IAClCQ,gBAAgB;AAClB,EAAC;AAED,OAAO,MAAMO,gCAAgC;IAC3C,GAAGD,wBAAwB;IAC3BD,OAAO;AACT,EAAC;AAED,OAAO,MAAMG,uBACX,+CAA8C;AAEhD,OAAO,eAAeC,gBAAgB,EACpCC,GAAG,EACHC,MAAM,EACNC,GAAG,EAKJ;IAMC,MAAM,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,eAAe,EAAE,GAAG,MAAM/G,aACxD0G,KACAC;IAEF,MAAMK,oBAAoB3G,qBAAqBqG,KAAKE;IACpD,OAAO;QACLC;QACAC;QACAC;QACAC;IACF;AACF;AAEA,OAAO,SAASC;IACd,IAAI;QACFnF,QAAQ;QACR,OAAO;IACT,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAEA,MAAMoF,qBAAqB;AAE3B,OAAO,SAASC,oBACdC,OAAgC;IAEhC,OAAO,IAAIC,IACTD,QACGE,GAAG,CAAC,CAACC;QACJ,IAAI,OAAOA,IAAIC,KAAK,KAAK,YAAYD,IAAIC,KAAK,CAAClE,IAAI,KAAK,cAAc;YACpE,OAAOiE,IAAIC,KAAK,CAACC,cAAc;QACjC;QACA,OAAO;IACT,GACC5E,MAAM,CAAC,CAAC6D,MAAQA,OAAO;AAE9B;AAEA,eAAe,eAAegB,qBAC5BhB,GAAW,EACX,EACEiB,OAAO,EACPC,aAAa,EACbjB,MAAM,EACNkB,YAAY,EACZjB,MAAM,KAAK,EACXkB,WAAW,EACXC,gBAAgB,KAAK,EACrBC,QAAQ,EACRC,2BAA2B,KAAK,EAChCC,QAAQ,EACRC,gBAAgB,EAChBC,iBAAiB,EACjBC,cAAc,EACdC,MAAM,EACNC,kBAAkB,EAClBC,UAAU,EACV3B,QAAQ,EACRC,YAAY,EACZC,eAAe,EACfC,iBAAiB,EACjByB,mBAAmB,EACnBC,mBAAmB,EACnBC,aAAa,EACbC,YAAY,EAiCb;QAsHCjC,sBAOIA,uBAwciBA,kBACWA,mBAGtBA,mBAIAE,2BAEmBF,mBACDE,4BACLF,mBAgTfA,uBAoCAA,sBA0xBoBA,0BAiEtBA,2BA2CJE,4BAJJ,wDAAwD;IACxD,iCAAiC;IACjC1C,gCAAAA,wBAmG0BwC,uBAsBTA,mBACQA,mBACLA,mBACXA,mBACEA,mBAsCXkC,yBAiLc1E,uBAoDZA,wBA0FAA,6BAAAA;IAzwEF,MAAM2E,UAAUlH;IAChB,MAAMmH,WAAWlB,iBAAiBnJ,eAAesK,MAAM;IACvD,MAAMC,eAAepB,iBAAiBnJ,eAAewK,UAAU;IAC/D,MAAMC,eAAetB,iBAAiBnJ,eAAe0K,MAAM;IAE3D,MAAMC,WAAWC,QAAQ9G,QAAQC,GAAG,CAACsB,WAAW;IAEhD,MAAMnE,0BACJyJ,YAAY7G,QAAQC,GAAG,CAAC8G,kCAAkC,GACtDzJ,gCACAD;IAEN,uFAAuF;IACvF,MAAM2J,0BAA0BL,gBAAgBF;IAEhD,MAAMQ,cACJvB,SAASwB,WAAW,CAACC,MAAM,GAAG,KAC9BzB,SAAS0B,UAAU,CAACD,MAAM,GAAG,KAC7BzB,SAAS7C,QAAQ,CAACsE,MAAM,GAAG;IAE7B,MAAME,YAAY,CAAC,CAACvB;IACpB,MAAMwB,0BAA0B;IAChC,MAAMC,sBAAsBvJ,uBAAuBmG,UAC/C,kBACA;IAEJ,MAAMqD,kBAAkBzJ,mBAAmBmG;IAE3C,IAAI,CAACE,OAAOzF,sBAAsBwF,SAAS;QACzCA,OAAOsD,OAAO,GAAG;IACnB;IACA,MAAMA,UAAU1M,KAAK2M,IAAI,CAACxD,KAAKC,OAAOsD,OAAO;IAE7C,IAAIE,eAAe,CAACH,mBAAmBrD,OAAOyD,YAAY,CAACC,kBAAkB;IAC7E,IAAIC,kBAAkDC;IACtD,IAAIJ,cAAc;YAEK,4BAAA,6BACnBrI;QAFF,0CAA0C;QAC1C,MAAM0I,gBACJ1I,WAAAA,QAAQ,8BADW,8BAAA,AACnBA,SACC2I,iBAAiB,sBAFC,6BAAA,iCACnB3I,8BADmB,2BAEK4I,MAAM;QAChCJ,kBAAkBE,eACd;YAAC,CAAC,WAAW,EAAEA,cAAc;YAAW;SAAK,GAC7CD;IACN;IAEA,IAAI,CAAC5G,qBAAqB,CAACwG,gBAAgBH,iBAAiB;QAC1DnL,IAAI8L,IAAI,CACN,CAAC,6EAA6E,EAAEpN,KAAKqN,QAAQ,CAC3FlE,KACAsD,iBACA,+CAA+C,CAAC;QAEpDrG,oBAAoB;IACtB;IAEA,mEAAmE;IACnE,IAAI,CAACqG,mBAAmBjB,UAAU;QAChC,MAAM9I,aAAa0G,OAAOyD,YAAY,CAACS,aAAa;IACtD;IAEA,4DAA4D;IAC5D,2DAA2D;IAC3D,MAAMC,yBAAmC,AACvCnE,CAAAA,OAAOoE,iBAAiB,IAAI,EAAE,AAAD,EAC7BC,MAAM,CAACjJ;IAET,KAAK,MAAMkJ,OAAOtE,OAAOyD,YAAY,CAACc,sBAAsB,IAAI,EAAE,CAAE;QAClE,IAAI,CAACJ,uBAAuBK,QAAQ,CAACF,MAAM;YACzCH,uBAAuBM,IAAI,CAACH;QAC9B;IACF;IAEA,IAAI,CAACrH,gCAAgC,CAACuG,gBAAgBxD,OAAO0E,QAAQ,EAAE;QACrExM,IAAI8L,IAAI,CACN;QAEF/G,+BAA+B;IACjC;IAEA,MAAM0H,eAAwC,CAAC;IAC/C,SAASC,QAAQC,WAAmB;QAClC,MAAMC,SAASH,YAAY,CAACE,YAAY;QACxC,IAAIC,WAAWlB,WAAW;YACxB,OAAOkB;QACT;QAEA,MAAMC,gBACJF,YAAYL,QAAQ,CAAC,mBACrB,CAAChJ,oBAAoB0C,IAAI,CAAC,CAACC,IAAMA,EAAE6G,IAAI,CAACH,iBACxC,CAAC/K,qBAAqB+K,aAAaV;QAErCQ,YAAY,CAACE,YAAY,GAAGE;QAC5B,OAAOA;IACT;IAEA,MAAME,4BACJjF,OAAOyD,YAAY,CAACyB,WAAW,IAAI,CAAC,CAAClF,OAAOoE,iBAAiB;IAC/D,MAAMe,gBAAgB;QACpBH,MAAM;YAAEI,IAAI;gBAAC;gBAA8B;aAAsB;QAAC;QAClE,GAAIH,4BAEA,CAAC,IACD;YAAEI,SAAS;gBAACtF;mBAAQvE;aAAoB;QAAC,CAAC;QAC9CoJ;IACF;IAEA,MAAMU,cAAc5K,eAClB8I,cACAH,iBACAR,yBACAS,SACAjC,UACAtB,KACC4B,UAAUN,UACXpB,KACAmC,WACApC,uBAAAA,OAAOyD,YAAY,qBAAnBzD,qBAAqBuF,aAAa,EAClCJ,cAAcP,OAAO;IAGvB,MAAMY,sBAAsBF,cACxB1B,YACAjJ,wBACEqF,wBAAAA,OAAOyD,YAAY,qBAAnBzD,sBAAqBuF,aAAa,EAClCxF,KACAE,KACA4C,yBACAsC,cAAcP,OAAO;IAG3B,IAAIa,+BAA+B;IACnC,MAAMC,eAAe,CAACC;YAElB3F;QADF,IACEA,CAAAA,2BAAAA,uBAAAA,OAAQyD,YAAY,qBAApBzD,qBAAsB4F,iBAAiB,KACvC,CAACH,8BACD;gBAMC,oCACCtK;YANF,sEAAsE;YACtE,+CAA+C;YAC/C,qFAAqF;YACrF,uDAAuD;YACvDsK,+BAA+B;aAE7BtK,WAAAA,QAAQ,8BADT,qCAAA,AACCA,SACC0K,yBAAyB,qBAF3B,wCACC1K,UAEAvE,KAAK2M,IAAI,CAACD,SAAS,CAAC,kBAAkB,EAAEwC,KAAKC,GAAG,GAAG,KAAK,CAAC;QAE7D;QAEA,MAAMC,sBAAsBnK,QAAQC,GAAG,CAACmK,kBAAkB;QAC1D,IAAIvD,YAAYsD,qBAAqB;gBAwB7B9F,2BAGAA;YA1BN,OAAO;gBACL7C,QAAQ;gBACR6I,SAAS;oBACPC,UAAUtD;oBACVuD,SAASrG;oBACTsB;oBACAM;oBACA0E,iBAAiBpG,OAAOmC;oBACxBgC,mBAAmBD;oBACnB9D;oBACAiG,aAAa1P,KAAK2M,IAAI,CACpBxD,KACAC,CAAAA,0BAAAA,OAAQsD,OAAO,KAAI,SACnB,SACA;oBAEFiD,yBAAyBtF;oBAEzB,0BAA0B;oBAC1BuF,KAAK7D,QAAQ9G,QAAQ4K,QAAQ,CAACD,GAAG;oBACjCE,qBAAqB/D,QAAQ3C,OAAOyD,YAAY,CAACiD,mBAAmB;oBACpEC,mBAAmB3G,OAAO2G,iBAAiB;oBAC3CC,YAAYjE,QACVzC,6BAAAA,4BAAAA,SAAU2G,eAAe,qBAAzB3G,0BAA2B4G,sBAAsB;oBAEnDC,uBAAuBpE,QACrBzC,6BAAAA,6BAAAA,SAAU2G,eAAe,qBAAzB3G,2BAA2B6G,qBAAqB;oBAElDC,wBAAwB7L,QAAQmC,OAAO,CACrC;oBAGF,GAAGqI,YAAY;gBACjB;YACF;QACF;QAEA,OAAO;YACLtI,QAAQ;YACR6I,SAAS;gBACPC,UAAUtD;gBACV3B;gBACAkF,SAASrG;gBACTsB;gBACAM;gBACA0E,iBAAiBpG,OAAOmC;gBACxB6E,YAAYjH;gBACZE;gBACAkE,mBAAmBD;gBACnB9D;gBACAiG,aAAa1P,KAAK2M,IAAI,CAACxD,KAAKC,CAAAA,0BAAAA,OAAQsD,OAAO,KAAI,SAAS,SAAS;gBACjEiD,yBAAyBtF;gBACzB,GAAG0E,YAAY;YACjB;QACF;IACF;IAEA,6CAA6C;IAC7C,MAAMuB,uBAAuBxB,aAAa;QACxCyB,kBAAkB;QAClBC,aAAarQ,eAAesQ,qBAAqB;QACjDC,KAAK;IACP;IACA,MAAMC,oBAAoB7B,aAAa;QACrCyB,kBAAkB;QAClBC,aAAarQ,eAAeyQ,mBAAmB;QAC/CF,KAAK;IACP;IACA,MAAMG,wBAAwB/B,aAAa;QACzCyB,kBAAkB;QAClBC,aAAarQ,eAAe2Q,eAAe;QAC3CJ,KAAK;IACP;IACA,oDAAoD;IACpD,MAAMK,mBAAmBjC,aAAa;QACpCyB,kBAAkB;QAClBG,KAAK;IACP;IAEA,MAAMM,iBAAiB;QACrBC,OAAOrE,eAAemE,mBAAmBrC;IAC3C;IAEA,MAAMwC,wBAAwB5E,YAC1B;QACE,uDAAuD;QACvD,iDAAiD;QACjD,gDAAgD;QAChD,+CAA+C;QAC/CgE;QACA5B;QACAE;KACD,CAACtJ,MAAM,CAACyG,WACT,EAAE;IAEN,MAAMoF,yBAAyB;QAC7B;QACA,uDAAuD;QACvD,iDAAiD;QACjD,gDAAgD;QAChD,+CAA+C;QAC/Cb;QACA5B;KACD,CAACpJ,MAAM,CAACyG;IAET,MAAMqF,yBAAyB;QAC7B;QACA,mEAAmE;QACnE,wFAAwF;QACxF,gDAAgD;QAChD,+CAA+C;QAC/CtC,aAAa;YACXyB,kBAAkB;YAClBC,aAAarQ,eAAekR,UAAU;QACxC;QACA3C;KACD,CAACpJ,MAAM,CAACyG;IAET,MAAMuF,sBAAsBjI,OAAOmC,WAAW;QAACjF;KAAwB,GAAG,EAAE;IAE5E,2CAA2C;IAC3C,MAAMgL,0BAA0B,CAAC,EAC/BC,cAAc,EACdC,YAAY,EAIb,GAAK;eACAA,eAAeH,sBAAsB,EAAE;YAC3C;gBACE,iDAAiD;gBACjD,uBAAuB;gBACvB7K,QAAQ;YACV;eACI6F,YACA;gBACE,uDAAuD;gBACvD,iDAAiD;gBACjD,gDAAgD;gBAChD,+CAA+C;gBAC/CkF,iBAAiBX,wBAAwBF;gBACzCjC;gBACAE;aACD,CAACtJ,MAAM,CAACyG,WACT,EAAE;SACP;IAED,MAAM2F,yBAAyBH,wBAAwB;QACrDC,gBAAgB;QAChB,8EAA8E;QAC9EC,cAAc;IAChB;IACA,MAAME,qBAAqBJ,wBAAwB;QACjDC,gBAAgB;QAChBC,cAAc;IAChB;IAEA,2EAA2E;IAC3E,8EAA8E;IAC9E,gBAAgB;IAChB,MAAMG,wBAAwBhF,eAC1BkC,aAAa;QACXyB,kBAAkB;QAClBC,aAAarQ,eAAe0R,OAAO;IACrC,KACAb,eAAeC,KAAK;IAExB,MAAMa,iBAAiB1I,OAAO0I,cAAc;IAE5C,MAAMC,aAAa9F,0BACfjM,KAAK2M,IAAI,CAACD,SAASxL,oBACnBwL;IAEJ,MAAMsF,uBAAuB;QAC3B;WACItG,eAAerI,qBAAqB,EAAE;QAC1C,kCAAkC;QAClC;KACD;IAED,MAAM4O,oBAAoBnG,WACtB3H,wBAAwB+N,KAAK,GAC7B3N,QAAQmC,OAAO,CACb,CAAC,yDAAyD,CAAC;IAGjE,MAAMyL,gBAAgB3G,WACjB;QACC,0BAA0B;QAC1B,WAAW,EAAE;QACb,GAAInC,MACA;YACE,CAACvI,0CAA0C,EAAEmR;YAC7C,CAACvR,gCAAgC,EAC/B,CAAC,EAAE,CAAC,GACJV,KACGqN,QAAQ,CACPlE,KACAnJ,KAAK2M,IAAI,CAAC1I,+BAA+B,OAAO,YAEjDmO,OAAO,CAAC,OAAO;QACtB,IACA,CAAC,CAAC;QACN,CAACzR,iCAAiC,EAChC,CAAC,EAAE,CAAC,GACJX,KACGqN,QAAQ,CACPlE,KACAnJ,KAAK2M,IAAI,CACP1I,+BACAoF,MAAM,CAAC,WAAW,CAAC,GAAG,YAGzB+I,OAAO,CAAC,OAAO;QACpB,GAAI9F,YACA;YACE,CAAC1L,qCAAqC,EAAEyI,MACpC;gBACE4I;gBACA,CAAC,EAAE,CAAC,GACFjS,KACGqN,QAAQ,CACPlE,KACAnJ,KAAK2M,IAAI,CACP1I,+BACA,oBAGHmO,OAAO,CAAC,OAAO;aACrB,GACD;gBACE,CAAC,EAAE,CAAC,GACFpS,KACGqN,QAAQ,CACPlE,KACAnJ,KAAK2M,IAAI,CACP1I,+BACA,gBAGHmO,OAAO,CAAC,OAAO;aACrB;QACP,IACA,CAAC,CAAC;IACR,IACApF;IAEJ,MAAMqF,gBAAkD;QACtD,yCAAyC;QACzClK,YAAY;YAAC;YAAO;YAAQ;YAAQ;YAAO;YAAQ;YAAS;SAAQ;QACpEmK,gBAAgBlJ,OAAOyD,YAAY,CAACyF,cAAc;QAClDzK,SAAS;YACP;eACG7C;SACJ;QACD8D,OAAOvF,qBAAqB;YAC1BmJ;YACAlB;YACAE;YACArC;YACAD;YACAqB;YACAM;YACA5B;YACAuB;QACF;QACA,GAAIc,WACA;YACE1D,UAAU;gBACR7C,SAASV,QAAQmC,OAAO,CAAC;YAC3B;QACF,IACAsG,SAAS;QACb,oFAAoF;QACpF1E,YAAYlF,aAAakH,cAAc;QACvC,GAAIoB,gBAAgB;YAClBzD,gBAAgB5E;QAClB,CAAC;QACDkP,SAAS;YACP3G,eAAe,IAAItI,yCAAyC0J;SAC7D,CAAC1H,MAAM,CAACyG;QACT,GAAKD,YAAYvC,eACb;YACE,6CAA6C;YAC7CX,cAAc;gBAAC;aAAqB;YACpC4J,UAAU;gBACRC,YAAYlJ;YACd;QACF,IACA,CAAC,CAAC;IACR;IAEA,2DAA2D;IAC3D,gEAAgE;IAChE,mEAAmE;IACnE,MAAMmJ,qBAA+B,EAAE;IACvC,MAAMC,yBAAmC,EAAE;IAC3C,MAAMC,2BAA2B,IAAI9I;IACrC,iDAAiD;IACjD,MAAM+I,iBAAiB,CACrBC,aACAC,gBACAC;QAEA,IAAI;YACF,IAAIJ,yBAAyBK,GAAG,CAACH,cAAc;gBAC7C;YACF;YACAF,yBAAyBM,GAAG,CAACJ;YAE7B,MAAMK,kBAAkB5O,QAAQmC,OAAO,CAAC,GAAGoM,YAAY,aAAa,CAAC,EAAE;gBACrEE,OAAO;oBAACD;iBAAe;YACzB;YAEA,6FAA6F;YAC7F,0EAA0E;YAC1E,eAAe;YACf,0EAA0E;YAC1E,2EAA2E;YAC3E,MAAMK,YAAYpT,KAAK2M,IAAI,CAACwG,iBAAiB;YAE7C,yFAAyF;YACzF,IAAIH,MAAMpF,QAAQ,CAACwF,YAAY;YAC/BJ,MAAMnF,IAAI,CAACuF;YACX,MAAMC,eAAe9O,QAAQ4O,iBAAiBE,YAAY,IAAI,CAAC;YAC/D,KAAK,MAAMC,QAAQ7N,OAAO8N,IAAI,CAACF,cAAe;gBAC5CR,eAAeS,MAAMF,WAAWJ;YAClC;QACF,EAAE,OAAOQ,GAAG;QACV,uDAAuD;QACzD;IACF;IAEA,KAAK,MAAMV,eAAe;QACxB;QACA;WACIxG,YACA;YACE,CAAC,wBAAwB,EAAEE,qBAAqB;YAChD,CAAC,4BAA4B,EAAEA,qBAAqB;SACrD,GACD,EAAE;KACP,CAAE;QACDqG,eAAeC,aAAa3J,KAAKwJ;IACnC;IACAE,eAAe,QAAQ1J,KAAKuJ;IAE5B,MAAMe,cAAcrK,OAAOqK,WAAW;IAEtC,wDAAwD;IACxD,2BAA2B;IAC3B,IAAIrK,OAAOsK,sBAAsB,IAAInG,wBAAwB;QAC3D,MAAMoG,2BAA2BpG,uBAAuBjI,MAAM,CAAC,CAACoI;gBAC9DtE;oBAAAA,iCAAAA,OAAOsK,sBAAsB,qBAA7BtK,+BAA+BwE,QAAQ,CAACF;;QAE1C,IAAIiG,yBAAyBvH,MAAM,GAAG,GAAG;YACvC,MAAM,qBAIL,CAJK,IAAIzH,MACR,CAAC,8FAA8F,EAAEgP,yBAAyBhH,IAAI,CAC5H,OACC,GAHC,qBAAA;uBAAA;4BAAA;8BAAA;YAIN;QACF;IACF;IAEA,+CAA+C;IAC/C,MAAMiH,yBAAyBtP,kBAAkBmJ,MAAM,IACjDrE,OAAOsK,sBAAsB,IAAI,EAAE,EACvCpO,MAAM,CAAC,CAACoI,MAAQ,EAACH,0CAAAA,uBAAwBK,QAAQ,CAACF;IACpD,wEAAwE;IACxE,MAAMmG,6BAA6B,IAAIC,OACrC,CAAC,2BAA2B,EAAEF,uBAC3B7J,GAAG,CAAC,CAACxE,IAAMA,EAAE6M,OAAO,CAAC,OAAO,YAC5BzF,IAAI,CAAC,KAAK,QAAQ,CAAC;IAGxB,MAAMoH,yBAAyB,IAAID,OACjC,CAAC,2BAA2B,EAAEvG,0CAAAA,uBAC1BxD,GAAG,CAAC,CAACxE,IAAMA,EAAE6M,OAAO,CAAC,OAAO,YAC7BzF,IAAI,CAAC,KAAK,QAAQ,CAAC;IAGxB,MAAMqH,kBAAkB7Q,oBAAoB;QAC1CiG;QACAyK;QACAI,oBAAoB1G;QACpBpE;IACF;IAEA,MAAM+K,sBAAsB,IAAIJ,OAAO,CAAC,IAAI,EAAEhC,eAAenF,IAAI,CAAC,KAAK,EAAE,CAAC;IAE1E,MAAMwH,yBAAyB;QAAC5F,cAAcH,IAAI;QAAE8F;KAAoB;IAExE,MAAME,iBAAiB,AAAC7P,QAAQ,UAC7B6P,cAAc;IAEjB,MAAMC,eAAe;QACnB;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAMC,kCACJ,CAAC,CAAClL,OAAOyD,YAAY,CAAC0H,mBAAmB,IAAIlL;IAE/C,MAAMmL,iBAAiB;QACrB,MAAMC,WAAWC,OAAOzP,QAAQC,GAAG,CAACyP,wBAAwB;QAC5D,IAAIL,iCAAiC;YACnC,IAAIG,UAAU;gBACZvO,QAAQC,IAAI,CACV;YAEJ;YACA,OAAO;QACT;QACA,OAAOsO,YAAYzH;IACrB;IAEA,MAAM4H,kBACJ,CAACvL,OACDmC,YACA,IAAI,AACFjH,CAAAA,QAAQ,sDAAqD,EAC7DsQ,eAAe,CACf,IAAIC,IACF;QACE;YAAC;YAAalI;SAAa;QAC3B;YAAC;YAAY,CAAC,GAACxD,mBAAAA,OAAO0E,QAAQ,qBAAf1E,iBAAiB2L,KAAK;SAAC;QACtC;YAAC;YAAuB,CAAC,GAAC3L,oBAAAA,OAAO0E,QAAQ,qBAAf1E,kBAAiB4L,gBAAgB;SAAC;QAC5D;YACE;YACA,CAAC,GAAC5L,oBAAAA,OAAO0E,QAAQ,qBAAf1E,kBAAiB6L,qBAAqB;SACzC;QACD;YACE;YACA,CAAC,EAAC3L,6BAAAA,4BAAAA,SAAU2G,eAAe,qBAAzB3G,0BAA2B4G,sBAAsB;SACpD;QACD;YAAC;YAAoB,CAAC,GAAC9G,oBAAAA,OAAO0E,QAAQ,qBAAf1E,kBAAiB8L,aAAa;SAAC;QACtD;YAAC;YAAmB,CAAC,EAAC5L,6BAAAA,6BAAAA,SAAU2G,eAAe,qBAAzB3G,2BAA2B6L,eAAe;SAAC;QACjE;YAAC;YAAc,CAAC,GAAC/L,oBAAAA,OAAO0E,QAAQ,qBAAf1E,kBAAiBgM,OAAO;SAAC;QAC1C;YAAC;YAAqB,CAAC,CAAChM,OAAOoE,iBAAiB;SAAC;QACjD;YAAC;YAA8B,CAAC,CAACpE,OAAOiM,0BAA0B;SAAC;QACnE;YAAC;YAA6B,CAAC,CAACjM,OAAOkM,yBAAyB;SAAC;QACjE;YAAC;YAAqB,CAAC,CAAClM,OAAO2G,iBAAiB;SAAC;QACjD,+EAA+E;QAC/E;YAAC;YAAgB3G,OAAOyD,YAAY,CAAC0I,YAAY,KAAK;SAAK;QAC3DxI;KACD,CAACzH,MAAM,CAAqByG;IAInC,IAAInF,gBAAuC;QACzC4O,aAAahB;QACb,GAAI5I,eAAe;YAAE6J,kBAAkB;gBAAEC,MAAM;YAAK;QAAE,IAAI,CAAC,CAAC;QAC5D,aAAa;QACbC,WACEnK,YAAYE,eAER,8DAA8D;QAC9D,+CAA+C;QAC/C;YACE;eACIA,eACA;gBACE;oBACE,yBAAyB;oBACzB,2BAA2B;gBAC7B;gBACAjK;gBACAC;aACD,GACD,EAAE;SACP,GACD;eACK0S;eACAC;YACH,CAAC,EACCuB,OAAO,EACPC,OAAO,EACPjO,cAAc,EACdkO,WAAW,EACXC,UAAU,EAqBX,GACC/B,gBACE4B,SACAC,SACAjO,gBACAkO,YAAYE,WAAW,EACvB,CAAC1G;oBACC,MAAM2G,kBAAkBF,WAAWzG;oBACnC,OAAO,CAAC4G,gBAAwBC,mBAC9B,IAAIC,QAAQ,CAAC1P,SAAS2P;4BACpBJ,gBACEC,gBACAC,kBACA,CAACG,KAAKC,QAAQC;oCAIRA;gCAHJ,IAAIF,KAAK,OAAOD,OAAOC;gCACvB,IAAI,CAACC,QAAQ,OAAO7P,QAAQ;oCAAC;oCAAM;iCAAM;gCACzC,MAAM+P,QAAQ,SAASrI,IAAI,CAACmI,UACxBC,CAAAA,gCAAAA,mCAAAA,YAAaE,mBAAmB,qBAAhCF,iCAAkCzQ,IAAI,MACtC,WACA,UAAUqI,IAAI,CAACmI;gCACnB7P,QAAQ;oCAAC6P;oCAAQE;iCAAM;4BACzB;wBAEJ;gBACJ;SAEL;QAEPE,cAAc;YACZC,cAAc,CAACvN;YACfwN,gBAAgB;YAChBC,SAAS;YAETC,aAAa,AAAC,CAAA;gBAGZ,kBAAkB;gBAClB,IAAI1N,KAAK;oBACP,IAAIuC,cAAc;wBAChB;;;;;YAKA,GACA,MAAMoL,wBAAwB,CAACC;4BAC7B,8FAA8F;4BAC9F,4EAA4E;4BAC5E,MAAMC,QACJ;4BACF,MAAMC,QAAQF,WAAWE,KAAK,CAACD;4BAC/B,OAAOC,QAAQA,KAAK,CAAC,EAAE,GAAG;wBAC5B;wBACA,OAAO;4BACLC,aAAa;gCACX,+FAA+F;gCAC/F,yDAAyD;gCACzDC,QAAQ;oCACNC,QAAQ;oCACRC,oBAAoB;oCACpBnJ,MAAM;oCACNoJ,SAAS;oCACTC,WAAW;oCACXC,kBAAkB;oCAClBC,oBAAoB;oCACpBrE,MAAM,CAACxN;wCACL,MAAM8R,WAAW9R,OAAO+R,gBAAgB;wCACxC,MAAMC,aAAad,sBAAsBY;wCACzC,IAAIE,YAAY;4CACd,OAAO,CAAC,cAAc,EAAEA,YAAY;wCACtC,OAAO;4CACL,MAAMC,OAAOjY,OAAOkY,UAAU,CAAC,QAAQC,MAAM,CAACL;4CAC9CG,KAAKE,MAAM,CAACL;4CACZ,OAAO,CAAC,cAAc,EAAEG,KAAKG,MAAM,CAAC,QAAQ;wCAC9C;oCACF;gCACF;gCACA,mCAAmC;gCACnCC,SAAS;gCACTC,gBAAgB;4BAClB;wBACF;oBACF;oBAEA,OAAO;gBACT;gBAEA,IAAIxM,gBAAgBF,cAAc;oBAChC,OAAO;wBACL2M,UAAU,GAAG3M,eAAe,CAAC,YAAY,CAAC,GAAG,GAAG,SAAS,CAAC;wBAC1D4L,QAAQ;wBACRG,WAAW;oBACb;gBACF;gBAEA,MAAMa,sBAAsB;oBAC1BhB,QAAQ;oBACRhE,MAAM;oBACN,6DAA6D;oBAC7DiF,OAAO/X;oBACP4N,MAAKtI,MAAW;wBACd,MAAM0S,WAAW1S,OAAO+R,gBAAgB,oBAAvB/R,OAAO+R,gBAAgB,MAAvB/R;wBACjB,OAAO0S,WACH7F,uBAAuBrL,IAAI,CAAC,CAACmR,UAC3BD,SAASE,UAAU,CAACD,YAEtB;oBACN;oBACAE,UAAU;oBACV,mEAAmE;oBACnE,wCAAwC;oBACxCC,SAAS;gBACX;gBAEA,MAAMC,gBAAgB;oBACpBzK,MAAKtI,MAIJ;4BAEIA;wBADH,OACE,GAACA,eAAAA,OAAOC,IAAI,qBAAXD,aAAa4S,UAAU,CAAC,WACzB5S,OAAOgT,IAAI,KAAK,UAChB,oBAAoB1K,IAAI,CAACtI,OAAO+R,gBAAgB,MAAM;oBAE1D;oBACAvE,MAAKxN,MAKJ;wBACC,MAAMiS,OAAOjY,OAAOkY,UAAU,CAAC;wBAC/B,IAAInS,YAAYC,SAAS;4BACvBA,OAAOiT,UAAU,CAAChB;wBACpB,OAAO;4BACL,IAAI,CAACjS,OAAOkT,QAAQ,EAAE;gCACpB,MAAM,qBAEL,CAFK,IAAIrU,MACR,CAAC,iCAAiC,EAAEmB,OAAOC,IAAI,CAAC,uBAAuB,CAAC,GADpE,qBAAA;2CAAA;gDAAA;kDAAA;gCAEN;4BACF;4BACAgS,KAAKE,MAAM,CAACnS,OAAOkT,QAAQ,CAAC;gCAAEpD,SAASzM;4BAAI;wBAC7C;wBAEA,wFAAwF;wBACxF,yHAAyH;wBACzH,0CAA0C;wBAC1C,IAAIrD,OAAOyS,KAAK,EAAE;4BAChBR,KAAKE,MAAM,CAACnS,OAAOyS,KAAK;wBAC1B;wBAEA,OAAOR,KAAKG,MAAM,CAAC,OAAOe,SAAS,CAAC,GAAG;oBACzC;oBACAN,UAAU;oBACVlB,WAAW;oBACXF,oBAAoB;gBACtB;gBAEA,kBAAkB;gBAClB,OAAO;oBACL,oDAAoD;oBACpD,qDAAqD;oBACrD,oDAAoD;oBACpD,0CAA0C;oBAC1CD,QAAQxL,WAEJ,YAAY;oBACZ,mCACA,CAACoN,QACC,CAAC,iCAAiC9K,IAAI,CAAC8K,MAAM5F,IAAI;oBAEvD8D,aAAatL,WACT;wBACEqN,WAAW;4BACT7B,QAAQ;4BACRhE,MAAM;4BACNiF,OAAO9X;4BACP2N,MAAM;4BACNuK,UAAU;4BACVC,SAAS;wBACX;wBACAQ,KAAK;4BACHhL,MAAM;4BACNkF,MAAM;4BACNgE,QAAQ;4BACRqB,UAAU;4BACVlB,WAAW;4BACXF,oBAAoB;wBACtB;oBACF,IACA;wBACE4B,WAAWb;wBACXc,KAAKP;oBACP;oBACJlB,oBAAoB;oBACpBH,SAAS;gBACX;YACF,CAAA;YACA6B,cAAc7N,WACV;gBAAE8H,MAAMvS;YAAoC,IAC5CiM;YAEJsM,UACE,CAACjQ,OACAmC,CAAAA,YACCE,gBACCE,gBAAgBxC,OAAOyD,YAAY,CAAC0M,kBAAkB;YAC3DC,WAAW;gBACT,oBAAoB;gBACpB1N,WACI,IAAK5H,CAAAA,eAAc,EAAEuV,0BAA0B,CAAE;oBAC/C,6BAA6B;oBAC7B,iEAAiE;oBACjEC,kBAAkB;wBAChBC,UAAU;4BACRC,QAAQ;4BACRC,aAAa;gCACX,mDAAmD;4BACrD;wBACF;wBACAC,QAAQ,CAAC7O,cAAc;4BACrB8O,UAAU;gCAAC;6BAAc;4BACzBC,iBAAiB,CAACxO;wBACpB;oBACF;gBACF,KACA,CAACsC;oBACC,4BAA4B;oBAC5B,MAAM,EAAEmM,YAAY,EAAE,GACpB1V,QAAQ;oBACV,IAAI0V,aAAa;wBACfhP;wBACA+O,iBAAiB,CAACxO;oBACpB,GAAG0O,KAAK,CAACpM;gBACX;gBACJ,aAAa;gBACb,6DAA6D;gBAC7D,iEAAiE;gBACjEhC,YACC7G,CAAAA,QAAQC,GAAG,CAACiV,gBAAgB,GACzB/Q,OAAOyD,YAAY,CAACuN,eAAe,GACnChR,EAAAA,wBAAAA,OAAOyD,YAAY,qBAAnBzD,sBAAqBgR,eAAe,MAAKpN,aACzC5D,OAAOyD,YAAY,CAACuN,eAAe,AAAD,IAClC,IAAKlW,CAAAA,eAAc,EAAEmW,iCAAiC,CAAE;oBACtD,8BAA8B;oBAC9BX,kBAAkB;wBAChBY,SAAS7Q;oBACX;gBACF,KACA,CAACqE;oBACC,MAAM,EAAEyM,kBAAkB,EAAE,GAC1BhW,QAAQ;oBACV,IAAIgW,mBAAmB;wBACrBC,gBAAgB;4BACdzQ,KAAK;gCACH,+DAA+D;gCAC/D,+CAA+C;gCAC/C6P,QAAQ;gCACR,6DAA6D;gCAC7D,4DAA4D;gCAC5Da,YAAY;4BACd;wBACF;oBACF,GAAGP,KAAK,CAACpM;gBACX;aACL;QACH;QACA8H,SAASzM;QACT,8CAA8C;QAC9C+I,OAAO;YACL,OAAO;gBACL,GAAIC,gBAAgBA,gBAAgB,CAAC,CAAC;gBACtC,GAAG5H,WAAW;YAChB;QACF;QACAmQ,cAAcjV,OAAOC,MAAM,CAAC;YAC1B,GAAGF,gBAAgB;YACnBmV,IAAI,GAAEvR,uBAAAA,OAAOsR,YAAY,qBAAnBtR,qBAAqBwR,cAAc;QAC3C;QACAC,QAAQ;YACN,sEAAsE;YACtE,kCAAkC;YAClCC,YAAY,GACV1R,OAAO2R,WAAW,GACd3R,OAAO2R,WAAW,CAACC,QAAQ,CAAC,OAC1B5R,OAAO2R,WAAW,CAACE,KAAK,CAAC,GAAG,CAAC,KAC7B7R,OAAO2R,WAAW,GACpB,GACL,OAAO,CAAC;YACT/a,MAAM,CAACqJ,OAAOuC,eAAe5L,KAAK2M,IAAI,CAACoF,YAAY,YAAYA;YAC/D,oCAAoC;YACpCsG,UAAUpM,0BACN5C,OAAOqC,eACL,CAAC,SAAS,CAAC,GACX,CAAC,YAAY,CAAC,GAChB,CAAC,cAAc,EAAElB,gBAAgB,cAAc,GAAG,MAAM,EACtDnB,MAAM,KAAK,iBACZ,GAAG,CAAC;YACT6R,SAAS1P,YAAYE,eAAe,SAASsB;YAC7CmO,eAAe3P,YAAYE,eAAe,WAAW;YACrD0P,wBAAwB;YACxBC,uBACE;YACF,uDAAuD;YACvDC,eAAerP,0BACX,cACA,CAAC,cAAc,EAAEzB,gBAAgB,cAAc,KAC7CnB,MAAM,WAAW,uBAClB,GAAG,CAAC;YACTkS,+BAA+B;YAC/BC,oBAAoB/H;YACpB,iEAAiE;YACjE,mGAAmG;YACnG,iEAAiE;YACjE,oGAAoG;YACpG,2FAA2F;YAC3FgI,+BAA+BpS,MAC3B,6BACA2D;YACJ0O,2BAA2B;YAC3BC,cAAc;YACdC,kBAAkB;QACpB;QACAC,aAAa;QACbnV,SAAS2L;QACTyJ,eAAe;YACb,+BAA+B;YAC/BhT,OAAO;gBACL;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD,CAACiT,MAAM,CACN,CAACjT,OAAOrC;gBACN,4DAA4D;gBAC5DqC,KAAK,CAACrC,OAAO,GAAGzG,KAAK2M,IAAI,CAACqP,WAAW,WAAW,WAAWvV;gBAE3D,OAAOqC;YACT,GACA,CAAC;YAEHjB,SAAS;gBACP;mBACG7C;aACJ;YACDuN,SAAS,EAAE;QACb;QACAzM,QAAQ;YACNiB,OAAO;gBACL,+EAA+E;gBAC/E;oBACEiP,aAAa;wBACXxH,IAAI;+BACCrO,eAAe8b,KAAK,CAACC,UAAU;+BAC/B/b,eAAe8b,KAAK,CAACE,aAAa;yBACtC;oBACH;oBACAzV,SAAS;wBACP,6CAA6C;wBAC7CoC,OAAOtF,kCAAkC;oBAC3C;gBACF;gBACA;oBACEwS,aAAa;wBACXoG,KAAK;+BACAjc,eAAe8b,KAAK,CAACC,UAAU;+BAC/B/b,eAAe8b,KAAK,CAACE,aAAa;yBACtC;oBACH;oBACAzV,SAAS;wBACP,6CAA6C;wBAC7CoC,OAAOtF,kCAAkC;oBAC3C;gBACF;gBACA,mEAAmE;gBACnE;oBACE4K,MAAM;wBACJ;wBACA;qBACD;oBACD3H,QAAQ;oBACRuP,aAAa;wBACXxH,IAAIrO,eAAe8b,KAAK,CAACC,UAAU;oBACrC;oBACA5M,SAAS;wBACP+M,SACE;oBACJ;gBACF;gBACA;oBACEjO,MAAM;wBACJ;wBACA;qBACD;oBACD3H,QAAQ;oBACRuP,aAAa;wBACXoG,KAAK;+BACAjc,eAAe8b,KAAK,CAACC,UAAU;+BAC/B/b,eAAe8b,KAAK,CAACE,aAAa;yBACtC;oBACH;oBACA7M,SAAS;wBACP+M,SACE;oBACJ;gBACF;gBACA,yFAAyF;gBACzF,iFAAiF;gBACjF,oCAAoC;gBACpC;oBACEjO,MAAM;wBACJ;wBACA;qBACD;oBACD3H,QAAQ;oBACRuP,aAAa;wBACXxH,IAAIrO,eAAe8b,KAAK,CAACE,aAAa;oBACxC;gBACF;mBACIvQ,eACA,EAAE,GACF;oBACE;wBACEwC,MAAM;wBACN3H,QAAQ;oBACV;iBACD;mBACD6F,YACA;oBACE;wBACE,uFAAuF;wBACvF,UAAU;wBACViM,OAAOpY,eAAemc,MAAM;wBAC5BlO,MAAMrJ;oBACR;oBACA,4CAA4C;oBAC5C;wBACEwX,eAAe,IAAIzI,OACjB1T,yBAAyBoc,aAAa;wBAExCjE,OAAOpY,eAAesQ,qBAAqB;oBAC7C;oBACA;wBACE,gEAAgE;wBAChE,2CAA2C;wBAC3C8H,OAAOpY,eAAeyQ,mBAAmB;wBACzCxC,MAAM;oBACR;oBACA;wBACE4H,aAAa3V;wBACbqG,SAAS;4BACPoC,OAAOpF;wBACT;oBACF;oBACA;wBACEsS,aAAazV;wBACbmG,SAAS;4BACPoC,OAAOnF,0BAA0B;wBACnC;oBACF;oBACA;wBACEqS,aAAa1V;wBACboG,SAAS;4BACPoC,OAAOnF,0BAA0B;wBACnC;oBACF;iBACD,GACD,EAAE;mBACF2I,aAAa,CAACd,WACd;oBACE;wBACEwK,aAAazV;wBACb6N,MAAM;4BACJ,8DAA8D;4BAC9D,yBAAyB;4BACzBqO,KAAK;gCACHtI;gCACA;oCACEiI,KAAK;wCAACvI;wCAA4B9O;qCAAmB;gCACvD;6BACD;wBACH;wBACAwX,eAAe;4BACb,8DAA8D;4BAC9D,8DAA8D;4BAC9D,6DAA6D;4BAC7D,8DAA8D;4BAC9D,WAAW;4BACXH,KAAK;gCACH,IAAItI,OAAO1T,yBAAyBsc,QAAQ;gCAC5C,IAAI5I,OAAO1T,yBAAyBuc,iBAAiB;6BACtD;wBACH;wBACAjW,SAAS;4BACP4B,YAAYlF,aAAakH,cAAc;4BACvCrC,gBAAgB+J;4BAChB,mFAAmF;4BACnF,kFAAkF;4BAClF,8BAA8B;4BAC9BlJ,OAAOrF,2BAA2B+I,qBAAqB;gCACrD,iCAAiC;gCACjC9B;gCACA6N,OAAOpY,eAAesQ,qBAAqB;gCAC3CmM,WAAWpR;gCACXE;4BACF;wBACF;wBACAvE,KAAK;oBACP;iBACD,GACD,EAAE;mBAEH0V,uBAAuB;oBACxBC,qBACE1T,OAAOyD,YAAY,CAACkQ,UAAU,IAC9B,8DAA8D;oBAC9D3T,OAAOyD,YAAY,CAACmQ,eAAe,IACnC;oBACFxR;oBACAT;oBACA+G;gBACF;gBAEA,kDAAkD;gBAClD,yDAAyD;mBACrD,CAAC1I,OAAOyD,YAAY,CAACpE,cAAc,GACnC;oBACE;wBACE2F,MAAM;wBACN1H,SAAS;4BACP+B,gBAAgB;wBAClB;oBACF;iBACD,GACD,EAAE;mBACF6D,aAAaZ,eACb;oBACE,sEAAsE;oBACtE,mEAAmE;oBACnE,oCAAoC;oBACpC;wBACE6Q,eAAe,IAAIzI,OACjB1T,yBAAyB6c,YAAY;wBAEvC1E,OAAOpY,eAAesQ,qBAAqB;oBAC7C;iBACD,GACD,EAAE;mBACFnE,YACA;oBACE;wBACE,8CAA8C;wBAC9C,kEAAkE;wBAClE4Q,OAAO;4BACL;gCACElH,aAAazV;gCACb6N,MAAM;oCACJ,8DAA8D;oCAC9D,yBAAyB;oCACzBqO,KAAK;wCACHtI;wCACA;4CACEiI,KAAK;gDAACvI;gDAA4B9O;6CAAmB;wCACvD;qCACD;gCACH;gCACA2B,SAAS;oCACP,8DAA8D;oCAC9D,4DAA4D;oCAC5DoC,OAAOrF,2BAA2B+I,qBAAqB;wCACrD9B;wCACA6N,OAAOpY,eAAesQ,qBAAqB;wCAC3CmM,WAAWpR;wCACXE;oCACF;gCACF;4BACF;4BACA;gCACE0C,MAAM+F;gCACN6B,aAAa7V,eAAeyQ,mBAAmB;gCAC/ClK,SAAS;oCACPoC,OAAOrF,2BAA2B+I,qBAAqB;wCACrD9B;wCACA6N,OAAOpY,eAAeyQ,mBAAmB;wCACzCgM,WAAWpR;wCACXE;oCACF;gCACF;4BACF;yBACD;oBACH;oBACA;wBACE0C,MAAM+F;wBACN6B,aAAa7V,eAAe2Q,eAAe;wBAC3CpK,SAAS;4BACPoC,OAAOrF,2BAA2B+I,qBAAqB;gCACrD9B;gCACA6N,OAAOpY,eAAe2Q,eAAe;gCACrC8L,WAAWpR;gCACXE;4BACF;wBACF;oBACF;iBACD,GACD,EAAE;gBACN,iFAAiF;mBAC7EY,aAAajD,OAAOmC,WACpB;oBACE;wBACE4C,MAAMG,cAAcH,IAAI;wBACxBJ,SAAS;4BACP,+CAA+C;4BAC/CO,cAAcP,OAAO;4BACrB+F;4BACAjP;yBACD;wBACDkR,aAAa7V,eAAe2Q,eAAe;wBAC3C3J,KAAKmK;wBACL5K,SAAS;4BACP4B,YAAYlF,aAAakH,cAAc;wBACzC;oBACF;iBACD,GACD,EAAE;gBACN;oBACE4S,OAAO;wBACL;4BACE,GAAG3O,aAAa;4BAChByH,aAAa7V,eAAe0R,OAAO;4BACnC1K,KAAKyK;4BACL,kDAAkD;4BAClD,8DAA8D;4BAC9DuL,QAAQ;gCACNC,KAAK;4BACP;wBACF;wBACA;4BACE,GAAG7O,aAAa;4BAChByH,aAAa7V,eAAekd,OAAO;4BACnClW,KAAKyK;wBAGP;wBACA;4BACExD,MAAMG,cAAcH,IAAI;4BACxB4H,aAAa7V,eAAekR,UAAU;4BACtClK,KAAKiK;4BACL1K,SAAS;gCACP4B,YAAYlF,aAAakH,cAAc;gCACvCrC,gBAAgB+J;gCAChBlJ,OAAOrF,2BAA2B+I,qBAAqB;oCACrD9B;oCACA6N,OAAOpY,eAAekR,UAAU;oCAChCuL,WAAWpR;oCACXE;gCACF;4BACF;wBACF;wBACA;4BACE0C,MAAMG,cAAcH,IAAI;4BACxB4H,aAAa7V,eAAemd,UAAU;4BACtCnW,KAAKgK;4BACLzK,SAAS;gCACP4B,YAAYlF,aAAakH,cAAc;gCACvCrC,gBAAgB+J;gCAChBlJ,OAAOrF,2BAA2B+I,qBAAqB;oCACrD9B;oCACA6N,OAAOpY,eAAemd,UAAU;oCAChCV,WAAWpR;oCACXE;gCACF;4BACF;wBACF;2BACIY,YACA;4BACE;gCACE8B,MAAMG,cAAcH,IAAI;gCACxB4H,aAAazV;gCACbyN,SAASjJ;gCACToC,KAAK+J;4BACP;4BACA;gCACE9C,MAAMG,cAAcH,IAAI;gCACxBmO,eAAe,IAAIzI,OACjB1T,yBAAyB6c,YAAY;gCAEvC9V,KAAK+J;4BACP;4BACA;gCACE9C,MAAMG,cAAcH,IAAI;gCACxB4H,aAAa7V,eAAe2Q,eAAe;gCAC3C,uEAAuE;gCACvE9C,SAASnJ;gCACTsC,KAAKuK;gCACLhL,SAAS;oCACP4B,YAAYlF,aAAakH,cAAc;gCACzC;4BACF;4BACA;gCACE8D,MAAMG,cAAcH,IAAI;gCACxB4H,aAAa7V,eAAeyQ,mBAAmB;gCAC/C5C,SAASjJ;gCACToC,KAAKwK;gCACLjL,SAAS;oCACP4B,YAAYlF,aAAakH,cAAc;gCACzC;4BACF;yBACD,GACD,EAAE;wBACN;4BACE,GAAGiE,aAAa;4BAChBpH,KAAK;mCACAmK;gCACHN,eAAeC,KAAK;gCACpBrC;6BACD,CAACtJ,MAAM,CAACyG;wBACX;qBACD;gBACH;mBAEI,CAAC3C,OAAOmU,MAAM,CAACC,mBAAmB,GAClC;oBACE;wBACEpP,MAAMnF;wBACNxC,QAAQ;wBACRgX,QAAQ;4BAAErB,KAAKla;wBAAa;wBAC5Bwb,YAAY;4BAAEtB,KAAK;gCAAC;6BAAM;wBAAC;wBAC3BG,eAAe;4BACbH,KAAK;gCACH,IAAItI,OAAO1T,yBAAyBsc,QAAQ;gCAC5C,IAAI5I,OAAO1T,yBAAyBoc,aAAa;gCACjD,IAAI1I,OAAO1T,yBAAyBuc,iBAAiB;6BACtD;wBACH;wBACArN,SAAS;4BACPqO,OAAOtU;4BACPiB;4BACAsT,UAAUxU,OAAOwU,QAAQ;4BACzB7C,aAAa3R,OAAO2R,WAAW;wBACjC;oBACF;iBACD,GACD,EAAE;mBACFrP,eACA;oBACE;wBACEhF,SAAS;4BACPoB,UAAU;gCACR7C,SAASV,QAAQmC,OAAO,CAAC;4BAC3B;wBACF;oBACF;iBACD,GACD8E,WACE;oBACE;wBACE9E,SAAS;4BACPoB,UACEsB,OAAOyD,YAAY,CAACgR,qBAAqB,KAAK,QAC1C;gCACEC,QAAQ;gCACRC,QAAQ;gCACRC,WAAW;gCACXle,QAAQ;gCACRme,QAAQ;gCACRC,MAAM;gCACNC,OAAO;gCACPC,IAAI;gCACJpe,MAAM;gCACNqe,UAAU;gCACVpZ,SAAS;gCACTqZ,aAAa;gCACbC,QAAQ;gCACRC,gBAAgB;gCAChBC,KAAK;gCACLC,QAAQ;gCACRC,KAAK;gCACLC,MAAM;gCACNC,IAAI;gCACJC,MAAM;gCACNC,QAAQ;gCACRC,cAAc;4BAChB,IACA;gCACElB,QAAQvZ,QAAQmC,OAAO,CACrB;gCAEFqX,QAAQxZ,QAAQmC,OAAO,CACrB;gCAEFsX,WAAWzZ,QAAQmC,OAAO,CACxB;gCAEF5G,QAAQyE,QAAQmC,OAAO,CACrB;gCAEFuX,QAAQ1Z,QAAQmC,OAAO,CACrB;gCAEFwX,MAAM3Z,QAAQmC,OAAO,CACnB;gCAEFyX,OAAO5Z,QAAQmC,OAAO,CACpB;gCAEF0X,IAAI7Z,QAAQmC,OAAO,CACjB;gCAEF1G,MAAMuE,QAAQmC,OAAO,CACnB;gCAEF2X,UAAU9Z,QAAQmC,OAAO,CACvB;gCAEFzB,SAASV,QAAQmC,OAAO,CAAC;gCACzB,4BAA4B;gCAC5B4X,aAAa/Z,QAAQmC,OAAO,CAC1B;gCAEF6X,QAAQha,QAAQmC,OAAO,CACrB;gCAEF8X,gBAAgBja,QAAQmC,OAAO,CAC7B;gCAEF+X,KAAKla,QAAQmC,OAAO,CAAC;gCACrBgY,QAAQna,QAAQmC,OAAO,CACrB;gCAEFiY,KAAKpa,QAAQmC,OAAO,CAClB;gCAEF,4BAA4B;gCAC5B,+BAA+B;gCAC/BkY,MAAMra,QAAQmC,OAAO,CAAC;gCACtBmY,IAAIta,QAAQmC,OAAO,CACjB;gCAEFoY,MAAMva,QAAQmC,OAAO,CACnB;gCAEFqY,QAAQxa,QAAQmC,OAAO,CACrB;gCAEFsY,cAAcza,QAAQmC,OAAO,CAC3B;4BAEJ;wBACR;oBACF;iBACD,GACD,EAAE;gBACR;oBACE,oEAAoE;oBACpE,6BAA6B;oBAC7B0H,MAAM;oBACN6Q,aAAa;gBACf;gBACA,0EAA0E;gBAC1E,yEAAyE;gBACzE,4GAA4G;gBAC5G,gEAAgE;gBAChE,0DAA0D;gBAC1D,iFAAiF;gBACjF,iFAAiF;gBACjF;oBACE7Q,MAAM;oBACN6Q,aAAa;gBACf;gBACA;oBACE,uEAAuE;oBACvE,uEAAuE;oBACvE,mDAAmD;oBACnD,iEAAiE;oBACjE,mEAAmE;oBACnE,qEAAqE;oBACrE,4DAA4D;oBAC5D7Q,MAAM;oBACNjH,KAAK,CAAC,EAAEoV,aAAa,EAA6B;4BAE9CA;wBADF,MAAM2C,QAAQ,AACZ3C,CAAAA,EAAAA,uBAAAA,cAAcpF,KAAK,CAAC,uCAApBoF,oBAAwC,CAAC,EAAE,KAAI,EAAC,EAChDnX,KAAK,CAAC;wBAER,OAAO;4BACL;gCACEqB,QAAQ;gCACR6I,SAAS;oCACP4P;oCACAxP,aAAa1P,KAAK2M,IAAI,CACpBxD,KACAC,CAAAA,0BAAAA,OAAQsD,OAAO,KAAI,SACnB,SACA;gCAEJ;gCACA,gEAAgE;gCAChE,2DAA2D;gCAC3D,gBAAgB;gCAChByS,OAAO,wBAAwB5C;4BACjC;yBACD;oBACH;gBACF;gBACA;oBACE7V,SAAS;wBACPoC,OAAO;4BACLsW,MAAMpb;wBACR;oBACF;gBACF;aACD;QACH;QACAuO,SAAS;YACP3G,gBACE,IAAIL,QAAQ8T,6BAA6B,CACvC,6BACA,SAAU7G,QAAQ;gBAChB,MAAM8G,aAAatf,KAAKuf,QAAQ,CAC9B/G,SAAS3C,OAAO,EAChB;gBAEF,MAAM0C,QAAQC,SAAS1C,WAAW,CAACE,WAAW;gBAC9C,IAAIwJ;gBAEJ,OAAQjH;oBACN,KAAKpY,eAAeyQ,mBAAmB;oBACvC,KAAKzQ,eAAesQ,qBAAqB;oBACzC,KAAKtQ,eAAe2Q,eAAe;oBACnC,KAAK3Q,eAAesf,aAAa;wBAC/BD,UAAU;wBACV;oBACF,KAAK;oBACL,KAAKxS;oBACL;wBACEwS,UAAU;gBACd;gBACAhH,SAAS3C,OAAO,GAAG,CAAC,+BAA+B,EAAE2J,QAAQ,mBAAmB,EAAEF,YAAY;YAChG;YAEJjW,OAAO,IAAItG,wBAAwB;gBAAE2c,gBAAgB;YAAE;YACvDrW,OACEmC,YACCM,CAAAA,WAEG,IAAK3H,CAAAA,uBAAsB,EAAU;gBACnCwb,cAAc;gBACdC,aAAa;gBACbC,SAAS;YACX,KACA,IAAIlgB,0BAA0BI,QAAO;YAC3C,6GAA6G;YAC5GyL,CAAAA,YAAYE,YAAW,KACtB,IAAIH,QAAQuU,aAAa,CAAC;gBACxB,0CAA0C;gBAC1CC,QAAQ;oBAACxb,QAAQmC,OAAO,CAAC;oBAAW;iBAAS;gBAC7C,sDAAsD;gBACtD,GAAI8E,YAAY;oBAAEvG,SAAS;wBAACV,QAAQmC,OAAO,CAAC;qBAAW;gBAAC,CAAC;YAC3D;YAEF,IAAKrC,CAAAA,mBAAkB,EAAE2b,YAAY,CACnC/f,aAAa;gBACXggB,aAAa;gBACb7W;gBACAC;gBACAqD;gBACAwT,aAAa/W;gBACbgC;gBACAe;gBACAV;gBACAE;gBACAE;gBACAZ;gBACAmV,sBAAsB/U;gBACtBT;YACF;YAEFa,YACE,IAAIxJ,oBAAoB;gBACtBqW,UAAUpX;gBACVwJ;gBACAM;gBACAqV,cAAc,CAAC,OAAO,EAAEpf,mCAAmC,GAAG,CAAC;gBAC/DqI;YACF;YACF,oDAAoD;YACpD,CAACyC,YAAaN,CAAAA,YAAYE,YAAW,KAAM,IAAI7J;YAC/C+J,gBACE,CAACvC,OACD,IAAK,AACH9E,CAAAA,QAAQ,kDAAiD,EAExD8b,sBAAsB,CACvB;gBACE7Q,SAASrG;gBACT4B,QAAQA;gBACRN,UAAUA;gBACV8K,cAAcnM,OAAOyD,YAAY,CAAC0I,YAAY;gBAC9C+K,uBAAuBlX,OAAOkX,qBAAqB;gBACnDC,eAAejU;gBACfkU,cAAc,EAAE;gBAChBlW;YACF;YAEJ,4EAA4E;YAC5E,yEAAyE;YACzE,0EAA0E;YAC1E,kEAAkE;YAClElB,OAAOqX,2BAA2B,IAChC,IAAIlV,QAAQmV,YAAY,CAAC;gBACvBC,gBAAgB;gBAChBC,eAAe;YACjB;eACEvX,MACA,AAAC,CAAA;gBACC,0FAA0F;gBAC1F,qGAAqG;gBACrG,MAAM,EAAEwX,6BAA6B,EAAE,GACrCtc,QAAQ;gBACV,MAAMuc,aAAoB;oBACxB,IAAID,8BAA8B;wBAChCtQ,kBAAkBjE;oBACpB;iBACD;gBAED,IAAId,YAAYE,cAAc;oBAC5BoV,WAAWjT,IAAI,CAAC,IAAItC,QAAQwV,0BAA0B;gBACxD;gBAEA,OAAOD;YACT,CAAA,MACA,EAAE;YACN,CAACzX,OACC,IAAIkC,QAAQmV,YAAY,CAAC;gBACvBC,gBAAgB;gBAChBC,eAAe;YACjB;YACF3U,2BACE,IAAInK,oBAAoB;gBACtBuH;gBACAkX,eAAejU;gBACf0U,eAAetV;gBACfgB,SAAS,CAACrD,MAAMqD,UAAUM;YAC5B;YACF,iEAAiE;YACjE,wDAAwD;YACxDtB,gBACE,IAAIlK,iBAAiB;gBACnB6H;gBACA4X,YAAY,CAAC5X,OAAO,CAAC,GAACD,2BAAAA,OAAOyD,YAAY,CAACqU,GAAG,qBAAvB9X,yBAAyB+X,SAAS;gBACxDxW;gBACAyW,kBAAkB;oBAChBC,iBAAiBjX;oBACjBkX,oCAAoCjX;oBACpCkX,wBAAwBlW,aAAamW,aAAa;oBAClDC,iCAAiCpW,aAAaqW,qBAAqB;oBACnEC,oCACEtW,aAAauW,wBAAwB;gBACzC;YACF;YACFpW,YACE,IAAI7J,oBAAoB;gBACtByI;gBACAO;gBACAH;gBACA+V,eAAejU;gBACfpB;YACF;YACFY,WACI,IAAI1H,sBAAsB;gBAAE0G;YAAe,KAC3C,IAAI/I,gBAAgB;gBAAE+I;gBAAgB0E,SAASrG;YAAI;YACvD,IAAIlH;YACJuJ,YACE,IAAIrJ,eAAe;gBACjB,yDAAyD;gBACzD0f,UAAUtd,QAAQmC,OAAO,CAAC;gBAC1Bob,UAAU7c,QAAQC,GAAG,CAAC6c,cAAc;gBACpCzO,MAAM,CAAC,uBAAuB,EAAEjK,MAAM,KAAK,UAAU,GAAG,CAAC;gBACzDiQ,UAAU;gBACVlM,MAAM;oBACJ,CAACvM,6CAA6C,EAAE;oBAChD,gCAAgC;oBAChCmhB,WAAW;gBACb;YACF;YACF1V,aAAad,YAAY,IAAI7I,uBAAuB;gBAAE0G;YAAI;YAC1DiD,aACGd,CAAAA,WACG,IAAIpJ,8BAA8B;gBAChCiH;gBACA0B;gBACAkX,uBAAuB,CAAC,CAAC7Y,OAAOyD,YAAY,CAACqV,SAAS;YACxD,KACA,IAAI7f,wBAAwB;gBAC1B0I;gBACA1B;gBACAqC;gBACArB;YACF,EAAC;YACPiC,aACE,CAACd,YACD,IAAIhJ,gBAAgB;gBAClB2G;gBACAuD,SAAStD,OAAOsD,OAAO;gBACvB3B;gBACA1B;gBACAqC;gBACAoG,gBAAgB1I,OAAO0I,cAAc;gBACrCqQ,iBAAiB/Y,OAAOyD,YAAY,CAACuV,SAAS;gBAC9CxX;gBACAC;YACF;YACF,CAACxB,OACCmC,YACA,CAAC,GAACpC,4BAAAA,OAAOyD,YAAY,CAACqU,GAAG,qBAAvB9X,0BAAyB+X,SAAS,KACpC,IAAIve,2BAA2BwG,OAAOyD,YAAY,CAACqU,GAAG,CAACC,SAAS;YAClE3V,YACE,IAAI3I,uBAAuB;gBACzBkI;YACF;YACF,CAAC1B,OACCmC,YACApC,OAAOyD,YAAY,CAACwV,WAAW,IAC9BvW,CAAAA,WACG,IAAK5H,CAAAA,eAAc,EAAEoe,WAAW,CAACze,iBAAiB,CAAE;gBAClD0e,QAAQnZ,OAAOyD,YAAY,CAACwV,WAAW,KAAK;gBAC5CG,QAAQ;YACV,KACA,IAAI3e,kBACFuF,OAAOyD,YAAY,CAACwV,WAAW,KAAK,SACtC;YACNzN;YACA,CAACvL,OACCuC,gBACA,IAAI,AACFrH,CAAAA,QAAQ,sDAAqD,EAC7DsQ,eAAe,CAAC,IAAIC;YACxBR,mCACE,IAAI,AACF/P,CAAAA,QAAQ,iDAAgD,EACxD4T,OAAO,CAAC;gBACR7N;gBACA,GAAGlB,OAAOyD,YAAY,CAAC0H,mBAAmB;YAC5C;SACH,CAACjP,MAAM,CAACyG;IACX;IAEA,wCAAwC;IACxC,mEAAmE;IACnE,IAAIvC,mBAAmB,CAACA,gBAAgBiZ,UAAU,EAAE;YAClD7b,gCAAAA;SAAAA,0BAAAA,cAAcF,OAAO,sBAArBE,iCAAAA,wBAAuBiB,OAAO,qBAA9BjB,+BAAgCiH,IAAI,CAACrE,gBAAgBkZ,OAAO;IAC9D;KAIA9b,yBAAAA,cAAcF,OAAO,sBAArBE,iCAAAA,uBAAuB2L,OAAO,qBAA9B3L,+BAAgC+b,OAAO,CACrC,IAAI/gB,oBACF0H,CAAAA,6BAAAA,6BAAAA,SAAU2G,eAAe,qBAAzB3G,2BAA2B0J,KAAK,KAAI,CAAC,GACrCxJ;IAIJ,MAAM8B,iBAAiB1E;IAEvB,IAAI8E,cAAc;YAChBJ,8BAAAA,wBAMAA,+BAAAA,yBAMAA,+BAAAA;SAZAA,yBAAAA,eAAexF,MAAM,sBAArBwF,+BAAAA,uBAAuBvE,KAAK,qBAA5BuE,6BAA8BqX,OAAO,CAAC;YACpCvU,MAAM;YACN3H,QAAQ;YACRV,MAAM;YACNwW,eAAe;QACjB;SACAjR,0BAAAA,eAAexF,MAAM,sBAArBwF,gCAAAA,wBAAuBvE,KAAK,qBAA5BuE,8BAA8BqX,OAAO,CAAC;YACpCjF,YAAY;YACZjX,QAAQ;YACRV,MAAM;YACNwS,OAAOpY,eAAeyiB,SAAS;QACjC;SACAtX,0BAAAA,eAAexF,MAAM,sBAArBwF,gCAAAA,wBAAuBvE,KAAK,qBAA5BuE,8BAA8BqX,OAAO,CAAC;YACpC3M,aAAa7V,eAAeyiB,SAAS;YACrC7c,MAAM;QACR;IACF;IAEAuF,eAAegX,WAAW,GAAG;QAC3BO,QAAQ;QACRC,iBAAiB;QACjBC,WAAW3b,MAAMC,OAAO,CAAC+B,OAAOyD,YAAY,CAACmW,UAAU,IACnD;YACEC,aAAa7Z,OAAOyD,YAAY,CAACmW,UAAU;YAC3CE,eAAeljB,KAAK2M,IAAI,CAACxD,KAAK;YAC9Bga,kBAAkBnjB,KAAK2M,IAAI,CAACxD,KAAK;QACnC,IACAC,OAAOyD,YAAY,CAACmW,UAAU,GAC5B;YACEE,eAAeljB,KAAK2M,IAAI,CAACxD,KAAK;YAC9Bga,kBAAkBnjB,KAAK2M,IAAI,CAACxD,KAAK;YACjC,GAAGC,OAAOyD,YAAY,CAACmW,UAAU;QACnC,IACAhW;IACR;IAEA1B,eAAexF,MAAM,CAAEqX,MAAM,GAAG;QAC9BiG,YAAY;YACVhG,KAAK;QACP;IACF;IACA9R,eAAexF,MAAM,CAAEud,SAAS,GAAG;QACjCC,OAAO;YACLjL,UAAU;QACZ;IACF;IAEA,IAAI,CAAC/M,eAAeuP,MAAM,EAAE;QAC1BvP,eAAeuP,MAAM,GAAG,CAAC;IAC3B;IACA,IAAIrP,UAAU;QACZF,eAAeuP,MAAM,CAAC0I,YAAY,GAAG;IACvC;IAEA,IAAI/X,YAAYE,cAAc;QAC5BJ,eAAeuP,MAAM,CAAC2I,mBAAmB,GAAG;YAAC;SAAS;IACxD;IAEA,iDAAiD;IACjD,wDAAwD;IACxD,oDAAoD;IACpDlY,eAAemY,QAAQ,GAAG,CAAC;IAC3B,IAAIxe,QAAQ4K,QAAQ,CAACD,GAAG,KAAK,KAAK;QAChCtE,eAAemY,QAAQ,CAACC,YAAY,GAAG;YACrC;SACD;IACH,OAAO;QACLpY,eAAemY,QAAQ,CAACC,YAAY,GAAG;YAAC;SAA+B;IACzE;IACA,IAAIze,QAAQ4K,QAAQ,CAACD,GAAG,KAAK,KAAK;QAChCtE,eAAemY,QAAQ,CAACE,cAAc,GAAG;YACvC;SACD;IACH;IAEA,IAAIta,KAAK;QACP,IAAI,CAACiC,eAAeqL,YAAY,EAAE;YAChCrL,eAAeqL,YAAY,GAAG,CAAC;QACjC;QAEA,2EAA2E;QAC3E,2CAA2C;QAC3C,IAAI,CAACrK,WAAW;YACdhB,eAAeqL,YAAY,CAACiN,eAAe,GAAG;QAChD;QACAtY,eAAeqL,YAAY,CAACkN,WAAW,GAAG;IAC5C;IAEA,MAAMC,aAAaC,KAAKC,SAAS,CAAC;QAChCrW,sBAAsB,EAAEvE,2BAAAA,wBAAAA,OAAQyD,YAAY,qBAApBzD,sBAAsBuE,sBAAsB;QACpE8F,aAAarK,OAAOqK,WAAW;QAC/B3B,gBAAgBA;QAChBmS,eAAe7a,OAAO6a,aAAa;QACnCC,uBACE9a,OAAO+a,aAAa,KAAK,QACrBnX,YACA5D,OAAO+a,aAAa,CAACC,QAAQ;QACnCC,6BAA6B,CAAC,CAACjb,OAAOib,2BAA2B;QACjEC,iBAAiBlb,OAAOkb,eAAe;QACvCC,aAAanb,OAAOyD,YAAY,CAAC0X,WAAW;QAC5CC,mBAAmBpb,OAAOyD,YAAY,CAAC2X,iBAAiB;QACxDC,mBAAmBrb,OAAOyD,YAAY,CAAC4X,iBAAiB;QACxD7G,UAAUxU,OAAOwU,QAAQ;QACzB6C,6BAA6BrX,OAAOqX,2BAA2B;QAC/D1F,aAAa3R,OAAO2R,WAAW;QAC/BxO;QACAyU,eAAetV;QACfhB;QACA3K,SAAS,CAAC,CAACqJ,OAAOrJ,OAAO;QACzBmM;QACAwY,WAAW9X;QACXsI,aAAa,GAAE9L,oBAAAA,OAAO0E,QAAQ,qBAAf1E,kBAAiB8L,aAAa;QAC7CD,qBAAqB,GAAE7L,oBAAAA,OAAO0E,QAAQ,qBAAf1E,kBAAiB6L,qBAAqB;QAC7DD,gBAAgB,GAAE5L,oBAAAA,OAAO0E,QAAQ,qBAAf1E,kBAAiB4L,gBAAgB;QACnDD,KAAK,GAAE3L,oBAAAA,OAAO0E,QAAQ,qBAAf1E,kBAAiB2L,KAAK;QAC7BK,OAAO,GAAEhM,oBAAAA,OAAO0E,QAAQ,qBAAf1E,kBAAiBgM,OAAO;QACjCrF,mBAAmB3G,OAAO2G,iBAAiB;QAC3C4U,iBAAiBvb,OAAOmU,MAAM,CAACqH,UAAU;QACzCC,qBAAqBzb,OAAOyD,YAAY,CAACgY,mBAAmB;QAC5DC,kBAAkB1b,OAAOyD,YAAY,CAACiY,gBAAgB;QACtDnV,yBAAyBtF;IAC3B;IAEA,MAAMJ,QAAa;QACjBlE,MAAM;QACN,mFAAmF;QACnFgf,sBAAsB1b,MAAM,IAAI2b;QAChC,YAAY;QACZ,kHAAkH;QAClH,qBAAqB;QACrB,iDAAiD;QACjDtgB,SAAS,GAAGsX,UAAU,CAAC,EAAE/W,QAAQC,GAAG,CAAC6c,cAAc,CAAC,CAAC,EAAE+B,YAAY;QACnE5Z,gBAAgBlK,KAAK2M,IAAI,CAACD,SAAS,SAAS;QAC5C,gIAAgI;QAChI,8GAA8G;QAC9G,yGAAyG;QACzG,kEAAkE;QAClEuY,aAAa5b,MAAM,SAAS;IAC9B;IAEA,oFAAoF;IACpF,IAAID,OAAOrJ,OAAO,IAAIqJ,OAAOqJ,UAAU,EAAE;QACvCxI,MAAMib,iBAAiB,GAAG;YACxB9b,QAAQ;gBAACA,OAAOqJ,UAAU;aAAC;YAC3B,uGAAuG;YACvG0S,gBAAgB,EAAE;QACpB;IACF,OAAO;QACLlb,MAAMib,iBAAiB,GAAG;YACxB,uGAAuG;YACvGC,gBAAgB,EAAE;QACpB;IACF;KACA7Z,0BAAAA,eAAeiH,OAAO,qBAAtBjH,wBAAwBuC,IAAI,CAAC,CAACC;QAC5BA,SAASsX,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,2BAA2B,CAACC;YAClD,MAAML,oBAAoBK,MAAMC,WAAW,CAACN,iBAAiB;YAC7D,MAAMO,cAAczlB,KAAK0lB,OAAO,CAACnhB,QAAQmC,OAAO,CAAC;YACjD,sFAAsF;YACtF,2EAA2E;YAC3E,KAAK,MAAMif,OAAOT,kBAAmB;gBACnC,IAAIS,IAAIjN,UAAU,CAAC+M,cAAc;oBAC/BP,kBAAkBU,MAAM,CAACD;gBAC3B;YACF;QACF;IACF;IAEAra,eAAerB,KAAK,GAAGA;IAEvB,IAAIhF,QAAQC,GAAG,CAAC2gB,oBAAoB,EAAE;QACpC,MAAMC,QAAQ7gB,QAAQC,GAAG,CAAC2gB,oBAAoB,CAACjY,QAAQ,CAAC;QACxD,MAAMmY,gBACJ9gB,QAAQC,GAAG,CAAC2gB,oBAAoB,CAACjY,QAAQ,CAAC;QAC5C,MAAMoY,gBACJ/gB,QAAQC,GAAG,CAAC2gB,oBAAoB,CAACjY,QAAQ,CAAC;QAC5C,MAAMqY,gBACJhhB,QAAQC,GAAG,CAAC2gB,oBAAoB,CAACjY,QAAQ,CAAC;QAC5C,MAAMsY,gBACJjhB,QAAQC,GAAG,CAAC2gB,oBAAoB,CAACjY,QAAQ,CAAC;QAE5C,MAAMuY,UACJ,AAACJ,iBAAiBva,YAAcwa,iBAAiB/Z;QACnD,MAAMma,UACJ,AAACH,iBAAiBza,YAAc0a,iBAAiBja;QAEnD,MAAMoa,aAAa,CAACP,SAAS,CAACK,WAAW,CAACC;QAE1C,IAAIC,cAAcP,OAAO;YACvBxa,eAAegb,qBAAqB,GAAG;gBACrCC,OAAO;gBACPC,OAAO;YACT;QACF;QAEA,IAAIH,cAAcF,SAAS;YACzB7a,eAAeiH,OAAO,CAAE1E,IAAI,CAAC,CAACC;gBAC5BA,SAASsX,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,wBAAwB,CAACC;oBAC/Crf,QAAQugB,GAAG,CACTlB,MAAMmB,QAAQ,CAAC;wBACbC,QAAQ;wBACRC,SAASP,aAAa,QAAQ;oBAChC;gBAEJ;YACF;QACF,OAAO,IAAID,SAAS;YAClB9a,eAAeiH,OAAO,CAAE1E,IAAI,CAAC,CAACC;gBAC5BA,SAASsX,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,wBAAwB,CAACC;oBAC/Crf,QAAQugB,GAAG,CACTlB,MAAMmB,QAAQ,CAAC;wBACbG,QAAQ;wBACRF,QAAQ;wBACRG,SAAS;oBACX;gBAEJ;YACF;QACF;QAEA,IAAIX,SAAS;YACX,MAAMY,iBACJhnB,QAAQgnB,cAAc;YACxBzb,eAAeiH,OAAO,CAAE1E,IAAI,CAC1B,IAAIkZ,eAAe;gBACjBZ,SAAS;YACX;YAEF7a,eAAe6a,OAAO,GAAG;QAC3B;IACF;IAEAvf,gBAAgB,MAAMrF,mBAAmBqF,eAAe;QACtD6C;QACAud,eAAe7d;QACf8d,eAAexc,WACX,IAAIqJ,OAAO5T,mBAAmBF,KAAK2M,IAAI,CAAClC,UAAU,CAAC,IAAI,CAAC,MACxDuC;QACJV;QACA4a,eAAe7d;QACfkG,UAAUtD;QACV+U,eAAetV;QACfyb,WAAW3b,YAAYE;QACvBqP,aAAa3R,OAAO2R,WAAW,IAAI;QACnCqM,cAAche,OAAOge,YAAY;QACjCC,aAAaje,OAAOie,WAAW;QAC/BhD,6BAA6Bjb,OAAOib,2BAA2B;QAC/DiD,QAAQle,OAAOke,MAAM;QACrBza,cAAczD,OAAOyD,YAAY;QACjC2Q,qBAAqBpU,OAAOmU,MAAM,CAACC,mBAAmB;QACtDhQ,mBAAmBpE,OAAOoE,iBAAiB;QAC3CsX,kBAAkB1b,OAAOyD,YAAY,CAACiY,gBAAgB;IACxD;IAEA,0BAA0B;IAC1Ble,cAAcqD,KAAK,CAACqJ,IAAI,GAAG,GAAG1M,cAAc0M,IAAI,CAAC,CAAC,EAAE1M,cAAc2gB,IAAI,GACpE/c,gBAAgB,cAAc,IAC9B;IAEF,IAAInB,KAAK;QACP,IAAIzC,cAAcd,MAAM,EAAE;YACxBc,cAAcd,MAAM,CAAC0hB,WAAW,GAAG,CAAC1hB,SAClC,CAAC6D,mBAAmByE,IAAI,CAACtI,OAAO0S,QAAQ;QAC5C,OAAO;YACL5R,cAAcd,MAAM,GAAG;gBACrB0hB,aAAa,CAAC1hB,SAAgB,CAAC6D,mBAAmByE,IAAI,CAACtI,OAAO0S,QAAQ;YACxE;QACF;IACF;IAEA,IAAIiP,kBAAkB7gB,cAAcX,OAAO;IAC3C,IAAI,OAAOmD,OAAOrJ,OAAO,KAAK,YAAY;YACd6G,wBA0CtB0E,6BAKKA;QA/CT,MAAMoc,qBAAoB9gB,yBAAAA,cAAc2L,OAAO,qBAArB3L,uBAAuBwF,MAAM;QAEvDxF,gBAAgBwC,OAAOrJ,OAAO,CAAC6G,eAAe;YAC5CuC;YACAE;YACAkG,UAAUtD;YACV7B;YACAhB;YACA4H;YACA2W,YAAYliB,OAAO8N,IAAI,CAAChJ,aAAa6B,MAAM;YAC3CrM,SAASwL;YACT,GAAIU,0BACA;gBACE2b,aAAalc,eAAe,SAAS;YACvC,IACA,CAAC,CAAC;QACR;QAEA,IAAIkJ,mBAAmB8S,mBAAmB;gBACf9gB;YAAzB,MAAMihB,oBAAmBjhB,0BAAAA,cAAc2L,OAAO,qBAArB3L,wBAAuBwF,MAAM;YACtD,IAAIyb,kBAAkB;gBACpB,MAAMC,iBAAiBD,qBAAqBH;gBAC5C9S,gBAAgBmT,QAAQ,CAAC,kBAAkBD,iBAAiB,IAAI;YAClE;QACF;QAEA,IAAI,CAAClhB,eAAe;YAClB,MAAM,qBAGL,CAHK,IAAIjC,MACR,CAAC,6GAA6G,EAAEyE,OAAO4e,cAAc,CAAC,GAAG,CAAC,GACxI,iFAFE,qBAAA;uBAAA;4BAAA;8BAAA;YAGN;QACF;QAEA,IAAI3e,OAAOoe,oBAAoB7gB,cAAcX,OAAO,EAAE;YACpDW,cAAcX,OAAO,GAAGwhB;YACxBzhB,qBAAqByhB;QACvB;QAEA,wDAAwD;QACxD,MAAMnc,iBAAiB1E;QAEvB,0EAA0E;QAC1E,IAAI0E,EAAAA,8BAAAA,eAAegX,WAAW,qBAA1BhX,4BAA4B2c,eAAe,MAAK,MAAM;YACxD3c,eAAegX,WAAW,CAAC2F,eAAe,GAAG;gBAC3CC,SAAS;YACX;QACF,OAAO,IACL,SAAO5c,+BAAAA,eAAegX,WAAW,qBAA1BhX,6BAA4B2c,eAAe,MAAK,YACvD3c,eAAegX,WAAW,CAAC2F,eAAe,CAACC,OAAO,KAAK,OACvD;YACA5c,eAAegX,WAAW,CAAC2F,eAAe,CAACC,OAAO,GAAG;QACvD;QAEA,IAAI,OAAO,AAACthB,cAAsBuhB,IAAI,KAAK,YAAY;YACrDjiB,QAAQC,IAAI,CACV;QAEJ;IACF;IACA,MAAMY,QAAQH,EAAAA,wBAAAA,cAAcd,MAAM,qBAApBc,sBAAsBG,KAAK,KAAI,EAAE;IAE/C,MAAMqhB,gBAAgBrhB,MAAMshB,IAAI,CAC9B,CAACphB,OACC,AAACA,QACC,OAAOA,SAAS,YAChBA,KAAKR,MAAM,KAAK,uBAChB,UAAUQ,QACVA,KAAKmH,IAAI,YAAY0F,UACrB7M,KAAKmH,IAAI,CAACA,IAAI,CAAC,WACjB;IAGJ,IAAIga,iBAAiB9b,WAAW;QAC9B,wEAAwE;QACxE,qEAAqE;QACrE,0CAA0C;QAC1CvF,MAAM8G,IAAI,CAAC;YACTO,MAAMga,cAAcha,IAAI;YACxB8O,OAAO;gBACL/c,eAAesQ,qBAAqB;gBACpCtQ,eAAeyQ,mBAAmB;gBAClCzQ,eAAe2Q,eAAe;aAC/B,CAAC/G,GAAG,CAAC,CAACwO,QAAW,CAAA;oBAChBvC,aAAauC;oBACb7R,SAAS;wBACPoC,OAAOrF,2BAA2B+I,qBAAqB;4BACrD9B;4BACA6N;4BACAqE,WAAWpR;4BACXE;wBACF;oBACF;gBACF,CAAA;QACF;IACF;IAEA,IAAI,CAACtC,OAAOmU,MAAM,CAACC,mBAAmB,EAAE;QACtC,MAAM8K,gBAAgBvhB,MAAMshB,IAAI,CAC9B,CAACphB,OACCA,QAAQ,OAAOA,SAAS,YAAYA,KAAKR,MAAM,KAAK;QAExD,IAAI2hB,iBAAiBE,iBAAiB,OAAOA,kBAAkB,UAAU;YACvE,uDAAuD;YACvD,wDAAwD;YACxD,8CAA8C;YAC9CA,cAAcla,IAAI,GAAG;QACvB;IACF;IAEA,IACEhF,OAAOyD,YAAY,CAAC0b,SAAS,MAC7B3hB,yBAAAA,cAAcd,MAAM,qBAApBc,uBAAsBG,KAAK,KAC3BH,cAAc2L,OAAO,EACrB;QACA,kEAAkE;QAClE,iEAAiE;QACjE,kJAAkJ;QAClJ,MAAMiW,oBAAoB;YAAC;SAA8B;QACzD,MAAMC,aAAa;YACjBza,SAASwa;YACT/K,QAAQ+K;YACRziB,MAAM;QACR;QAEA,MAAM2iB,WAAW,EAAE;QACnB,MAAMC,aAAa,EAAE;QAErB,KAAK,MAAM1hB,QAAQL,cAAcd,MAAM,CAACiB,KAAK,CAAE;YAC7C,IAAI,CAACE,QAAQ,OAAOA,SAAS,UAAU;YACvC,IAAIA,KAAKP,OAAO,EAAE;gBAChBgiB,SAAS7a,IAAI,CAAC5G;YAChB,OAAO;gBACL,IACEA,KAAKiW,KAAK,IACV,CAAEjW,CAAAA,KAAKmH,IAAI,IAAInH,KAAK+G,OAAO,IAAI/G,KAAKuR,QAAQ,IAAIvR,KAAKwW,MAAM,AAAD,GAC1D;oBACAxW,KAAKiW,KAAK,CAAClW,OAAO,CAAC,CAACO,IAAMohB,WAAW9a,IAAI,CAACtG;gBAC5C,OAAO;oBACLohB,WAAW9a,IAAI,CAAC5G;gBAClB;YACF;QACF;QAEAL,cAAcd,MAAM,CAACiB,KAAK,GAAG;eACvB2hB;YACJ;gBACExL,OAAO;uBAAIyL;oBAAYF;iBAAW;YACpC;SACD;IACH;IAEA,8DAA8D;IAC9D,IAAI,OAAOrf,OAAOwf,oBAAoB,KAAK,YAAY;QACrD,MAAMtZ,UAAUlG,OAAOwf,oBAAoB,CAAC;YAC1ClO,cAAc9T,cAAc8T,YAAY;QAC1C;QACA,IAAIpL,QAAQoL,YAAY,EAAE;YACxB9T,cAAc8T,YAAY,GAAGpL,QAAQoL,YAAY;QACnD;IACF;IAEA,SAASmO,YAAY5hB,IAA0C;QAC7D,IAAI,CAACA,MAAM;YACT,OAAO;QACT;QAEA,MAAM6hB,YAAY;YAChB;YACA;YACA;YACA;YACA;SACD;QAED,IAAI7hB,gBAAgB6M,UAAUgV,UAAUxhB,IAAI,CAAC,CAACyhB,QAAU9hB,KAAKmH,IAAI,CAAC2a,SAAS;YACzE,OAAO;QACT;QAEA,IAAI,OAAO9hB,SAAS,YAAY;YAC9B,IACE6hB,UAAUxhB,IAAI,CAAC,CAACyhB;gBACd,IAAI;oBACF,IAAI9hB,KAAK8hB,QAAQ;wBACf,OAAO;oBACT;gBACF,EAAE,OAAM,CAAC;gBACT,OAAO;YACT,IACA;gBACA,OAAO;YACT;QACF;QAEA,IAAI3hB,MAAMC,OAAO,CAACJ,SAASA,KAAKK,IAAI,CAACuhB,cAAc;YACjD,OAAO;QACT;QAEA,OAAO;IACT;IAEA,MAAMG,mBACJpiB,EAAAA,yBAAAA,cAAcd,MAAM,sBAApBc,8BAAAA,uBAAsBG,KAAK,qBAA3BH,4BAA6BU,IAAI,CAC/B,CAACL,OAAc4hB,YAAY5hB,KAAKmH,IAAI,KAAKya,YAAY5hB,KAAKwH,OAAO,OAC9D;IAEP,IAAIua,kBAAkB;YAYhBpiB,8BAAAA,wBAWAA,yBAMAA,uCAAAA;QA5BJ,kCAAkC;QAClC,IAAIqF,yBAAyB;YAC3B/F,QAAQC,IAAI,CACVvG,OAAOC,KAAK,gBACVA,KACE,8FAEF;QAEN;QAEA,KAAI+G,yBAAAA,cAAcd,MAAM,sBAApBc,+BAAAA,uBAAsBG,KAAK,qBAA3BH,6BAA6BwF,MAAM,EAAE;YACvC,6BAA6B;YAC7BxF,cAAcd,MAAM,CAACiB,KAAK,CAACC,OAAO,CAAC,CAACO;gBAClC,IAAI,CAACA,KAAK,OAAOA,MAAM,UAAU;gBACjC,IAAIH,MAAMC,OAAO,CAACE,EAAE2V,KAAK,GAAG;oBAC1B3V,EAAE2V,KAAK,GAAG3V,EAAE2V,KAAK,CAAC5X,MAAM,CACtB,CAAC2jB,IAAM,AAACA,CAAS,CAACC,OAAOC,GAAG,CAAC,qBAAqB,KAAK;gBAE3D;YACF;QACF;QACA,KAAIviB,0BAAAA,cAAc2L,OAAO,qBAArB3L,wBAAuBwF,MAAM,EAAE;YACjC,gCAAgC;YAChCxF,cAAc2L,OAAO,GAAG3L,cAAc2L,OAAO,CAACjN,MAAM,CAClD,CAACC,IAAM,AAACA,EAAU6jB,iBAAiB,KAAK;QAE5C;QACA,KAAIxiB,8BAAAA,cAAc+P,YAAY,sBAA1B/P,wCAAAA,4BAA4B4S,SAAS,qBAArC5S,sCAAuCwF,MAAM,EAAE;YACjD,uBAAuB;YACvBxF,cAAc+P,YAAY,CAAC6C,SAAS,GAClC5S,cAAc+P,YAAY,CAAC6C,SAAS,CAAClU,MAAM,CACzC,CAAC+jB,IAAM,AAACA,EAAUD,iBAAiB,KAAK;QAE9C;IACF;IAEA,yEAAyE;IACzE,IAAI/f,OAAOmC,UAAU;QACnB7E,mBAAmBC,eAAeoK,eAAeC,KAAK;IACxD;IAEA,2CAA2C;IAC3C,4CAA4C;IAC5C,4CAA4C;IAC5C,0BAA0B;IAC1B,MAAMqY,gBAAqB1iB,cAAcsL,KAAK;IAC9C,IAAI,OAAOoX,kBAAkB,aAAa;QACxC,MAAMC,eAAe;YACnB,MAAMrX,QACJ,OAAOoX,kBAAkB,aACrB,MAAMA,kBACNA;YACN,0CAA0C;YAC1C,IACEnX,iBACA/K,MAAMC,OAAO,CAAC6K,KAAK,CAAC,UAAU,KAC9BA,KAAK,CAAC,UAAU,CAAC9F,MAAM,GAAG,GAC1B;gBACA,MAAMod,eAAerX,aAAa,CAChCxR,iCACD;gBACDuR,KAAK,CAACvR,iCAAiC,GAAG;uBACrCuR,KAAK,CAAC,UAAU;oBACnBsX;iBACD;YACH;YACA,OAAOtX,KAAK,CAAC,UAAU;YAEvB,KAAK,MAAMoB,QAAQ7N,OAAO8N,IAAI,CAACrB,OAAQ;gBACrCA,KAAK,CAACoB,KAAK,GAAGjS,mBAAmB;oBAC/BooB,OAAOvX,KAAK,CAACoB,KAAK;oBAClBhJ;oBACAgJ;oBACAhH;gBACF;YACF;YAEA,OAAO4F;QACT;QACA,sCAAsC;QACtCtL,cAAcsL,KAAK,GAAGqX;IACxB;IAEA,IAAI,CAAClgB,OAAO,OAAOzC,cAAcsL,KAAK,KAAK,YAAY;QACrD,6BAA6B;QAC7BtL,cAAcsL,KAAK,GAAG,MAAMtL,cAAcsL,KAAK;IACjD;IAEA,OAAOtL;AACT;AAEA,SAASiW,uBAAuB,EAC9BC,mBAAmB,EACnBtR,QAAQ,EACRT,MAAM,EACN+G,cAAc,EAMf;IACC,8CAA8C;IAC9C,MAAM4X,uBAAuB1pB,KAAK2M,IAAI,CAAC3I,mBAAmB;IAE1D,MAAM2lB,0BAA0B,CAACtN;QAC/B,OAAO;YACL7D,UAAUkR;YACVjjB,QAAQ;YACR6I,SAAS;gBACP+M;YACF;QACF;IACF;IAEA,gGAAgG;IAChG,IAAI,CAACS,qBAAqB;QACxB,OAAO;YACL6M,wBACE;SAEH;IACH;IAEA,kGAAkG;IAClG,4CAA4C;IAC5C,IAAI,CAAC5e,QAAQ;QACX,OAAO;YACL4e,wBACE;SAEH;IACH;IAEA,6HAA6H;IAC7H,4DAA4D;IAC5D,MAAMC,0BAA0BD,wBAC9B;IAGF,wGAAwG;IACxG,IAAIne,UAAU;QACZ,OAAO;YAACoe;SAAwB;IAClC;IAEA,OAAO;QACL;YACE1M,OAAO;gBACL;oBACE1E,UAAUkR;oBACV1T,aAAazV;oBAGbkG,QAAQ;oBACR6I,SAAS;wBACPvE;wBACA+G;oBACF;gBACF;gBACA,qGAAqG;gBACrG8X;aACD;QACH;KACD;AACH", "ignoreList": [0]}