{"version": 3, "sources": ["../../../src/lib/metadata/metadata-context.tsx"], "sourcesContent": ["import type { AppRenderContext } from '../../server/app-render/app-render'\nimport type { MetadataContext } from './types/resolvers'\n\nexport function createMetadataContext(\n  renderOpts: AppRenderContext['renderOpts']\n): MetadataContext {\n  return {\n    trailingSlash: renderOpts.trailingSlash,\n    isStaticMetadataRouteFile: false,\n  }\n}\n"], "names": ["createMetadataContext", "renderOpts", "trailingSlash", "isStaticMetadataRouteFile"], "mappings": ";;;;+BAGgBA;;;eAAAA;;;AAAT,SAASA,sBACdC,UAA0C;IAE1C,OAAO;QACLC,eAAeD,WAAWC,aAAa;QACvCC,2BAA2B;IAC7B;AACF", "ignoreList": [0]}