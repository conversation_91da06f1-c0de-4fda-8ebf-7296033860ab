{"version": 3, "sources": ["../../../../src/lib/metadata/resolvers/resolve-basics.ts"], "sourcesContent": ["import type {\n  AlternateLinkDescriptor,\n  ResolvedAlternateURLs,\n} from '../types/alternative-urls-types'\nimport type {\n  Metadata,\n  ResolvedMetadata,\n  Viewport,\n} from '../types/metadata-interface'\nimport type { ResolvedVerification } from '../types/metadata-types'\nimport type {\n  FieldResolver,\n  AsyncFieldResolverExtraArgs,\n  MetadataContext,\n} from '../types/resolvers'\nimport { resolveAsArrayOrUndefined } from '../generate/utils'\nimport { resolveAbsoluteUrlWithPathname } from './resolve-url'\n\nfunction resolveAlternateUrl(\n  url: string | URL,\n  metadataBase: URL | null,\n  pathname: string,\n  metadataContext: MetadataContext\n) {\n  // If alter native url is an URL instance,\n  // we treat it as a URL base and resolve with current pathname\n  if (url instanceof URL) {\n    const newUrl = new URL(pathname, url)\n    url.searchParams.forEach((value, key) =>\n      newUrl.searchParams.set(key, value)\n    )\n    url = newUrl\n  }\n  return resolveAbsoluteUrlWithPathname(\n    url,\n    metadataBase,\n    pathname,\n    metadataContext\n  )\n}\n\nexport const resolveThemeColor: FieldResolver<'themeColor', Viewport> = (\n  themeColor\n) => {\n  if (!themeColor) return null\n  const themeColorDescriptors: Viewport['themeColor'] = []\n\n  resolveAsArrayOrUndefined(themeColor)?.forEach((descriptor) => {\n    if (typeof descriptor === 'string')\n      themeColorDescriptors.push({ color: descriptor })\n    else if (typeof descriptor === 'object')\n      themeColorDescriptors.push({\n        color: descriptor.color,\n        media: descriptor.media,\n      })\n  })\n\n  return themeColorDescriptors\n}\n\nasync function resolveUrlValuesOfObject(\n  obj:\n    | Record<\n        string,\n        string | URL | AlternateLinkDescriptor[] | null | undefined\n      >\n    | null\n    | undefined,\n  metadataBase: ResolvedMetadata['metadataBase'],\n  pathname: Promise<string>,\n  metadataContext: MetadataContext\n): Promise<null | Record<string, AlternateLinkDescriptor[]>> {\n  if (!obj) return null\n\n  const result: Record<string, AlternateLinkDescriptor[]> = {}\n  for (const [key, value] of Object.entries(obj)) {\n    if (typeof value === 'string' || value instanceof URL) {\n      const pathnameForUrl = await pathname\n      result[key] = [\n        {\n          url: resolveAlternateUrl(\n            value,\n            metadataBase,\n            pathnameForUrl,\n            metadataContext\n          ),\n        },\n      ]\n    } else if (value && value.length) {\n      result[key] = []\n      const pathnameForUrl = await pathname\n      value.forEach((item, index) => {\n        const url = resolveAlternateUrl(\n          item.url,\n          metadataBase,\n          pathnameForUrl,\n          metadataContext\n        )\n        result[key][index] = {\n          url,\n          title: item.title,\n        }\n      })\n    }\n  }\n  return result\n}\n\nasync function resolveCanonicalUrl(\n  urlOrDescriptor: string | URL | null | AlternateLinkDescriptor | undefined,\n  metadataBase: URL | null,\n  pathname: Promise<string>,\n  metadataContext: MetadataContext\n): Promise<null | AlternateLinkDescriptor> {\n  if (!urlOrDescriptor) return null\n\n  const url =\n    typeof urlOrDescriptor === 'string' || urlOrDescriptor instanceof URL\n      ? urlOrDescriptor\n      : urlOrDescriptor.url\n\n  const pathnameForUrl = await pathname\n\n  // Return string url because structureClone can't handle URL instance\n  return {\n    url: resolveAlternateUrl(\n      url,\n      metadataBase,\n      pathnameForUrl,\n      metadataContext\n    ),\n  }\n}\n\nexport const resolveAlternates: AsyncFieldResolverExtraArgs<\n  'alternates',\n  [ResolvedMetadata['metadataBase'], Promise<string>, MetadataContext]\n> = async (alternates, metadataBase, pathname, context) => {\n  if (!alternates) return null\n\n  const canonical = await resolveCanonicalUrl(\n    alternates.canonical,\n    metadataBase,\n    pathname,\n    context\n  )\n  const languages = await resolveUrlValuesOfObject(\n    alternates.languages,\n    metadataBase,\n    pathname,\n    context\n  )\n  const media = await resolveUrlValuesOfObject(\n    alternates.media,\n    metadataBase,\n    pathname,\n    context\n  )\n  const types = await resolveUrlValuesOfObject(\n    alternates.types,\n    metadataBase,\n    pathname,\n    context\n  )\n\n  const result: ResolvedAlternateURLs = {\n    canonical,\n    languages,\n    media,\n    types,\n  }\n\n  return result\n}\n\nconst robotsKeys = [\n  'noarchive',\n  'nosnippet',\n  'noimageindex',\n  'nocache',\n  'notranslate',\n  'indexifembedded',\n  'nositelinkssearchbox',\n  'unavailable_after',\n  'max-video-preview',\n  'max-image-preview',\n  'max-snippet',\n] as const\nconst resolveRobotsValue: (robots: Metadata['robots']) => string | null = (\n  robots\n) => {\n  if (!robots) return null\n  if (typeof robots === 'string') return robots\n\n  const values: string[] = []\n\n  if (robots.index) values.push('index')\n  else if (typeof robots.index === 'boolean') values.push('noindex')\n\n  if (robots.follow) values.push('follow')\n  else if (typeof robots.follow === 'boolean') values.push('nofollow')\n\n  for (const key of robotsKeys) {\n    const value = robots[key]\n    if (typeof value !== 'undefined' && value !== false) {\n      values.push(typeof value === 'boolean' ? key : `${key}:${value}`)\n    }\n  }\n\n  return values.join(', ')\n}\n\nexport const resolveRobots: FieldResolver<'robots'> = (robots) => {\n  if (!robots) return null\n  return {\n    basic: resolveRobotsValue(robots),\n    googleBot:\n      typeof robots !== 'string' ? resolveRobotsValue(robots.googleBot) : null,\n  }\n}\n\nconst VerificationKeys = ['google', 'yahoo', 'yandex', 'me', 'other'] as const\nexport const resolveVerification: FieldResolver<'verification'> = (\n  verification\n) => {\n  if (!verification) return null\n  const res: ResolvedVerification = {}\n\n  for (const key of VerificationKeys) {\n    const value = verification[key]\n    if (value) {\n      if (key === 'other') {\n        res.other = {}\n        for (const otherKey in verification.other) {\n          const otherValue = resolveAsArrayOrUndefined(\n            verification.other[otherKey]\n          )\n          if (otherValue) res.other[otherKey] = otherValue\n        }\n      } else res[key] = resolveAsArrayOrUndefined(value) as (string | number)[]\n    }\n  }\n  return res\n}\n\nexport const resolveAppleWebApp: FieldResolver<'appleWebApp'> = (appWebApp) => {\n  if (!appWebApp) return null\n  if (appWebApp === true) {\n    return {\n      capable: true,\n    }\n  }\n\n  const startupImages = appWebApp.startupImage\n    ? resolveAsArrayOrUndefined(appWebApp.startupImage)?.map((item) =>\n        typeof item === 'string' ? { url: item } : item\n      )\n    : null\n\n  return {\n    capable: 'capable' in appWebApp ? !!appWebApp.capable : true,\n    title: appWebApp.title || null,\n    startupImage: startupImages,\n    statusBarStyle: appWebApp.statusBarStyle || 'default',\n  }\n}\n\nexport const resolveAppLinks: FieldResolver<'appLinks'> = (appLinks) => {\n  if (!appLinks) return null\n  for (const key in appLinks) {\n    // @ts-ignore // TODO: type infer\n    appLinks[key] = resolveAsArrayOrUndefined(appLinks[key])\n  }\n  return appLinks as ResolvedMetadata['appLinks']\n}\n\nexport const resolveItunes: AsyncFieldResolverExtraArgs<\n  'itunes',\n  [ResolvedMetadata['metadataBase'], Promise<string>, MetadataContext]\n> = async (itunes, metadataBase, pathname, context) => {\n  if (!itunes) return null\n  return {\n    appId: itunes.appId,\n    appArgument: itunes.appArgument\n      ? resolveAlternateUrl(\n          itunes.appArgument,\n          metadataBase,\n          await pathname,\n          context\n        )\n      : undefined,\n  }\n}\n\nexport const resolveFacebook: FieldResolver<'facebook'> = (facebook) => {\n  if (!facebook) return null\n  return {\n    appId: facebook.appId,\n    admins: resolveAsArrayOrUndefined(facebook.admins),\n  }\n}\n\nexport const resolvePagination: AsyncFieldResolverExtraArgs<\n  'pagination',\n  [ResolvedMetadata['metadataBase'], Promise<string>, MetadataContext]\n> = async (pagination, metadataBase, pathname, context) => {\n  return {\n    previous: pagination?.previous\n      ? resolveAlternateUrl(\n          pagination.previous,\n          metadataBase,\n          await pathname,\n          context\n        )\n      : null,\n    next: pagination?.next\n      ? resolveAlternateUrl(\n          pagination.next,\n          metadataBase,\n          await pathname,\n          context\n        )\n      : null,\n  }\n}\n"], "names": ["resolveAlternates", "resolveAppLinks", "resolveAppleWebApp", "resolveFacebook", "resolveItunes", "resolvePagination", "resolveRobots", "resolveThemeColor", "resolveVerification", "resolveAlternateUrl", "url", "metadataBase", "pathname", "metadataContext", "URL", "newUrl", "searchParams", "for<PERSON>ach", "value", "key", "set", "resolveAbsoluteUrlWithPathname", "themeColor", "resolveAsArrayOrUndefined", "themeColorDescriptors", "descriptor", "push", "color", "media", "resolveUrlValuesOfObject", "obj", "result", "Object", "entries", "pathnameForUrl", "length", "item", "index", "title", "resolveCanonicalUrl", "urlOrDescriptor", "alternates", "context", "canonical", "languages", "types", "robotsKeys", "resolveRobotsValue", "robots", "values", "follow", "join", "basic", "googleBot", "VerificationKeys", "verification", "res", "other", "otherKey", "otherValue", "appWebApp", "capable", "startupImages", "startupImage", "map", "statusBarStyle", "appLinks", "itunes", "appId", "appArgument", "undefined", "facebook", "admins", "pagination", "previous", "next"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;IAsIaA,iBAAiB;eAAjBA;;IAqIAC,eAAe;eAAfA;;IAtBAC,kBAAkB;eAAlBA;;IAiDAC,eAAe;eAAfA;;IAlBAC,aAAa;eAAbA;;IA0BAC,iBAAiB;eAAjBA;;IA1FAC,aAAa;eAAbA;;IA3KAC,iBAAiB;eAAjBA;;IAqLAC,mBAAmB;eAAnBA;;;uBA/M6B;4BACK;AAE/C,SAASC,oBACPC,GAAiB,EACjBC,YAAwB,EACxBC,QAAgB,EAChBC,eAAgC;IAEhC,0CAA0C;IAC1C,8DAA8D;IAC9D,IAAIH,eAAeI,KAAK;QACtB,MAAMC,SAAS,IAAID,IAAIF,UAAUF;QACjCA,IAAIM,YAAY,CAACC,OAAO,CAAC,CAACC,OAAOC,MAC/BJ,OAAOC,YAAY,CAACI,GAAG,CAACD,KAAKD;QAE/BR,MAAMK;IACR;IACA,OAAOM,IAAAA,0CAA8B,EACnCX,KACAC,cACAC,UACAC;AAEJ;AAEO,MAAMN,oBAA2D,CACtEe;QAKAC;IAHA,IAAI,CAACD,YAAY,OAAO;IACxB,MAAME,wBAAgD,EAAE;KAExDD,6BAAAA,IAAAA,gCAAyB,EAACD,gCAA1BC,2BAAuCN,OAAO,CAAC,CAACQ;QAC9C,IAAI,OAAOA,eAAe,UACxBD,sBAAsBE,IAAI,CAAC;YAAEC,OAAOF;QAAW;aAC5C,IAAI,OAAOA,eAAe,UAC7BD,sBAAsBE,IAAI,CAAC;YACzBC,OAAOF,WAAWE,KAAK;YACvBC,OAAOH,WAAWG,KAAK;QACzB;IACJ;IAEA,OAAOJ;AACT;AAEA,eAAeK,yBACbC,GAMa,EACbnB,YAA8C,EAC9CC,QAAyB,EACzBC,eAAgC;IAEhC,IAAI,CAACiB,KAAK,OAAO;IAEjB,MAAMC,SAAoD,CAAC;IAC3D,KAAK,MAAM,CAACZ,KAAKD,MAAM,IAAIc,OAAOC,OAAO,CAACH,KAAM;QAC9C,IAAI,OAAOZ,UAAU,YAAYA,iBAAiBJ,KAAK;YACrD,MAAMoB,iBAAiB,MAAMtB;YAC7BmB,MAAM,CAACZ,IAAI,GAAG;gBACZ;oBACET,KAAKD,oBACHS,OACAP,cACAuB,gBACArB;gBAEJ;aACD;QACH,OAAO,IAAIK,SAASA,MAAMiB,MAAM,EAAE;YAChCJ,MAAM,CAACZ,IAAI,GAAG,EAAE;YAChB,MAAMe,iBAAiB,MAAMtB;YAC7BM,MAAMD,OAAO,CAAC,CAACmB,MAAMC;gBACnB,MAAM3B,MAAMD,oBACV2B,KAAK1B,GAAG,EACRC,cACAuB,gBACArB;gBAEFkB,MAAM,CAACZ,IAAI,CAACkB,MAAM,GAAG;oBACnB3B;oBACA4B,OAAOF,KAAKE,KAAK;gBACnB;YACF;QACF;IACF;IACA,OAAOP;AACT;AAEA,eAAeQ,oBACbC,eAA0E,EAC1E7B,YAAwB,EACxBC,QAAyB,EACzBC,eAAgC;IAEhC,IAAI,CAAC2B,iBAAiB,OAAO;IAE7B,MAAM9B,MACJ,OAAO8B,oBAAoB,YAAYA,2BAA2B1B,MAC9D0B,kBACAA,gBAAgB9B,GAAG;IAEzB,MAAMwB,iBAAiB,MAAMtB;IAE7B,qEAAqE;IACrE,OAAO;QACLF,KAAKD,oBACHC,KACAC,cACAuB,gBACArB;IAEJ;AACF;AAEO,MAAMb,oBAGT,OAAOyC,YAAY9B,cAAcC,UAAU8B;IAC7C,IAAI,CAACD,YAAY,OAAO;IAExB,MAAME,YAAY,MAAMJ,oBACtBE,WAAWE,SAAS,EACpBhC,cACAC,UACA8B;IAEF,MAAME,YAAY,MAAMf,yBACtBY,WAAWG,SAAS,EACpBjC,cACAC,UACA8B;IAEF,MAAMd,QAAQ,MAAMC,yBAClBY,WAAWb,KAAK,EAChBjB,cACAC,UACA8B;IAEF,MAAMG,QAAQ,MAAMhB,yBAClBY,WAAWI,KAAK,EAChBlC,cACAC,UACA8B;IAGF,MAAMX,SAAgC;QACpCY;QACAC;QACAhB;QACAiB;IACF;IAEA,OAAOd;AACT;AAEA,MAAMe,aAAa;IACjB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AACD,MAAMC,qBAAoE,CACxEC;IAEA,IAAI,CAACA,QAAQ,OAAO;IACpB,IAAI,OAAOA,WAAW,UAAU,OAAOA;IAEvC,MAAMC,SAAmB,EAAE;IAE3B,IAAID,OAAOX,KAAK,EAAEY,OAAOvB,IAAI,CAAC;SACzB,IAAI,OAAOsB,OAAOX,KAAK,KAAK,WAAWY,OAAOvB,IAAI,CAAC;IAExD,IAAIsB,OAAOE,MAAM,EAAED,OAAOvB,IAAI,CAAC;SAC1B,IAAI,OAAOsB,OAAOE,MAAM,KAAK,WAAWD,OAAOvB,IAAI,CAAC;IAEzD,KAAK,MAAMP,OAAO2B,WAAY;QAC5B,MAAM5B,QAAQ8B,MAAM,CAAC7B,IAAI;QACzB,IAAI,OAAOD,UAAU,eAAeA,UAAU,OAAO;YACnD+B,OAAOvB,IAAI,CAAC,OAAOR,UAAU,YAAYC,MAAM,GAAGA,IAAI,CAAC,EAAED,OAAO;QAClE;IACF;IAEA,OAAO+B,OAAOE,IAAI,CAAC;AACrB;AAEO,MAAM7C,gBAAyC,CAAC0C;IACrD,IAAI,CAACA,QAAQ,OAAO;IACpB,OAAO;QACLI,OAAOL,mBAAmBC;QAC1BK,WACE,OAAOL,WAAW,WAAWD,mBAAmBC,OAAOK,SAAS,IAAI;IACxE;AACF;AAEA,MAAMC,mBAAmB;IAAC;IAAU;IAAS;IAAU;IAAM;CAAQ;AAC9D,MAAM9C,sBAAqD,CAChE+C;IAEA,IAAI,CAACA,cAAc,OAAO;IAC1B,MAAMC,MAA4B,CAAC;IAEnC,KAAK,MAAMrC,OAAOmC,iBAAkB;QAClC,MAAMpC,QAAQqC,YAAY,CAACpC,IAAI;QAC/B,IAAID,OAAO;YACT,IAAIC,QAAQ,SAAS;gBACnBqC,IAAIC,KAAK,GAAG,CAAC;gBACb,IAAK,MAAMC,YAAYH,aAAaE,KAAK,CAAE;oBACzC,MAAME,aAAapC,IAAAA,gCAAyB,EAC1CgC,aAAaE,KAAK,CAACC,SAAS;oBAE9B,IAAIC,YAAYH,IAAIC,KAAK,CAACC,SAAS,GAAGC;gBACxC;YACF,OAAOH,GAAG,CAACrC,IAAI,GAAGI,IAAAA,gCAAyB,EAACL;QAC9C;IACF;IACA,OAAOsC;AACT;AAEO,MAAMtD,qBAAmD,CAAC0D;QAS3DrC;IARJ,IAAI,CAACqC,WAAW,OAAO;IACvB,IAAIA,cAAc,MAAM;QACtB,OAAO;YACLC,SAAS;QACX;IACF;IAEA,MAAMC,gBAAgBF,UAAUG,YAAY,IACxCxC,6BAAAA,IAAAA,gCAAyB,EAACqC,UAAUG,YAAY,sBAAhDxC,2BAAmDyC,GAAG,CAAC,CAAC5B,OACtD,OAAOA,SAAS,WAAW;YAAE1B,KAAK0B;QAAK,IAAIA,QAE7C;IAEJ,OAAO;QACLyB,SAAS,aAAaD,YAAY,CAAC,CAACA,UAAUC,OAAO,GAAG;QACxDvB,OAAOsB,UAAUtB,KAAK,IAAI;QAC1ByB,cAAcD;QACdG,gBAAgBL,UAAUK,cAAc,IAAI;IAC9C;AACF;AAEO,MAAMhE,kBAA6C,CAACiE;IACzD,IAAI,CAACA,UAAU,OAAO;IACtB,IAAK,MAAM/C,OAAO+C,SAAU;QAC1B,iCAAiC;QACjCA,QAAQ,CAAC/C,IAAI,GAAGI,IAAAA,gCAAyB,EAAC2C,QAAQ,CAAC/C,IAAI;IACzD;IACA,OAAO+C;AACT;AAEO,MAAM9D,gBAGT,OAAO+D,QAAQxD,cAAcC,UAAU8B;IACzC,IAAI,CAACyB,QAAQ,OAAO;IACpB,OAAO;QACLC,OAAOD,OAAOC,KAAK;QACnBC,aAAaF,OAAOE,WAAW,GAC3B5D,oBACE0D,OAAOE,WAAW,EAClB1D,cACA,MAAMC,UACN8B,WAEF4B;IACN;AACF;AAEO,MAAMnE,kBAA6C,CAACoE;IACzD,IAAI,CAACA,UAAU,OAAO;IACtB,OAAO;QACLH,OAAOG,SAASH,KAAK;QACrBI,QAAQjD,IAAAA,gCAAyB,EAACgD,SAASC,MAAM;IACnD;AACF;AAEO,MAAMnE,oBAGT,OAAOoE,YAAY9D,cAAcC,UAAU8B;IAC7C,OAAO;QACLgC,UAAUD,CAAAA,8BAAAA,WAAYC,QAAQ,IAC1BjE,oBACEgE,WAAWC,QAAQ,EACnB/D,cACA,MAAMC,UACN8B,WAEF;QACJiC,MAAMF,CAAAA,8BAAAA,WAAYE,IAAI,IAClBlE,oBACEgE,WAAWE,IAAI,EACfhE,cACA,MAAMC,UACN8B,WAEF;IACN;AACF", "ignoreList": [0]}