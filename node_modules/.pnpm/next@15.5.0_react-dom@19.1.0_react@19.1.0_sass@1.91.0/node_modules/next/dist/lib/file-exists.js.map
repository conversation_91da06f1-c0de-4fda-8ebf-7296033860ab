{"version": 3, "sources": ["../../src/lib/file-exists.ts"], "sourcesContent": ["import { existsSync, promises } from 'fs'\nimport isError from './is-error'\n\nexport enum FileType {\n  File = 'file',\n  Directory = 'directory',\n}\n\nexport async function fileExists(\n  fileName: string,\n  type?: FileType\n): Promise<boolean> {\n  try {\n    if (type === FileType.File) {\n      const stats = await promises.stat(fileName)\n      return stats.isFile()\n    } else if (type === FileType.Directory) {\n      const stats = await promises.stat(fileName)\n      return stats.isDirectory()\n    }\n\n    return existsSync(fileName)\n  } catch (err) {\n    if (\n      isError(err) &&\n      (err.code === 'ENOENT' || err.code === 'ENAMETOOLONG')\n    ) {\n      return false\n    }\n    throw err\n  }\n}\n"], "names": ["FileType", "fileExists", "fileName", "type", "stats", "promises", "stat", "isFile", "isDirectory", "existsSync", "err", "isError", "code"], "mappings": ";;;;;;;;;;;;;;;IAGYA,QAAQ;eAARA;;IAKUC,UAAU;eAAVA;;;oBARe;gEACjB;;;;;;AAEb,IAAA,AAAKD,kCAAAA;;;WAAAA;;AAKL,eAAeC,WACpBC,QAAgB,EAChBC,IAAe;IAEf,IAAI;QACF,IAAIA,iBAAwB;YAC1B,MAAMC,QAAQ,MAAMC,YAAQ,CAACC,IAAI,CAACJ;YAClC,OAAOE,MAAMG,MAAM;QACrB,OAAO,IAAIJ,sBAA6B;YACtC,MAAMC,QAAQ,MAAMC,YAAQ,CAACC,IAAI,CAACJ;YAClC,OAAOE,MAAMI,WAAW;QAC1B;QAEA,OAAOC,IAAAA,cAAU,EAACP;IACpB,EAAE,OAAOQ,KAAK;QACZ,IACEC,IAAAA,gBAAO,EAACD,QACPA,CAAAA,IAAIE,IAAI,KAAK,YAAYF,IAAIE,IAAI,KAAK,cAAa,GACpD;YACA,OAAO;QACT;QACA,MAAMF;IACR;AACF", "ignoreList": [0]}