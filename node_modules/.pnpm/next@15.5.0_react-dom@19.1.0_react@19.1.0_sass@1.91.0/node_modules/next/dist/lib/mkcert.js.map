{"version": 3, "sources": ["../../src/lib/mkcert.ts"], "sourcesContent": ["import fs from 'node:fs'\nimport path from 'node:path'\nimport { X509Certificate, createPrivate<PERSON><PERSON> } from 'node:crypto'\nimport { getCacheDirectory } from './helpers/get-cache-directory'\nimport * as Log from '../build/output/log'\nimport { execSync } from 'node:child_process'\nconst { WritableStream } =\n  require('node:stream/web') as typeof import('node:stream/web')\n\nconst MKCERT_VERSION = 'v1.4.4'\n\nexport interface SelfSignedCertificate {\n  key: string\n  cert: string\n  rootCA?: string\n}\n\nfunction getBinaryName() {\n  const platform = process.platform\n  const arch = process.arch === 'x64' ? 'amd64' : process.arch\n\n  if (platform === 'win32') {\n    return `mkcert-${MKCERT_VERSION}-windows-${arch}.exe`\n  }\n  if (platform === 'darwin') {\n    return `mkcert-${MKCERT_VERSION}-darwin-${arch}`\n  }\n  if (platform === 'linux') {\n    return `mkcert-${MKCERT_VERSION}-linux-${arch}`\n  }\n\n  throw new Error(`Unsupported platform: ${platform}`)\n}\n\nasync function downloadBinary() {\n  try {\n    const binaryName = getBinaryName()\n    const cacheDirectory = getCacheDirectory('mkcert')\n    const binaryPath = path.join(cacheDirectory, binaryName)\n\n    if (fs.existsSync(binaryPath)) {\n      return binaryPath\n    }\n\n    const downloadUrl = `https://github.com/FiloSottile/mkcert/releases/download/${MKCERT_VERSION}/${binaryName}`\n\n    await fs.promises.mkdir(cacheDirectory, { recursive: true })\n\n    Log.info(`Downloading mkcert package...`)\n\n    const response = await fetch(downloadUrl)\n\n    if (!response.ok || !response.body) {\n      throw new Error(`request failed with status ${response.status}`)\n    }\n\n    Log.info(`Download response was successful, writing to disk`)\n\n    const binaryWriteStream = fs.createWriteStream(binaryPath)\n\n    await response.body.pipeTo(\n      new WritableStream({\n        write(chunk) {\n          return new Promise((resolve, reject) => {\n            binaryWriteStream.write(chunk, (error) => {\n              if (error) {\n                reject(error)\n                return\n              }\n\n              resolve()\n            })\n          })\n        },\n        close() {\n          return new Promise((resolve, reject) => {\n            binaryWriteStream.close((error) => {\n              if (error) {\n                reject(error)\n                return\n              }\n\n              resolve()\n            })\n          })\n        },\n      })\n    )\n\n    await fs.promises.chmod(binaryPath, 0o755)\n\n    return binaryPath\n  } catch (err) {\n    Log.error('Error downloading mkcert:', err)\n  }\n}\n\nexport async function createSelfSignedCertificate(\n  host?: string,\n  certDir: string = 'certificates'\n): Promise<SelfSignedCertificate | undefined> {\n  try {\n    const binaryPath = await downloadBinary()\n    if (!binaryPath) throw new Error('missing mkcert binary')\n\n    const resolvedCertDir = path.resolve(process.cwd(), `./${certDir}`)\n\n    await fs.promises.mkdir(resolvedCertDir, {\n      recursive: true,\n    })\n\n    const keyPath = path.resolve(resolvedCertDir, 'localhost-key.pem')\n    const certPath = path.resolve(resolvedCertDir, 'localhost.pem')\n\n    if (fs.existsSync(keyPath) && fs.existsSync(certPath)) {\n      const cert = new X509Certificate(fs.readFileSync(certPath))\n      const key = fs.readFileSync(keyPath)\n\n      if (\n        cert.checkHost(host ?? 'localhost') &&\n        cert.checkPrivateKey(createPrivateKey(key))\n      ) {\n        Log.info('Using already generated self signed certificate')\n        const caLocation = execSync(`\"${binaryPath}\" -CAROOT`).toString().trim()\n\n        return {\n          key: keyPath,\n          cert: certPath,\n          rootCA: `${caLocation}/rootCA.pem`,\n        }\n      }\n    }\n\n    Log.info(\n      'Attempting to generate self signed certificate. This may prompt for your password'\n    )\n\n    const defaultHosts = ['localhost', '127.0.0.1', '::1']\n\n    const hosts =\n      host && !defaultHosts.includes(host)\n        ? [...defaultHosts, host]\n        : defaultHosts\n\n    execSync(\n      `\"${binaryPath}\" -install -key-file \"${keyPath}\" -cert-file \"${certPath}\" ${hosts.join(\n        ' '\n      )}`,\n      { stdio: 'ignore' }\n    )\n\n    const caLocation = execSync(`\"${binaryPath}\" -CAROOT`).toString().trim()\n\n    if (!fs.existsSync(keyPath) || !fs.existsSync(certPath)) {\n      throw new Error('Certificate files not found')\n    }\n\n    Log.info(`CA Root certificate created in ${caLocation}`)\n    Log.info(`Certificates created in ${resolvedCertDir}`)\n\n    const gitignorePath = path.resolve(process.cwd(), './.gitignore')\n\n    if (fs.existsSync(gitignorePath)) {\n      const gitignore = await fs.promises.readFile(gitignorePath, 'utf8')\n      if (!gitignore.includes(certDir)) {\n        Log.info('Adding certificates to .gitignore')\n\n        await fs.promises.appendFile(gitignorePath, `\\n${certDir}`)\n      }\n    }\n\n    return {\n      key: keyPath,\n      cert: certPath,\n      rootCA: `${caLocation}/rootCA.pem`,\n    }\n  } catch (err) {\n    Log.error(\n      'Failed to generate self-signed certificate. Falling back to http.',\n      err\n    )\n  }\n}\n"], "names": ["createSelfSignedCertificate", "WritableStream", "require", "MKCERT_VERSION", "getBinaryName", "platform", "process", "arch", "Error", "downloadBinary", "binaryName", "cacheDirectory", "getCacheDirectory", "binaryPath", "path", "join", "fs", "existsSync", "downloadUrl", "promises", "mkdir", "recursive", "Log", "info", "response", "fetch", "ok", "body", "status", "binaryWriteStream", "createWriteStream", "pipeTo", "write", "chunk", "Promise", "resolve", "reject", "error", "close", "chmod", "err", "host", "certDir", "resolvedCertDir", "cwd", "keyP<PERSON>", "certPath", "cert", "X509Certificate", "readFileSync", "key", "checkHost", "checkPrivateKey", "createPrivateKey", "caLocation", "execSync", "toString", "trim", "rootCA", "defaultHosts", "hosts", "includes", "stdio", "gitignore<PERSON>ath", "gitignore", "readFile", "appendFile"], "mappings": ";;;;+BAiGsBA;;;eAAAA;;;+DAjGP;iEACE;4BACiC;mCAChB;6DACb;mCACI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACzB,MAAM,EAAEC,cAAc,EAAE,GACtBC,QAAQ;AAEV,MAAMC,iBAAiB;AAQvB,SAASC;IACP,MAAMC,WAAWC,QAAQD,QAAQ;IACjC,MAAME,OAAOD,QAAQC,IAAI,KAAK,QAAQ,UAAUD,QAAQC,IAAI;IAE5D,IAAIF,aAAa,SAAS;QACxB,OAAO,CAAC,OAAO,EAAEF,eAAe,SAAS,EAAEI,KAAK,IAAI,CAAC;IACvD;IACA,IAAIF,aAAa,UAAU;QACzB,OAAO,CAAC,OAAO,EAAEF,eAAe,QAAQ,EAAEI,MAAM;IAClD;IACA,IAAIF,aAAa,SAAS;QACxB,OAAO,CAAC,OAAO,EAAEF,eAAe,OAAO,EAAEI,MAAM;IACjD;IAEA,MAAM,qBAA8C,CAA9C,IAAIC,MAAM,CAAC,sBAAsB,EAAEH,UAAU,GAA7C,qBAAA;eAAA;oBAAA;sBAAA;IAA6C;AACrD;AAEA,eAAeI;IACb,IAAI;QACF,MAAMC,aAAaN;QACnB,MAAMO,iBAAiBC,IAAAA,oCAAiB,EAAC;QACzC,MAAMC,aAAaC,iBAAI,CAACC,IAAI,CAACJ,gBAAgBD;QAE7C,IAAIM,eAAE,CAACC,UAAU,CAACJ,aAAa;YAC7B,OAAOA;QACT;QAEA,MAAMK,cAAc,CAAC,wDAAwD,EAAEf,eAAe,CAAC,EAAEO,YAAY;QAE7G,MAAMM,eAAE,CAACG,QAAQ,CAACC,KAAK,CAACT,gBAAgB;YAAEU,WAAW;QAAK;QAE1DC,KAAIC,IAAI,CAAC,CAAC,6BAA6B,CAAC;QAExC,MAAMC,WAAW,MAAMC,MAAMP;QAE7B,IAAI,CAACM,SAASE,EAAE,IAAI,CAACF,SAASG,IAAI,EAAE;YAClC,MAAM,qBAA0D,CAA1D,IAAInB,MAAM,CAAC,2BAA2B,EAAEgB,SAASI,MAAM,EAAE,GAAzD,qBAAA;uBAAA;4BAAA;8BAAA;YAAyD;QACjE;QAEAN,KAAIC,IAAI,CAAC,CAAC,iDAAiD,CAAC;QAE5D,MAAMM,oBAAoBb,eAAE,CAACc,iBAAiB,CAACjB;QAE/C,MAAMW,SAASG,IAAI,CAACI,MAAM,CACxB,IAAI9B,eAAe;YACjB+B,OAAMC,KAAK;gBACT,OAAO,IAAIC,QAAQ,CAACC,SAASC;oBAC3BP,kBAAkBG,KAAK,CAACC,OAAO,CAACI;wBAC9B,IAAIA,OAAO;4BACTD,OAAOC;4BACP;wBACF;wBAEAF;oBACF;gBACF;YACF;YACAG;gBACE,OAAO,IAAIJ,QAAQ,CAACC,SAASC;oBAC3BP,kBAAkBS,KAAK,CAAC,CAACD;wBACvB,IAAIA,OAAO;4BACTD,OAAOC;4BACP;wBACF;wBAEAF;oBACF;gBACF;YACF;QACF;QAGF,MAAMnB,eAAE,CAACG,QAAQ,CAACoB,KAAK,CAAC1B,YAAY;QAEpC,OAAOA;IACT,EAAE,OAAO2B,KAAK;QACZlB,KAAIe,KAAK,CAAC,6BAA6BG;IACzC;AACF;AAEO,eAAexC,4BACpByC,IAAa,EACbC,UAAkB,cAAc;IAEhC,IAAI;QACF,MAAM7B,aAAa,MAAMJ;QACzB,IAAI,CAACI,YAAY,MAAM,qBAAkC,CAAlC,IAAIL,MAAM,0BAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAiC;QAExD,MAAMmC,kBAAkB7B,iBAAI,CAACqB,OAAO,CAAC7B,QAAQsC,GAAG,IAAI,CAAC,EAAE,EAAEF,SAAS;QAElE,MAAM1B,eAAE,CAACG,QAAQ,CAACC,KAAK,CAACuB,iBAAiB;YACvCtB,WAAW;QACb;QAEA,MAAMwB,UAAU/B,iBAAI,CAACqB,OAAO,CAACQ,iBAAiB;QAC9C,MAAMG,WAAWhC,iBAAI,CAACqB,OAAO,CAACQ,iBAAiB;QAE/C,IAAI3B,eAAE,CAACC,UAAU,CAAC4B,YAAY7B,eAAE,CAACC,UAAU,CAAC6B,WAAW;YACrD,MAAMC,OAAO,IAAIC,2BAAe,CAAChC,eAAE,CAACiC,YAAY,CAACH;YACjD,MAAMI,MAAMlC,eAAE,CAACiC,YAAY,CAACJ;YAE5B,IACEE,KAAKI,SAAS,CAACV,QAAQ,gBACvBM,KAAKK,eAAe,CAACC,IAAAA,4BAAgB,EAACH,OACtC;gBACA5B,KAAIC,IAAI,CAAC;gBACT,MAAM+B,aAAaC,IAAAA,2BAAQ,EAAC,CAAC,CAAC,EAAE1C,WAAW,SAAS,CAAC,EAAE2C,QAAQ,GAAGC,IAAI;gBAEtE,OAAO;oBACLP,KAAKL;oBACLE,MAAMD;oBACNY,QAAQ,GAAGJ,WAAW,WAAW,CAAC;gBACpC;YACF;QACF;QAEAhC,KAAIC,IAAI,CACN;QAGF,MAAMoC,eAAe;YAAC;YAAa;YAAa;SAAM;QAEtD,MAAMC,QACJnB,QAAQ,CAACkB,aAAaE,QAAQ,CAACpB,QAC3B;eAAIkB;YAAclB;SAAK,GACvBkB;QAENJ,IAAAA,2BAAQ,EACN,CAAC,CAAC,EAAE1C,WAAW,sBAAsB,EAAEgC,QAAQ,cAAc,EAAEC,SAAS,EAAE,EAAEc,MAAM7C,IAAI,CACpF,MACC,EACH;YAAE+C,OAAO;QAAS;QAGpB,MAAMR,aAAaC,IAAAA,2BAAQ,EAAC,CAAC,CAAC,EAAE1C,WAAW,SAAS,CAAC,EAAE2C,QAAQ,GAAGC,IAAI;QAEtE,IAAI,CAACzC,eAAE,CAACC,UAAU,CAAC4B,YAAY,CAAC7B,eAAE,CAACC,UAAU,CAAC6B,WAAW;YACvD,MAAM,qBAAwC,CAAxC,IAAItC,MAAM,gCAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAuC;QAC/C;QAEAc,KAAIC,IAAI,CAAC,CAAC,+BAA+B,EAAE+B,YAAY;QACvDhC,KAAIC,IAAI,CAAC,CAAC,wBAAwB,EAAEoB,iBAAiB;QAErD,MAAMoB,gBAAgBjD,iBAAI,CAACqB,OAAO,CAAC7B,QAAQsC,GAAG,IAAI;QAElD,IAAI5B,eAAE,CAACC,UAAU,CAAC8C,gBAAgB;YAChC,MAAMC,YAAY,MAAMhD,eAAE,CAACG,QAAQ,CAAC8C,QAAQ,CAACF,eAAe;YAC5D,IAAI,CAACC,UAAUH,QAAQ,CAACnB,UAAU;gBAChCpB,KAAIC,IAAI,CAAC;gBAET,MAAMP,eAAE,CAACG,QAAQ,CAAC+C,UAAU,CAACH,eAAe,CAAC,EAAE,EAAErB,SAAS;YAC5D;QACF;QAEA,OAAO;YACLQ,KAAKL;YACLE,MAAMD;YACNY,QAAQ,GAAGJ,WAAW,WAAW,CAAC;QACpC;IACF,EAAE,OAAOd,KAAK;QACZlB,KAAIe,KAAK,CACP,qEACAG;IAEJ;AACF", "ignoreList": [0]}