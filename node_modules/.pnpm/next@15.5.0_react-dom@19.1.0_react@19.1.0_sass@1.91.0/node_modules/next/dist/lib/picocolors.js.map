{"version": 3, "sources": ["../../src/lib/picocolors.ts"], "sourcesContent": ["// ISC License\n\n// Copyright (c) 2021 <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>\n\n// Permission to use, copy, modify, and/or distribute this software for any\n// purpose with or without fee is hereby granted, provided that the above\n// copyright notice and this permission notice appear in all copies.\n\n// THE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES\n// WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF\n// MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR\n// ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES\n// WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN\n// ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF\n// OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.\n//\n// https://github.com/ale<PERSON><PERSON><PERSON>ov/picocolors/blob/b6261487e7b81aaab2440e397a356732cad9e342/picocolors.js#L1\n\nconst { env, stdout } = globalThis?.process ?? {}\n\nconst enabled =\n  env &&\n  !env.NO_COLOR &&\n  (env.FORCE_COLOR || (stdout?.isTTY && !env.CI && env.TERM !== 'dumb'))\n\nconst replaceClose = (\n  str: string,\n  close: string,\n  replace: string,\n  index: number\n): string => {\n  const start = str.substring(0, index) + replace\n  const end = str.substring(index + close.length)\n  const nextIndex = end.indexOf(close)\n  return ~nextIndex\n    ? start + replaceClose(end, close, replace, nextIndex)\n    : start + end\n}\n\nconst formatter = (open: string, close: string, replace = open) => {\n  if (!enabled) return String\n  return (input: string) => {\n    const string = '' + input\n    const index = string.indexOf(close, open.length)\n    return ~index\n      ? open + replaceClose(string, close, replace, index) + close\n      : open + string + close\n  }\n}\n\nexport const reset = enabled ? (s: string) => `\\x1b[0m${s}\\x1b[0m` : String\nexport const bold = formatter('\\x1b[1m', '\\x1b[22m', '\\x1b[22m\\x1b[1m')\nexport const dim = formatter('\\x1b[2m', '\\x1b[22m', '\\x1b[22m\\x1b[2m')\nexport const italic = formatter('\\x1b[3m', '\\x1b[23m')\nexport const underline = formatter('\\x1b[4m', '\\x1b[24m')\nexport const inverse = formatter('\\x1b[7m', '\\x1b[27m')\nexport const hidden = formatter('\\x1b[8m', '\\x1b[28m')\nexport const strikethrough = formatter('\\x1b[9m', '\\x1b[29m')\nexport const black = formatter('\\x1b[30m', '\\x1b[39m')\nexport const red = formatter('\\x1b[31m', '\\x1b[39m')\nexport const green = formatter('\\x1b[32m', '\\x1b[39m')\nexport const yellow = formatter('\\x1b[33m', '\\x1b[39m')\nexport const blue = formatter('\\x1b[34m', '\\x1b[39m')\nexport const magenta = formatter('\\x1b[35m', '\\x1b[39m')\nexport const purple = formatter('\\x1b[38;2;173;127;168m', '\\x1b[39m')\nexport const cyan = formatter('\\x1b[36m', '\\x1b[39m')\nexport const white = formatter('\\x1b[37m', '\\x1b[39m')\nexport const gray = formatter('\\x1b[90m', '\\x1b[39m')\nexport const bgBlack = formatter('\\x1b[40m', '\\x1b[49m')\nexport const bgRed = formatter('\\x1b[41m', '\\x1b[49m')\nexport const bgGreen = formatter('\\x1b[42m', '\\x1b[49m')\nexport const bgYellow = formatter('\\x1b[43m', '\\x1b[49m')\nexport const bgBlue = formatter('\\x1b[44m', '\\x1b[49m')\nexport const bgMagenta = formatter('\\x1b[45m', '\\x1b[49m')\nexport const bgCyan = formatter('\\x1b[46m', '\\x1b[49m')\nexport const bgWhite = formatter('\\x1b[47m', '\\x1b[49m')\n"], "names": ["bgBlack", "bgBlue", "bg<PERSON>yan", "bgGreen", "bgMagenta", "bgRed", "bgWhite", "bgYellow", "black", "blue", "bold", "cyan", "dim", "gray", "green", "hidden", "inverse", "italic", "magenta", "purple", "red", "reset", "strikethrough", "underline", "white", "yellow", "globalThis", "env", "stdout", "process", "enabled", "NO_COLOR", "FORCE_COLOR", "isTTY", "CI", "TERM", "replaceClose", "str", "close", "replace", "index", "start", "substring", "end", "length", "nextIndex", "indexOf", "formatter", "open", "String", "input", "string", "s"], "mappings": "AAAA,cAAc;AAEd,wEAAwE;AAExE,2EAA2E;AAC3E,yEAAyE;AACzE,oEAAoE;AAEpE,2EAA2E;AAC3E,mEAAmE;AACnE,0EAA0E;AAC1E,yEAAyE;AACzE,wEAAwE;AACxE,0EAA0E;AAC1E,iEAAiE;AACjE,EAAE;AACF,8GAA8G;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAoDjGA,OAAO;eAAPA;;IAIAC,MAAM;eAANA;;IAEAC,MAAM;eAANA;;IAJAC,OAAO;eAAPA;;IAGAC,SAAS;eAATA;;IAJAC,KAAK;eAALA;;IAMAC,OAAO;eAAPA;;IAJAC,QAAQ;eAARA;;IAbAC,KAAK;eAALA;;IAIAC,IAAI;eAAJA;;IAXAC,IAAI;eAAJA;;IAcAC,IAAI;eAAJA;;IAbAC,GAAG;eAAHA;;IAeAC,IAAI;eAAJA;;IAPAC,KAAK;eAALA;;IAJAC,MAAM;eAANA;;IADAC,OAAO;eAAPA;;IAFAC,MAAM;eAANA;;IAUAC,OAAO;eAAPA;;IACAC,MAAM;eAANA;;IALAC,GAAG;eAAHA;;IATAC,KAAK;eAALA;;IAOAC,aAAa;eAAbA;;IAHAC,SAAS;eAATA;;IAYAC,KAAK;eAALA;;IALAC,MAAM;eAANA;;;IA3CWC;AAAxB,MAAM,EAAEC,GAAG,EAAEC,MAAM,EAAE,GAAGF,EAAAA,cAAAA,+BAAAA,YAAYG,OAAO,KAAI,CAAC;AAEhD,MAAMC,UACJH,OACA,CAACA,IAAII,QAAQ,IACZJ,CAAAA,IAAIK,WAAW,IAAKJ,CAAAA,0BAAAA,OAAQK,KAAK,KAAI,CAACN,IAAIO,EAAE,IAAIP,IAAIQ,IAAI,KAAK,MAAM;AAEtE,MAAMC,eAAe,CACnBC,KACAC,OACAC,SACAC;IAEA,MAAMC,QAAQJ,IAAIK,SAAS,CAAC,GAAGF,SAASD;IACxC,MAAMI,MAAMN,IAAIK,SAAS,CAACF,QAAQF,MAAMM,MAAM;IAC9C,MAAMC,YAAYF,IAAIG,OAAO,CAACR;IAC9B,OAAO,CAACO,YACJJ,QAAQL,aAAaO,KAAKL,OAAOC,SAASM,aAC1CJ,QAAQE;AACd;AAEA,MAAMI,YAAY,CAACC,MAAcV,OAAeC,UAAUS,IAAI;IAC5D,IAAI,CAAClB,SAAS,OAAOmB;IACrB,OAAO,CAACC;QACN,MAAMC,SAAS,KAAKD;QACpB,MAAMV,QAAQW,OAAOL,OAAO,CAACR,OAAOU,KAAKJ,MAAM;QAC/C,OAAO,CAACJ,QACJQ,OAAOZ,aAAae,QAAQb,OAAOC,SAASC,SAASF,QACrDU,OAAOG,SAASb;IACtB;AACF;AAEO,MAAMjB,QAAQS,UAAU,CAACsB,IAAc,CAAC,OAAO,EAAEA,EAAE,OAAO,CAAC,GAAGH;AAC9D,MAAMvC,OAAOqC,UAAU,WAAW,YAAY;AAC9C,MAAMnC,MAAMmC,UAAU,WAAW,YAAY;AAC7C,MAAM9B,SAAS8B,UAAU,WAAW;AACpC,MAAMxB,YAAYwB,UAAU,WAAW;AACvC,MAAM/B,UAAU+B,UAAU,WAAW;AACrC,MAAMhC,SAASgC,UAAU,WAAW;AACpC,MAAMzB,gBAAgByB,UAAU,WAAW;AAC3C,MAAMvC,QAAQuC,UAAU,YAAY;AACpC,MAAM3B,MAAM2B,UAAU,YAAY;AAClC,MAAMjC,QAAQiC,UAAU,YAAY;AACpC,MAAMtB,SAASsB,UAAU,YAAY;AACrC,MAAMtC,OAAOsC,UAAU,YAAY;AACnC,MAAM7B,UAAU6B,UAAU,YAAY;AACtC,MAAM5B,SAAS4B,UAAU,0BAA0B;AACnD,MAAMpC,OAAOoC,UAAU,YAAY;AACnC,MAAMvB,QAAQuB,UAAU,YAAY;AACpC,MAAMlC,OAAOkC,UAAU,YAAY;AACnC,MAAM/C,UAAU+C,UAAU,YAAY;AACtC,MAAM1C,QAAQ0C,UAAU,YAAY;AACpC,MAAM5C,UAAU4C,UAAU,YAAY;AACtC,MAAMxC,WAAWwC,UAAU,YAAY;AACvC,MAAM9C,SAAS8C,UAAU,YAAY;AACrC,MAAM3C,YAAY2C,UAAU,YAAY;AACxC,MAAM7C,SAAS6C,UAAU,YAAY;AACrC,MAAMzC,UAAUyC,UAAU,YAAY", "ignoreList": [0]}