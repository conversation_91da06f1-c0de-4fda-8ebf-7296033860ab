{"version": 3, "sources": ["../../../src/lib/typescript/writeConfigurationDefaults.ts"], "sourcesContent": ["import { promises as fs } from 'fs'\nimport { bold, cyan, white } from '../picocolors'\nimport * as Comment<PERSON>son from 'next/dist/compiled/comment-json'\nimport semver from 'next/dist/compiled/semver'\nimport os from 'os'\nimport type { CompilerOptions } from 'typescript'\nimport { getTypeScriptConfiguration } from './getTypeScriptConfiguration'\nimport * as Log from '../../build/output/log'\n\ntype DesiredCompilerOptionsShape = {\n  [K in keyof CompilerOptions]:\n    | { suggested: any; reason?: string }\n    | {\n        parsedValue?: any\n        parsedValues?: Array<any>\n        value: any\n        reason: string\n      }\n}\n\nfunction getDesiredCompilerOptions(\n  ts: typeof import('typescript'),\n  tsOptions?: CompilerOptions\n): DesiredCompilerOptionsShape {\n  const o: DesiredCompilerOptionsShape = {\n    target: {\n      suggested: 'ES2017',\n      reason:\n        'For top-level `await`. Note: Next.js only polyfills for the esmodules target.',\n    },\n    // These are suggested values and will be set when not present in the\n    // tsconfig.json\n    lib: { suggested: ['dom', 'dom.iterable', 'esnext'] },\n    allowJs: { suggested: true },\n    skipLibCheck: { suggested: true },\n    strict: { suggested: false },\n    ...(semver.lt(ts.version, '5.0.0')\n      ? { forceConsistentCasingInFileNames: { suggested: true } }\n      : undefined),\n    noEmit: { suggested: true },\n    ...(semver.gte(ts.version, '4.4.2')\n      ? { incremental: { suggested: true } }\n      : undefined),\n\n    // These values are required and cannot be changed by the user\n    // Keep this in sync with the webpack config\n    // 'parsedValue' matches the output value from ts.parseJsonConfigFileContent()\n    module: {\n      parsedValue: ts.ModuleKind.ESNext,\n      // All of these values work:\n      parsedValues: [\n        semver.gte(ts.version, '5.4.0') && (ts.ModuleKind as any).Preserve,\n        ts.ModuleKind.ES2020,\n        ts.ModuleKind.ESNext,\n        ts.ModuleKind.CommonJS,\n        ts.ModuleKind.AMD,\n        ts.ModuleKind.NodeNext,\n        ts.ModuleKind.Node16,\n      ],\n      value: 'esnext',\n      reason: 'for dynamic import() support',\n    },\n    // TODO: Semver check not needed once Next.js repo uses 5.4.\n    ...(semver.gte(ts.version, '5.4.0') &&\n    tsOptions?.module === (ts.ModuleKind as any).Preserve\n      ? {\n          // TypeScript 5.4 introduced `Preserve`. Using `Preserve` implies\n          // - `moduleResolution` is `Bundler`\n          // - `esModuleInterop` is `true`\n          // - `resolveJsonModule` is `true`\n          // This means that if the user is using Preserve, they don't need these options\n        }\n      : {\n          esModuleInterop: {\n            value: true,\n            reason: 'requirement for SWC / babel',\n          },\n          moduleResolution: {\n            // In TypeScript 5.0, `NodeJs` has renamed to `Node10`\n            parsedValue:\n              ts.ModuleResolutionKind.Bundler ??\n              ts.ModuleResolutionKind.NodeNext ??\n              (ts.ModuleResolutionKind as any).Node10 ??\n              ts.ModuleResolutionKind.NodeJs,\n            // All of these values work:\n            parsedValues: [\n              (ts.ModuleResolutionKind as any).Node10 ??\n                ts.ModuleResolutionKind.NodeJs,\n              // only newer TypeScript versions have this field, it\n              // will be filtered for new versions of TypeScript\n              (ts.ModuleResolutionKind as any).Node12,\n              ts.ModuleResolutionKind.Node16,\n              ts.ModuleResolutionKind.NodeNext,\n              ts.ModuleResolutionKind.Bundler,\n            ].filter((val) => typeof val !== 'undefined'),\n            value: 'node',\n            reason: 'to match webpack resolution',\n          },\n          resolveJsonModule: {\n            value: true,\n            reason: 'to match webpack resolution',\n          },\n        }),\n    ...(tsOptions?.verbatimModuleSyntax === true\n      ? undefined\n      : {\n          isolatedModules: {\n            value: true,\n            reason: 'requirement for SWC / Babel',\n          },\n        }),\n    jsx: {\n      parsedValue: ts.JsxEmit.Preserve,\n      value: 'preserve',\n      reason: 'next.js implements its own optimized jsx transform',\n    },\n  }\n\n  return o\n}\n\nexport function getRequiredConfiguration(\n  ts: typeof import('typescript')\n): Partial<import('typescript').CompilerOptions> {\n  const res: Partial<import('typescript').CompilerOptions> = {}\n\n  const desiredCompilerOptions = getDesiredCompilerOptions(ts)\n  for (const optionKey of Object.keys(desiredCompilerOptions)) {\n    const ev = desiredCompilerOptions[optionKey]\n    if (!('value' in ev)) {\n      continue\n    }\n    res[optionKey] = ev.parsedValue ?? ev.value\n  }\n\n  return res\n}\n\nconst localDevTestFilesExcludeAction =\n  'NEXT_PRIVATE_LOCAL_DEV_TEST_FILES_EXCLUDE'\n\nexport async function writeConfigurationDefaults(\n  ts: typeof import('typescript'),\n  tsConfigPath: string,\n  isFirstTimeSetup: boolean,\n  hasAppDir: boolean,\n  distDir: string,\n  hasPagesDir: boolean\n): Promise<void> {\n  if (isFirstTimeSetup) {\n    await fs.writeFile(tsConfigPath, '{}' + os.EOL)\n  }\n\n  const { options: tsOptions, raw: rawConfig } =\n    await getTypeScriptConfiguration(ts, tsConfigPath, true)\n\n  const userTsConfigContent = await fs.readFile(tsConfigPath, {\n    encoding: 'utf8',\n  })\n  const userTsConfig = CommentJson.parse(userTsConfigContent)\n  if (userTsConfig.compilerOptions == null && !('extends' in rawConfig)) {\n    userTsConfig.compilerOptions = {}\n    isFirstTimeSetup = true\n  }\n\n  const desiredCompilerOptions = getDesiredCompilerOptions(ts, tsOptions)\n\n  const suggestedActions: string[] = []\n  const requiredActions: string[] = []\n  for (const optionKey of Object.keys(desiredCompilerOptions)) {\n    const check = desiredCompilerOptions[optionKey]\n    if ('suggested' in check) {\n      if (!(optionKey in tsOptions)) {\n        if (!userTsConfig.compilerOptions) {\n          userTsConfig.compilerOptions = {}\n        }\n        userTsConfig.compilerOptions[optionKey] = check.suggested\n        suggestedActions.push(\n          cyan(optionKey) +\n            ' was set to ' +\n            bold(check.suggested) +\n            (check.reason ? ` (${check.reason})` : '')\n        )\n      }\n    } else if ('value' in check) {\n      const ev = tsOptions[optionKey]\n      if (\n        !('parsedValues' in check\n          ? check.parsedValues?.includes(ev)\n          : 'parsedValue' in check\n            ? check.parsedValue === ev\n            : check.value === ev)\n      ) {\n        if (!userTsConfig.compilerOptions) {\n          userTsConfig.compilerOptions = {}\n        }\n        userTsConfig.compilerOptions[optionKey] = check.value\n        requiredActions.push(\n          cyan(optionKey) +\n            ' was set to ' +\n            bold(check.value) +\n            ` (${check.reason})`\n        )\n      }\n    } else {\n      // eslint-disable-next-line @typescript-eslint/no-unused-vars\n      const _: never = check\n    }\n  }\n\n  const nextAppTypes = `${distDir}/types/**/*.ts`\n\n  if (!('include' in rawConfig)) {\n    userTsConfig.include = hasAppDir\n      ? ['next-env.d.ts', nextAppTypes, '**/*.ts', '**/*.tsx']\n      : ['next-env.d.ts', '**/*.ts', '**/*.tsx']\n    suggestedActions.push(\n      cyan('include') +\n        ' was set to ' +\n        bold(\n          hasAppDir\n            ? `['next-env.d.ts', '${nextAppTypes}', '**/*.ts', '**/*.tsx']`\n            : `['next-env.d.ts', '**/*.ts', '**/*.tsx']`\n        )\n    )\n  } else if (hasAppDir && !rawConfig.include.includes(nextAppTypes)) {\n    if (!Array.isArray(userTsConfig.include)) {\n      userTsConfig.include = []\n    }\n    // rawConfig will resolve all extends and include paths (ex: tsconfig.json, tsconfig.base.json, etc.)\n    // if it doesn't match userTsConfig then update the userTsConfig to add the\n    // rawConfig's includes in addition to nextAppTypes\n    if (\n      rawConfig.include.length !== userTsConfig.include.length ||\n      JSON.stringify(rawConfig.include.sort()) !==\n        JSON.stringify(userTsConfig.include.sort())\n    ) {\n      userTsConfig.include.push(...rawConfig.include, nextAppTypes)\n      suggestedActions.push(\n        cyan('include') +\n          ' was set to ' +\n          bold(\n            `[${[...rawConfig.include, nextAppTypes]\n              .map((i) => `'${i}'`)\n              .join(', ')}]`\n          )\n      )\n    } else {\n      userTsConfig.include.push(nextAppTypes)\n      suggestedActions.push(\n        cyan('include') + ' was updated to add ' + bold(`'${nextAppTypes}'`)\n      )\n    }\n  }\n\n  // Enable the Next.js typescript plugin.\n  if (hasAppDir) {\n    // Check if the config or the resolved config has the plugin already.\n    const plugins = [\n      ...(Array.isArray(tsOptions.plugins) ? tsOptions.plugins : []),\n      ...(userTsConfig.compilerOptions &&\n      Array.isArray(userTsConfig.compilerOptions.plugins)\n        ? userTsConfig.compilerOptions.plugins\n        : []),\n    ]\n    const hasNextPlugin = plugins.some(\n      ({ name }: { name: string }) => name === 'next'\n    )\n\n    // If the TS config extends on another config, we can't add the `plugin` field\n    // because that will override the parent config's plugins.\n    // Instead we have to show a message to the user to add the plugin manually.\n    if (\n      !userTsConfig.compilerOptions ||\n      (plugins.length &&\n        !hasNextPlugin &&\n        'extends' in rawConfig &&\n        (!rawConfig.compilerOptions || !rawConfig.compilerOptions.plugins))\n    ) {\n      Log.info(\n        `\\nYour ${bold(\n          'tsconfig.json'\n        )} extends another configuration, which means we cannot add the Next.js TypeScript plugin automatically. To improve your development experience, we recommend adding the Next.js plugin (\\`${cyan(\n          '\"plugins\": [{ \"name\": \"next\" }]'\n        )}\\`) manually to your TypeScript configuration. Learn more: https://nextjs.org/docs/app/api-reference/config/typescript#the-typescript-plugin\\n`\n      )\n    } else if (!hasNextPlugin) {\n      if (!('plugins' in userTsConfig.compilerOptions)) {\n        userTsConfig.compilerOptions.plugins = []\n      }\n      userTsConfig.compilerOptions.plugins.push({ name: 'next' })\n      suggestedActions.push(\n        cyan('plugins') + ' was updated to add ' + bold(`{ name: 'next' }`)\n      )\n    }\n\n    // If `strict` is set to `false` and `strictNullChecks` is set to `false`,\n    // then set `strictNullChecks` to `true`.\n    if (\n      hasPagesDir &&\n      hasAppDir &&\n      !tsOptions.strict &&\n      !('strictNullChecks' in tsOptions)\n    ) {\n      userTsConfig.compilerOptions.strictNullChecks = true\n      suggestedActions.push(\n        cyan('strictNullChecks') + ' was set to ' + bold(`true`)\n      )\n    }\n  }\n\n  if (!('exclude' in rawConfig)) {\n    userTsConfig.exclude = ['node_modules']\n    suggestedActions.push(\n      cyan('exclude') + ' was set to ' + bold(`['node_modules']`)\n    )\n  }\n\n  // During local development inside Next.js repo, exclude the test files coverage by the local tsconfig\n  if (process.env.NEXT_PRIVATE_LOCAL_DEV && userTsConfig.exclude) {\n    const tsGlob = '**/*.test.ts'\n    const tsxGlob = '**/*.test.tsx'\n    let hasUpdates = false\n    if (!userTsConfig.exclude.includes(tsGlob)) {\n      userTsConfig.exclude.push(tsGlob)\n      hasUpdates = true\n    }\n    if (!userTsConfig.exclude.includes(tsxGlob)) {\n      userTsConfig.exclude.push(tsxGlob)\n      hasUpdates = true\n    }\n\n    if (hasUpdates) {\n      requiredActions.push(localDevTestFilesExcludeAction)\n    }\n  }\n\n  if (suggestedActions.length < 1 && requiredActions.length < 1) {\n    return\n  }\n\n  await fs.writeFile(\n    tsConfigPath,\n    CommentJson.stringify(userTsConfig, null, 2) + os.EOL\n  )\n\n  Log.info('')\n  if (isFirstTimeSetup) {\n    Log.info(\n      `We detected TypeScript in your project and created a ${cyan(\n        'tsconfig.json'\n      )} file for you.`\n    )\n    return\n  }\n\n  Log.info(\n    `We detected TypeScript in your project and reconfigured your ${cyan(\n      'tsconfig.json'\n    )} file for you.${\n      userTsConfig.compilerOptions?.strict\n        ? ''\n        : ` Strict-mode is set to ${cyan('false')} by default.`\n    }`\n  )\n\n  if (suggestedActions.length) {\n    Log.info(\n      `The following suggested values were added to your ${cyan(\n        'tsconfig.json'\n      )}. These values ${cyan('can be changed')} to fit your project's needs:\\n`\n    )\n\n    suggestedActions.forEach((action) => Log.info(`\\t- ${action}`))\n\n    Log.info('')\n  }\n\n  const requiredActionsToBeLogged = process.env.NEXT_PRIVATE_LOCAL_DEV\n    ? requiredActions.filter(\n        (action) => action !== localDevTestFilesExcludeAction\n      )\n    : requiredActions\n\n  if (requiredActionsToBeLogged.length) {\n    Log.info(\n      `The following ${white('mandatory changes')} were made to your ${cyan(\n        'tsconfig.json'\n      )}:\\n`\n    )\n\n    requiredActionsToBeLogged.forEach((action) => Log.info(`\\t- ${action}`))\n\n    Log.info('')\n  }\n}\n"], "names": ["getRequiredConfiguration", "writeConfigurationDefaults", "getDesiredCompilerOptions", "ts", "tsOptions", "o", "target", "suggested", "reason", "lib", "allowJs", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "strict", "semver", "lt", "version", "forceConsistentCasingInFileNames", "undefined", "noEmit", "gte", "incremental", "module", "parsedValue", "Module<PERSON>ind", "ESNext", "parsed<PERSON><PERSON>ues", "Preserve", "ES2020", "CommonJS", "AMD", "NodeNext", "Node16", "value", "esModuleInterop", "moduleResolution", "ModuleResolutionKind", "<PERSON><PERSON><PERSON>", "Node10", "NodeJs", "Node12", "filter", "val", "resolveJsonModule", "verbatimModuleSyntax", "isolatedModules", "jsx", "JsxEmit", "res", "desiredCompilerOptions", "optionKey", "Object", "keys", "ev", "localDevTestFilesExcludeAction", "tsConfigPath", "isFirstTimeSetup", "hasAppDir", "distDir", "hasPagesDir", "userTsConfig", "fs", "writeFile", "os", "EOL", "options", "raw", "rawConfig", "getTypeScriptConfiguration", "userTsConfigContent", "readFile", "encoding", "CommentJson", "parse", "compilerOptions", "suggestedActions", "requiredActions", "check", "push", "cyan", "bold", "includes", "_", "nextAppTypes", "include", "Array", "isArray", "length", "JSON", "stringify", "sort", "map", "i", "join", "plugins", "hasNextPlugin", "some", "name", "Log", "info", "strict<PERSON>ull<PERSON>hecks", "exclude", "process", "env", "NEXT_PRIVATE_LOCAL_DEV", "tsGlob", "tsxGlob", "hasUpdates", "for<PERSON>ach", "action", "requiredActionsToBeLogged", "white"], "mappings": ";;;;;;;;;;;;;;;IAyHgBA,wBAAwB;eAAxBA;;IAoBMC,0BAA0B;eAA1BA;;;oBA7IS;4BACG;qEACL;+DACV;2DACJ;4CAE4B;6DACtB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAarB,SAASC,0BACPC,EAA+B,EAC/BC,SAA2B;IAE3B,MAAMC,IAAiC;QACrCC,QAAQ;YACNC,WAAW;YACXC,QACE;QACJ;QACA,qEAAqE;QACrE,gBAAgB;QAChBC,KAAK;YAAEF,WAAW;gBAAC;gBAAO;gBAAgB;aAAS;QAAC;QACpDG,SAAS;YAAEH,WAAW;QAAK;QAC3BI,cAAc;YAAEJ,WAAW;QAAK;QAChCK,QAAQ;YAAEL,WAAW;QAAM;QAC3B,GAAIM,eAAM,CAACC,EAAE,CAACX,GAAGY,OAAO,EAAE,WACtB;YAAEC,kCAAkC;gBAAET,WAAW;YAAK;QAAE,IACxDU,SAAS;QACbC,QAAQ;YAAEX,WAAW;QAAK;QAC1B,GAAIM,eAAM,CAACM,GAAG,CAAChB,GAAGY,OAAO,EAAE,WACvB;YAAEK,aAAa;gBAAEb,WAAW;YAAK;QAAE,IACnCU,SAAS;QAEb,8DAA8D;QAC9D,4CAA4C;QAC5C,8EAA8E;QAC9EI,QAAQ;YACNC,aAAanB,GAAGoB,UAAU,CAACC,MAAM;YACjC,4BAA4B;YAC5BC,cAAc;gBACZZ,eAAM,CAACM,GAAG,CAAChB,GAAGY,OAAO,EAAE,YAAY,AAACZ,GAAGoB,UAAU,CAASG,QAAQ;gBAClEvB,GAAGoB,UAAU,CAACI,MAAM;gBACpBxB,GAAGoB,UAAU,CAACC,MAAM;gBACpBrB,GAAGoB,UAAU,CAACK,QAAQ;gBACtBzB,GAAGoB,UAAU,CAACM,GAAG;gBACjB1B,GAAGoB,UAAU,CAACO,QAAQ;gBACtB3B,GAAGoB,UAAU,CAACQ,MAAM;aACrB;YACDC,OAAO;YACPxB,QAAQ;QACV;QACA,4DAA4D;QAC5D,GAAIK,eAAM,CAACM,GAAG,CAAChB,GAAGY,OAAO,EAAE,YAC3BX,CAAAA,6BAAAA,UAAWiB,MAAM,MAAK,AAAClB,GAAGoB,UAAU,CAASG,QAAQ,GACjD;QAMA,IACA;YACEO,iBAAiB;gBACfD,OAAO;gBACPxB,QAAQ;YACV;YACA0B,kBAAkB;gBAChB,sDAAsD;gBACtDZ,aACEnB,GAAGgC,oBAAoB,CAACC,OAAO,IAC/BjC,GAAGgC,oBAAoB,CAACL,QAAQ,IAChC,AAAC3B,GAAGgC,oBAAoB,CAASE,MAAM,IACvClC,GAAGgC,oBAAoB,CAACG,MAAM;gBAChC,4BAA4B;gBAC5Bb,cAAc;oBACXtB,GAAGgC,oBAAoB,CAASE,MAAM,IACrClC,GAAGgC,oBAAoB,CAACG,MAAM;oBAChC,qDAAqD;oBACrD,kDAAkD;oBACjDnC,GAAGgC,oBAAoB,CAASI,MAAM;oBACvCpC,GAAGgC,oBAAoB,CAACJ,MAAM;oBAC9B5B,GAAGgC,oBAAoB,CAACL,QAAQ;oBAChC3B,GAAGgC,oBAAoB,CAACC,OAAO;iBAChC,CAACI,MAAM,CAAC,CAACC,MAAQ,OAAOA,QAAQ;gBACjCT,OAAO;gBACPxB,QAAQ;YACV;YACAkC,mBAAmB;gBACjBV,OAAO;gBACPxB,QAAQ;YACV;QACF,CAAC;QACL,GAAIJ,CAAAA,6BAAAA,UAAWuC,oBAAoB,MAAK,OACpC1B,YACA;YACE2B,iBAAiB;gBACfZ,OAAO;gBACPxB,QAAQ;YACV;QACF,CAAC;QACLqC,KAAK;YACHvB,aAAanB,GAAG2C,OAAO,CAACpB,QAAQ;YAChCM,OAAO;YACPxB,QAAQ;QACV;IACF;IAEA,OAAOH;AACT;AAEO,SAASL,yBACdG,EAA+B;IAE/B,MAAM4C,MAAqD,CAAC;IAE5D,MAAMC,yBAAyB9C,0BAA0BC;IACzD,KAAK,MAAM8C,aAAaC,OAAOC,IAAI,CAACH,wBAAyB;QAC3D,MAAMI,KAAKJ,sBAAsB,CAACC,UAAU;QAC5C,IAAI,CAAE,CAAA,WAAWG,EAAC,GAAI;YACpB;QACF;QACAL,GAAG,CAACE,UAAU,GAAGG,GAAG9B,WAAW,IAAI8B,GAAGpB,KAAK;IAC7C;IAEA,OAAOe;AACT;AAEA,MAAMM,iCACJ;AAEK,eAAepD,2BACpBE,EAA+B,EAC/BmD,YAAoB,EACpBC,gBAAyB,EACzBC,SAAkB,EAClBC,OAAe,EACfC,WAAoB;QAqNhBC;IAnNJ,IAAIJ,kBAAkB;QACpB,MAAMK,YAAE,CAACC,SAAS,CAACP,cAAc,OAAOQ,WAAE,CAACC,GAAG;IAChD;IAEA,MAAM,EAAEC,SAAS5D,SAAS,EAAE6D,KAAKC,SAAS,EAAE,GAC1C,MAAMC,IAAAA,sDAA0B,EAAChE,IAAImD,cAAc;IAErD,MAAMc,sBAAsB,MAAMR,YAAE,CAACS,QAAQ,CAACf,cAAc;QAC1DgB,UAAU;IACZ;IACA,MAAMX,eAAeY,aAAYC,KAAK,CAACJ;IACvC,IAAIT,aAAac,eAAe,IAAI,QAAQ,CAAE,CAAA,aAAaP,SAAQ,GAAI;QACrEP,aAAac,eAAe,GAAG,CAAC;QAChClB,mBAAmB;IACrB;IAEA,MAAMP,yBAAyB9C,0BAA0BC,IAAIC;IAE7D,MAAMsE,mBAA6B,EAAE;IACrC,MAAMC,kBAA4B,EAAE;IACpC,KAAK,MAAM1B,aAAaC,OAAOC,IAAI,CAACH,wBAAyB;QAC3D,MAAM4B,QAAQ5B,sBAAsB,CAACC,UAAU;QAC/C,IAAI,eAAe2B,OAAO;YACxB,IAAI,CAAE3B,CAAAA,aAAa7C,SAAQ,GAAI;gBAC7B,IAAI,CAACuD,aAAac,eAAe,EAAE;oBACjCd,aAAac,eAAe,GAAG,CAAC;gBAClC;gBACAd,aAAac,eAAe,CAACxB,UAAU,GAAG2B,MAAMrE,SAAS;gBACzDmE,iBAAiBG,IAAI,CACnBC,IAAAA,gBAAI,EAAC7B,aACH,iBACA8B,IAAAA,gBAAI,EAACH,MAAMrE,SAAS,IACnBqE,CAAAA,MAAMpE,MAAM,GAAG,CAAC,EAAE,EAAEoE,MAAMpE,MAAM,CAAC,CAAC,CAAC,GAAG,EAAC;YAE9C;QACF,OAAO,IAAI,WAAWoE,OAAO;gBAIrBA;YAHN,MAAMxB,KAAKhD,SAAS,CAAC6C,UAAU;YAC/B,IACE,CAAE,CAAA,kBAAkB2B,SAChBA,sBAAAA,MAAMnD,YAAY,qBAAlBmD,oBAAoBI,QAAQ,CAAC5B,MAC7B,iBAAiBwB,QACfA,MAAMtD,WAAW,KAAK8B,KACtBwB,MAAM5C,KAAK,KAAKoB,EAAC,GACvB;gBACA,IAAI,CAACO,aAAac,eAAe,EAAE;oBACjCd,aAAac,eAAe,GAAG,CAAC;gBAClC;gBACAd,aAAac,eAAe,CAACxB,UAAU,GAAG2B,MAAM5C,KAAK;gBACrD2C,gBAAgBE,IAAI,CAClBC,IAAAA,gBAAI,EAAC7B,aACH,iBACA8B,IAAAA,gBAAI,EAACH,MAAM5C,KAAK,IAChB,CAAC,EAAE,EAAE4C,MAAMpE,MAAM,CAAC,CAAC,CAAC;YAE1B;QACF,OAAO;YACL,6DAA6D;YAC7D,MAAMyE,IAAWL;QACnB;IACF;IAEA,MAAMM,eAAe,GAAGzB,QAAQ,cAAc,CAAC;IAE/C,IAAI,CAAE,CAAA,aAAaS,SAAQ,GAAI;QAC7BP,aAAawB,OAAO,GAAG3B,YACnB;YAAC;YAAiB0B;YAAc;YAAW;SAAW,GACtD;YAAC;YAAiB;YAAW;SAAW;QAC5CR,iBAAiBG,IAAI,CACnBC,IAAAA,gBAAI,EAAC,aACH,iBACAC,IAAAA,gBAAI,EACFvB,YACI,CAAC,mBAAmB,EAAE0B,aAAa,yBAAyB,CAAC,GAC7D,CAAC,wCAAwC,CAAC;IAGtD,OAAO,IAAI1B,aAAa,CAACU,UAAUiB,OAAO,CAACH,QAAQ,CAACE,eAAe;QACjE,IAAI,CAACE,MAAMC,OAAO,CAAC1B,aAAawB,OAAO,GAAG;YACxCxB,aAAawB,OAAO,GAAG,EAAE;QAC3B;QACA,qGAAqG;QACrG,2EAA2E;QAC3E,mDAAmD;QACnD,IACEjB,UAAUiB,OAAO,CAACG,MAAM,KAAK3B,aAAawB,OAAO,CAACG,MAAM,IACxDC,KAAKC,SAAS,CAACtB,UAAUiB,OAAO,CAACM,IAAI,QACnCF,KAAKC,SAAS,CAAC7B,aAAawB,OAAO,CAACM,IAAI,KAC1C;YACA9B,aAAawB,OAAO,CAACN,IAAI,IAAIX,UAAUiB,OAAO,EAAED;YAChDR,iBAAiBG,IAAI,CACnBC,IAAAA,gBAAI,EAAC,aACH,iBACAC,IAAAA,gBAAI,EACF,CAAC,CAAC,EAAE;mBAAIb,UAAUiB,OAAO;gBAAED;aAAa,CACrCQ,GAAG,CAAC,CAACC,IAAM,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC,EACnBC,IAAI,CAAC,MAAM,CAAC,CAAC;QAGxB,OAAO;YACLjC,aAAawB,OAAO,CAACN,IAAI,CAACK;YAC1BR,iBAAiBG,IAAI,CACnBC,IAAAA,gBAAI,EAAC,aAAa,yBAAyBC,IAAAA,gBAAI,EAAC,CAAC,CAAC,EAAEG,aAAa,CAAC,CAAC;QAEvE;IACF;IAEA,wCAAwC;IACxC,IAAI1B,WAAW;QACb,qEAAqE;QACrE,MAAMqC,UAAU;eACVT,MAAMC,OAAO,CAACjF,UAAUyF,OAAO,IAAIzF,UAAUyF,OAAO,GAAG,EAAE;eACzDlC,aAAac,eAAe,IAChCW,MAAMC,OAAO,CAAC1B,aAAac,eAAe,CAACoB,OAAO,IAC9ClC,aAAac,eAAe,CAACoB,OAAO,GACpC,EAAE;SACP;QACD,MAAMC,gBAAgBD,QAAQE,IAAI,CAChC,CAAC,EAAEC,IAAI,EAAoB,GAAKA,SAAS;QAG3C,8EAA8E;QAC9E,0DAA0D;QAC1D,4EAA4E;QAC5E,IACE,CAACrC,aAAac,eAAe,IAC5BoB,QAAQP,MAAM,IACb,CAACQ,iBACD,aAAa5B,aACZ,CAAA,CAACA,UAAUO,eAAe,IAAI,CAACP,UAAUO,eAAe,CAACoB,OAAO,AAAD,GAClE;YACAI,KAAIC,IAAI,CACN,CAAC,OAAO,EAAEnB,IAAAA,gBAAI,EACZ,iBACA,yLAAyL,EAAED,IAAAA,gBAAI,EAC/L,mCACA,8IAA8I,CAAC;QAErJ,OAAO,IAAI,CAACgB,eAAe;YACzB,IAAI,CAAE,CAAA,aAAanC,aAAac,eAAe,AAAD,GAAI;gBAChDd,aAAac,eAAe,CAACoB,OAAO,GAAG,EAAE;YAC3C;YACAlC,aAAac,eAAe,CAACoB,OAAO,CAAChB,IAAI,CAAC;gBAAEmB,MAAM;YAAO;YACzDtB,iBAAiBG,IAAI,CACnBC,IAAAA,gBAAI,EAAC,aAAa,yBAAyBC,IAAAA,gBAAI,EAAC,CAAC,gBAAgB,CAAC;QAEtE;QAEA,0EAA0E;QAC1E,yCAAyC;QACzC,IACErB,eACAF,aACA,CAACpD,UAAUQ,MAAM,IACjB,CAAE,CAAA,sBAAsBR,SAAQ,GAChC;YACAuD,aAAac,eAAe,CAAC0B,gBAAgB,GAAG;YAChDzB,iBAAiBG,IAAI,CACnBC,IAAAA,gBAAI,EAAC,sBAAsB,iBAAiBC,IAAAA,gBAAI,EAAC,CAAC,IAAI,CAAC;QAE3D;IACF;IAEA,IAAI,CAAE,CAAA,aAAab,SAAQ,GAAI;QAC7BP,aAAayC,OAAO,GAAG;YAAC;SAAe;QACvC1B,iBAAiBG,IAAI,CACnBC,IAAAA,gBAAI,EAAC,aAAa,iBAAiBC,IAAAA,gBAAI,EAAC,CAAC,gBAAgB,CAAC;IAE9D;IAEA,sGAAsG;IACtG,IAAIsB,QAAQC,GAAG,CAACC,sBAAsB,IAAI5C,aAAayC,OAAO,EAAE;QAC9D,MAAMI,SAAS;QACf,MAAMC,UAAU;QAChB,IAAIC,aAAa;QACjB,IAAI,CAAC/C,aAAayC,OAAO,CAACpB,QAAQ,CAACwB,SAAS;YAC1C7C,aAAayC,OAAO,CAACvB,IAAI,CAAC2B;YAC1BE,aAAa;QACf;QACA,IAAI,CAAC/C,aAAayC,OAAO,CAACpB,QAAQ,CAACyB,UAAU;YAC3C9C,aAAayC,OAAO,CAACvB,IAAI,CAAC4B;YAC1BC,aAAa;QACf;QAEA,IAAIA,YAAY;YACd/B,gBAAgBE,IAAI,CAACxB;QACvB;IACF;IAEA,IAAIqB,iBAAiBY,MAAM,GAAG,KAAKX,gBAAgBW,MAAM,GAAG,GAAG;QAC7D;IACF;IAEA,MAAM1B,YAAE,CAACC,SAAS,CAChBP,cACAiB,aAAYiB,SAAS,CAAC7B,cAAc,MAAM,KAAKG,WAAE,CAACC,GAAG;IAGvDkC,KAAIC,IAAI,CAAC;IACT,IAAI3C,kBAAkB;QACpB0C,KAAIC,IAAI,CACN,CAAC,qDAAqD,EAAEpB,IAAAA,gBAAI,EAC1D,iBACA,cAAc,CAAC;QAEnB;IACF;IAEAmB,KAAIC,IAAI,CACN,CAAC,6DAA6D,EAAEpB,IAAAA,gBAAI,EAClE,iBACA,cAAc,EACdnB,EAAAA,gCAAAA,aAAac,eAAe,qBAA5Bd,8BAA8B/C,MAAM,IAChC,KACA,CAAC,uBAAuB,EAAEkE,IAAAA,gBAAI,EAAC,SAAS,YAAY,CAAC,EACzD;IAGJ,IAAIJ,iBAAiBY,MAAM,EAAE;QAC3BW,KAAIC,IAAI,CACN,CAAC,kDAAkD,EAAEpB,IAAAA,gBAAI,EACvD,iBACA,eAAe,EAAEA,IAAAA,gBAAI,EAAC,kBAAkB,+BAA+B,CAAC;QAG5EJ,iBAAiBiC,OAAO,CAAC,CAACC,SAAWX,KAAIC,IAAI,CAAC,CAAC,IAAI,EAAEU,QAAQ;QAE7DX,KAAIC,IAAI,CAAC;IACX;IAEA,MAAMW,4BAA4BR,QAAQC,GAAG,CAACC,sBAAsB,GAChE5B,gBAAgBnC,MAAM,CACpB,CAACoE,SAAWA,WAAWvD,kCAEzBsB;IAEJ,IAAIkC,0BAA0BvB,MAAM,EAAE;QACpCW,KAAIC,IAAI,CACN,CAAC,cAAc,EAAEY,IAAAA,iBAAK,EAAC,qBAAqB,mBAAmB,EAAEhC,IAAAA,gBAAI,EACnE,iBACA,GAAG,CAAC;QAGR+B,0BAA0BF,OAAO,CAAC,CAACC,SAAWX,KAAIC,IAAI,CAAC,CAAC,IAAI,EAAEU,QAAQ;QAEtEX,KAAIC,IAAI,CAAC;IACX;AACF", "ignoreList": [0]}