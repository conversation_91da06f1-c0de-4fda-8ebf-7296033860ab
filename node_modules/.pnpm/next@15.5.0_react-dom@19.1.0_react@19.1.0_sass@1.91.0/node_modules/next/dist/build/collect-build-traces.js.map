{"version": 3, "sources": ["../../src/build/collect-build-traces.ts"], "sourcesContent": ["import { Span } from '../trace'\nimport type { NextConfigComplete } from '../server/config-shared'\n\nimport {\n  TRACE_IGNORES,\n  type BuildTraceContext,\n  getFilesMapFromReasons,\n} from './webpack/plugins/next-trace-entrypoints-plugin'\n\nimport path from 'path'\nimport fs from 'fs/promises'\nimport { nonNullable } from '../lib/non-nullable'\nimport * as ciEnvironment from '../server/ci-info'\nimport debugOriginal from 'next/dist/compiled/debug'\nimport picomatch from 'next/dist/compiled/picomatch'\nimport { defaultOverrides } from '../server/require-hook'\nimport { nodeFileTrace } from 'next/dist/compiled/@vercel/nft'\nimport { normalizePagePath } from '../shared/lib/page-path/normalize-page-path'\nimport { normalizeAppPath } from '../shared/lib/router/utils/app-paths'\nimport isError from '../lib/is-error'\nimport type { NodeFileTraceReasons } from '@vercel/nft'\nimport type { RoutesUsingEdgeRuntime } from './utils'\n\nconst debug = debugOriginal('next:build:build-traces')\n\nfunction shouldIgnore(\n  file: string,\n  serverIgnoreFn: (file: string) => boolean,\n  reasons: NodeFileTraceReasons,\n  cachedIgnoreFiles: Map<string, boolean>,\n  children: Set<string> = new Set()\n) {\n  if (cachedIgnoreFiles.has(file)) {\n    return cachedIgnoreFiles.get(file)\n  }\n\n  if (serverIgnoreFn(file)) {\n    cachedIgnoreFiles.set(file, true)\n    return true\n  }\n  children.add(file)\n\n  const reason = reasons.get(file)\n  if (!reason || reason.parents.size === 0 || reason.type.includes('initial')) {\n    cachedIgnoreFiles.set(file, false)\n    return false\n  }\n\n  // if all parents are ignored the child file\n  // should be ignored as well\n  let allParentsIgnored = true\n\n  for (const parent of reason.parents.values()) {\n    if (!children.has(parent)) {\n      children.add(parent)\n      if (\n        !shouldIgnore(\n          parent,\n          serverIgnoreFn,\n          reasons,\n          cachedIgnoreFiles,\n          children\n        )\n      ) {\n        allParentsIgnored = false\n        break\n      }\n    }\n  }\n\n  cachedIgnoreFiles.set(file, allParentsIgnored)\n  return allParentsIgnored\n}\n\nexport async function collectBuildTraces({\n  dir,\n  config,\n  distDir,\n  edgeRuntimeRoutes,\n  staticPages,\n  nextBuildSpan = new Span({ name: 'build' }),\n  hasSsrAmpPages,\n  buildTraceContext,\n  outputFileTracingRoot,\n  isTurbopack,\n}: {\n  dir: string\n  distDir: string\n  staticPages: string[]\n  hasSsrAmpPages: boolean\n  outputFileTracingRoot: string\n  // pageInfos is serialized when this function runs in a worker.\n  edgeRuntimeRoutes: RoutesUsingEdgeRuntime\n  nextBuildSpan?: Span\n  config: NextConfigComplete\n  buildTraceContext?: BuildTraceContext\n  isTurbopack: boolean\n}) {\n  const startTime = Date.now()\n  debug('starting build traces')\n\n  const { outputFileTracingIncludes = {}, outputFileTracingExcludes = {} } =\n    config\n  const excludeGlobKeys = Object.keys(outputFileTracingExcludes)\n  const includeGlobKeys = Object.keys(outputFileTracingIncludes)\n\n  await nextBuildSpan\n    .traceChild('node-file-trace-build', {\n      isTurbotrace: 'false', // TODO(arlyon): remove this\n    })\n    .traceAsyncFn(async () => {\n      const nextServerTraceOutput = path.join(\n        distDir,\n        'next-server.js.nft.json'\n      )\n      const nextMinimalTraceOutput = path.join(\n        distDir,\n        'next-minimal-server.js.nft.json'\n      )\n      const root = outputFileTracingRoot\n\n      // Under standalone mode, we need to trace the extra IPC server and\n      // worker files.\n      const isStandalone = config.output === 'standalone'\n      const sharedEntriesSet = Object.keys(defaultOverrides).map((value) =>\n        require.resolve(value, {\n          paths: [require.resolve('next/dist/server/require-hook')],\n        })\n      )\n\n      const { cacheHandler } = config\n      const { cacheHandlers } = config.experimental\n\n      // ensure we trace any dependencies needed for custom\n      // incremental cache handler\n      if (cacheHandler) {\n        sharedEntriesSet.push(\n          require.resolve(\n            path.isAbsolute(cacheHandler)\n              ? cacheHandler\n              : path.join(dir, cacheHandler)\n          )\n        )\n      }\n\n      if (cacheHandlers) {\n        for (const handlerPath of Object.values(cacheHandlers)) {\n          if (handlerPath) {\n            sharedEntriesSet.push(\n              require.resolve(\n                path.isAbsolute(handlerPath)\n                  ? handlerPath\n                  : path.join(dir, handlerPath)\n              )\n            )\n          }\n        }\n      }\n\n      const serverEntries = [\n        ...sharedEntriesSet,\n        ...(isStandalone\n          ? [\n              require.resolve('next/dist/server/lib/start-server'),\n              require.resolve('next/dist/server/next'),\n              require.resolve('next/dist/server/require-hook'),\n            ]\n          : []),\n        require.resolve('next/dist/server/next-server'),\n      ].filter(Boolean) as string[]\n\n      const minimalServerEntries = [\n        ...sharedEntriesSet,\n        require.resolve('next/dist/compiled/next-server/server.runtime.prod'),\n      ].filter(Boolean)\n\n      const additionalIgnores = new Set<string>()\n\n      for (const glob of excludeGlobKeys) {\n        if (picomatch(glob)('next-server')) {\n          outputFileTracingExcludes[glob].forEach((exclude) => {\n            additionalIgnores.add(exclude)\n          })\n        }\n      }\n\n      const makeIgnoreFn = (ignores: string[]) => {\n        // pre compile the ignore globs\n        const isMatch = picomatch(ignores, {\n          contains: true,\n          dot: true,\n        })\n\n        return (pathname: string) => {\n          if (path.isAbsolute(pathname) && !pathname.startsWith(root)) {\n            return true\n          }\n\n          return isMatch(pathname)\n        }\n      }\n\n      const sharedIgnores = [\n        '**/next/dist/compiled/next-server/**/*.dev.js',\n        ...(isStandalone ? [] : ['**/next/dist/compiled/jest-worker/**/*']),\n        '**/next/dist/compiled/webpack/*',\n        '**/node_modules/webpack5/**/*',\n        '**/next/dist/server/lib/route-resolver*',\n        'next/dist/compiled/semver/semver/**/*.js',\n\n        ...(ciEnvironment.hasNextSupport\n          ? [\n              // only ignore image-optimizer code when\n              // this is being handled outside of next-server\n              '**/next/dist/server/image-optimizer.js',\n            ]\n          : []),\n\n        ...(!hasSsrAmpPages\n          ? ['**/next/dist/compiled/@ampproject/toolbox-optimizer/**/*']\n          : []),\n\n        ...(isStandalone ? [] : TRACE_IGNORES),\n        ...additionalIgnores,\n      ]\n\n      const sharedIgnoresFn = makeIgnoreFn(sharedIgnores)\n\n      const serverIgnores = [\n        ...sharedIgnores,\n        '**/node_modules/react{,-dom,-dom-server-turbopack}/**/*.development.js',\n        '**/*.d.ts',\n        '**/*.map',\n        '**/next/dist/pages/**/*',\n        ...(ciEnvironment.hasNextSupport\n          ? ['**/node_modules/sharp/**/*', '**/@img/sharp-libvips*/**/*']\n          : []),\n      ].filter(nonNullable)\n      const serverIgnoreFn = makeIgnoreFn(serverIgnores)\n\n      const minimalServerIgnores = [\n        ...serverIgnores,\n        '**/next/dist/compiled/edge-runtime/**/*',\n        '**/next/dist/server/web/sandbox/**/*',\n        '**/next/dist/server/post-process.js',\n      ]\n      const minimalServerIgnoreFn = makeIgnoreFn(minimalServerIgnores)\n\n      const routesIgnores = [\n        ...sharedIgnores,\n        // server chunks are provided via next-trace-entrypoints-plugin plugin\n        // as otherwise all chunks are traced here and included for all pages\n        // whether they are needed or not\n        '**/.next/server/chunks/**',\n        '**/next/dist/server/optimize-amp.js',\n        '**/next/dist/server/post-process.js',\n      ].filter(nonNullable)\n\n      const routeIgnoreFn = makeIgnoreFn(routesIgnores)\n\n      const serverTracedFiles = new Set<string>()\n      const minimalServerTracedFiles = new Set<string>()\n\n      function addToTracedFiles(base: string, file: string, dest: Set<string>) {\n        dest.add(\n          path.relative(distDir, path.join(base, file)).replace(/\\\\/g, '/')\n        )\n      }\n\n      if (isStandalone) {\n        addToTracedFiles(\n          '',\n          require.resolve('next/dist/compiled/jest-worker/processChild'),\n          serverTracedFiles\n        )\n        addToTracedFiles(\n          '',\n          require.resolve('next/dist/compiled/jest-worker/threadChild'),\n          serverTracedFiles\n        )\n      }\n\n      if (isTurbopack) {\n        addToTracedFiles(distDir, './package.json', serverTracedFiles)\n        addToTracedFiles(distDir, './package.json', minimalServerTracedFiles)\n      }\n\n      {\n        const chunksToTrace: string[] = [\n          ...(buildTraceContext?.chunksTrace?.action.input || []),\n          ...serverEntries,\n          ...minimalServerEntries,\n        ]\n        const result = await nodeFileTrace(chunksToTrace, {\n          base: outputFileTracingRoot,\n          processCwd: dir,\n          mixedModules: true,\n          async readFile(p) {\n            try {\n              return await fs.readFile(p, 'utf8')\n            } catch (e) {\n              if (isError(e) && (e.code === 'ENOENT' || e.code === 'EISDIR')) {\n                // since tracing runs in parallel with static generation server\n                // files might be removed from that step so tolerate ENOENT\n                // errors gracefully\n                return ''\n              }\n              throw e\n            }\n          },\n          async readlink(p) {\n            try {\n              return await fs.readlink(p)\n            } catch (e) {\n              if (\n                isError(e) &&\n                (e.code === 'EINVAL' ||\n                  e.code === 'ENOENT' ||\n                  e.code === 'UNKNOWN')\n              ) {\n                return null\n              }\n              throw e\n            }\n          },\n          async stat(p) {\n            try {\n              return await fs.stat(p)\n            } catch (e) {\n              if (isError(e) && (e.code === 'ENOENT' || e.code === 'ENOTDIR')) {\n                return null\n              }\n              throw e\n            }\n          },\n          // handle shared ignores at top-level as it\n          // avoids over-tracing when we don't need to\n          // and speeds up total trace time\n          ignore(p) {\n            if (sharedIgnoresFn(p)) {\n              return true\n            }\n\n            // if a chunk is attempting to be traced that isn't\n            // in our initial list we need to ignore it to prevent\n            // over tracing as webpack needs to be the source of\n            // truth for which chunks should be included for each entry\n            if (\n              p.includes('.next/server/chunks') &&\n              !chunksToTrace.includes(path.join(outputFileTracingRoot, p))\n            ) {\n              return true\n            }\n            return false\n          },\n        })\n        const reasons = result.reasons\n        const fileList = result.fileList\n        for (const file of result.esmFileList) {\n          fileList.add(file)\n        }\n\n        const parentFilesMap = getFilesMapFromReasons(fileList, reasons)\n        const cachedLookupIgnore = new Map<string, boolean>()\n        const cachedLookupIgnoreMinimal = new Map<string, boolean>()\n\n        for (const [entries, tracedFiles] of [\n          [serverEntries, serverTracedFiles],\n          [minimalServerEntries, minimalServerTracedFiles],\n        ] as Array<[string[], Set<string>]>) {\n          for (const file of entries) {\n            const curFiles = [\n              ...(parentFilesMap\n                .get(path.relative(outputFileTracingRoot, file))\n                ?.keys() || []),\n            ]\n            tracedFiles.add(path.relative(distDir, file).replace(/\\\\/g, '/'))\n\n            for (const curFile of curFiles || []) {\n              const filePath = path.join(outputFileTracingRoot, curFile)\n\n              if (\n                !shouldIgnore(\n                  curFile,\n                  tracedFiles === minimalServerTracedFiles\n                    ? minimalServerIgnoreFn\n                    : serverIgnoreFn,\n                  reasons,\n                  tracedFiles === minimalServerTracedFiles\n                    ? cachedLookupIgnoreMinimal\n                    : cachedLookupIgnore\n                )\n              ) {\n                tracedFiles.add(\n                  path.relative(distDir, filePath).replace(/\\\\/g, '/')\n                )\n              }\n            }\n          }\n        }\n\n        const { entryNameFilesMap } = buildTraceContext?.chunksTrace || {}\n\n        const cachedLookupIgnoreRoutes = new Map<string, boolean>()\n\n        await Promise.all(\n          [\n            ...(entryNameFilesMap\n              ? Object.entries(entryNameFilesMap)\n              : new Map()),\n          ].map(async ([entryName, entryNameFiles]) => {\n            const isApp = entryName.startsWith('app/')\n            const isPages = entryName.startsWith('pages/')\n            let route = entryName\n            if (isApp) {\n              route = normalizeAppPath(route.substring('app'.length))\n            }\n            if (isPages) {\n              route = normalizePagePath(route.substring('pages'.length))\n            }\n\n            // we don't need to trace for automatically statically optimized\n            // pages as they don't have server bundles, note there is\n            // the caveat with flying shuttle mode as it needs this for\n            // detecting changed entries\n            if (staticPages.includes(route)) {\n              return\n            }\n            const entryOutputPath = path.join(\n              distDir,\n              'server',\n              `${entryName}.js`\n            )\n            const traceOutputPath = `${entryOutputPath}.nft.json`\n            const existingTrace = JSON.parse(\n              await fs.readFile(traceOutputPath, 'utf8')\n            ) as {\n              version: number\n              files: string[]\n              fileHashes: Record<string, string>\n            }\n            const traceOutputDir = path.dirname(traceOutputPath)\n            const curTracedFiles = new Set<string>()\n\n            for (const file of [...entryNameFiles, entryOutputPath]) {\n              const curFiles = [\n                ...(parentFilesMap\n                  .get(path.relative(outputFileTracingRoot, file))\n                  ?.keys() || []),\n              ]\n              for (const curFile of curFiles || []) {\n                if (\n                  !shouldIgnore(\n                    curFile,\n                    routeIgnoreFn,\n                    reasons,\n                    cachedLookupIgnoreRoutes\n                  )\n                ) {\n                  const filePath = path.join(outputFileTracingRoot, curFile)\n                  const outputFile = path\n                    .relative(traceOutputDir, filePath)\n                    .replace(/\\\\/g, '/')\n                  curTracedFiles.add(outputFile)\n                }\n              }\n            }\n\n            for (const file of existingTrace.files || []) {\n              curTracedFiles.add(file)\n            }\n\n            await fs.writeFile(\n              traceOutputPath,\n              JSON.stringify({\n                ...existingTrace,\n                files: [...curTracedFiles].sort(),\n              })\n            )\n          })\n        )\n      }\n\n      const moduleTypes = ['app-page', 'pages']\n\n      for (const type of moduleTypes) {\n        const modulePath = require.resolve(\n          `next/dist/server/route-modules/${type}/module.compiled`\n        )\n        const relativeModulePath = path.relative(root, modulePath)\n\n        const contextDir = path.join(\n          path.dirname(modulePath),\n          'vendored',\n          'contexts'\n        )\n\n        for (const item of await fs.readdir(contextDir)) {\n          const itemPath = path.relative(root, path.join(contextDir, item))\n          if (!serverIgnoreFn(itemPath)) {\n            addToTracedFiles(root, itemPath, serverTracedFiles)\n            addToTracedFiles(root, itemPath, minimalServerTracedFiles)\n          }\n        }\n        addToTracedFiles(root, relativeModulePath, serverTracedFiles)\n        addToTracedFiles(root, relativeModulePath, minimalServerTracedFiles)\n      }\n\n      const serverTracedFilesSorted = Array.from(serverTracedFiles)\n      serverTracedFilesSorted.sort()\n      const minimalServerTracedFilesSorted = Array.from(\n        minimalServerTracedFiles\n      )\n      minimalServerTracedFilesSorted.sort()\n\n      await Promise.all([\n        fs.writeFile(\n          nextServerTraceOutput,\n          JSON.stringify({\n            version: 1,\n            files: serverTracedFilesSorted,\n          } as {\n            version: number\n            files: string[]\n          })\n        ),\n        fs.writeFile(\n          nextMinimalTraceOutput,\n          JSON.stringify({\n            version: 1,\n            files: minimalServerTracedFilesSorted,\n          } as {\n            version: number\n            files: string[]\n          })\n        ),\n      ])\n    })\n\n  // apply outputFileTracingIncludes/outputFileTracingExcludes after runTurbotrace\n  const includeExcludeSpan = nextBuildSpan.traceChild('apply-include-excludes')\n  await includeExcludeSpan.traceAsyncFn(async () => {\n    const globOrig =\n      require('next/dist/compiled/glob') as typeof import('next/dist/compiled/glob')\n    const glob = (pattern: string): Promise<string[]> => {\n      return new Promise((resolve, reject) => {\n        globOrig(\n          pattern,\n          { cwd: dir, nodir: true, dot: true },\n          (err, files) => {\n            if (err) {\n              return reject(err)\n            }\n            resolve(files)\n          }\n        )\n      })\n    }\n\n    const { entryNameFilesMap } = buildTraceContext?.chunksTrace || {}\n\n    await Promise.all(\n      [\n        ...(entryNameFilesMap ? Object.entries(entryNameFilesMap) : new Map()),\n      ].map(async ([entryName]) => {\n        const isApp = entryName.startsWith('app/')\n        const isPages = entryName.startsWith('pages/')\n        let route = entryName\n        if (isApp) {\n          route = normalizeAppPath(entryName)\n        }\n        if (isPages) {\n          route = normalizePagePath(entryName)\n        }\n\n        if (staticPages.includes(route)) {\n          return\n        }\n\n        // edge routes have no trace files\n        if (edgeRuntimeRoutes.hasOwnProperty(route)) {\n          return\n        }\n\n        const combinedIncludes = new Set<string>()\n        const combinedExcludes = new Set<string>()\n        for (const curGlob of includeGlobKeys) {\n          const isMatch = picomatch(curGlob, { dot: true, contains: true })\n          if (isMatch(route)) {\n            for (const include of outputFileTracingIncludes[curGlob]) {\n              combinedIncludes.add(include.replace(/\\\\/g, '/'))\n            }\n          }\n        }\n\n        for (const curGlob of excludeGlobKeys) {\n          const isMatch = picomatch(curGlob, { dot: true, contains: true })\n          if (isMatch(route)) {\n            for (const exclude of outputFileTracingExcludes[curGlob]) {\n              combinedExcludes.add(exclude)\n            }\n          }\n        }\n\n        if (!combinedIncludes?.size && !combinedExcludes?.size) {\n          return\n        }\n\n        const traceFile = path.join(\n          distDir,\n          `server`,\n          `${entryName}.js.nft.json`\n        )\n        const pageDir = path.dirname(traceFile)\n        const traceContent = JSON.parse(await fs.readFile(traceFile, 'utf8'))\n        const includes: string[] = []\n        const resolvedTraceIncludes = new Map<string, string[]>()\n\n        if (combinedIncludes?.size) {\n          await Promise.all(\n            [...combinedIncludes].map(async (includeGlob) => {\n              const results = await glob(includeGlob)\n              const resolvedInclude = resolvedTraceIncludes.get(\n                includeGlob\n              ) || [\n                ...results.map((file) => {\n                  return path.relative(pageDir, path.join(dir, file))\n                }),\n              ]\n              includes.push(...resolvedInclude)\n              resolvedTraceIncludes.set(includeGlob, resolvedInclude)\n            })\n          )\n        }\n        const combined = new Set([...traceContent.files, ...includes])\n\n        if (combinedExcludes?.size) {\n          const resolvedGlobs = [...combinedExcludes].map((exclude) =>\n            path.join(dir, exclude)\n          )\n\n          // pre compile before forEach\n          const isMatch = picomatch(resolvedGlobs, {\n            dot: true,\n            contains: true,\n          })\n\n          combined.forEach((file) => {\n            if (isMatch(path.join(pageDir, file))) {\n              combined.delete(file)\n            }\n          })\n        }\n\n        // overwrite trace file with custom includes/excludes\n        await fs.writeFile(\n          traceFile,\n          JSON.stringify({\n            version: traceContent.version,\n            files: [...combined],\n          })\n        )\n      })\n    )\n  })\n\n  debug(`finished build tracing ${Date.now() - startTime}ms`)\n}\n"], "names": ["collectBuildTraces", "debug", "debugOriginal", "shouldIgnore", "file", "serverIgnoreFn", "reasons", "cachedIgnoreFiles", "children", "Set", "has", "get", "set", "add", "reason", "parents", "size", "type", "includes", "allParentsIgnored", "parent", "values", "dir", "config", "distDir", "edgeRuntimeRoutes", "staticPages", "nextBuildSpan", "Span", "name", "hasSsrAmpPages", "buildTraceContext", "outputFileTracingRoot", "isTurbopack", "startTime", "Date", "now", "outputFileTracingIncludes", "outputFileTracingExcludes", "excludeGlobKeys", "Object", "keys", "includeGlobKeys", "<PERSON><PERSON><PERSON><PERSON>", "isTurbotrace", "traceAsyncFn", "nextServerTraceOutput", "path", "join", "nextMinimalTraceOutput", "root", "isStandalone", "output", "sharedEntriesSet", "defaultOverrides", "map", "value", "require", "resolve", "paths", "cache<PERSON><PERSON><PERSON>", "cacheHandlers", "experimental", "push", "isAbsolute", "handler<PERSON><PERSON>", "serverEntries", "filter", "Boolean", "minimalServerEntries", "additionalIgnores", "glob", "picomatch", "for<PERSON>ach", "exclude", "makeIgnoreFn", "ignores", "isMatch", "contains", "dot", "pathname", "startsWith", "sharedIgnores", "ciEnvironment", "hasNextSupport", "TRACE_IGNORES", "sharedIgnoresFn", "serverIgnores", "nonNullable", "minimalServerIgnores", "minimalServerIgnoreFn", "routesIgnores", "routeIgnoreFn", "serverTracedFiles", "minimalServerTracedFiles", "addToTracedFiles", "base", "dest", "relative", "replace", "chunksToTrace", "chunksTrace", "action", "input", "result", "nodeFileTrace", "processCwd", "mixedModules", "readFile", "p", "fs", "e", "isError", "code", "readlink", "stat", "ignore", "fileList", "esmFileList", "parentFilesMap", "getFilesMapFromReasons", "cachedLookupIgnore", "Map", "cachedLookupIgnoreMinimal", "entries", "tracedFiles", "curFiles", "curFile", "filePath", "entryNameFilesMap", "cachedLookupIgnoreRoutes", "Promise", "all", "entryName", "entryNameFiles", "isApp", "isPages", "route", "normalizeAppPath", "substring", "length", "normalizePagePath", "entryOutputPath", "traceOutputPath", "existingTrace", "JSON", "parse", "traceOutputDir", "dirname", "curTracedFiles", "outputFile", "files", "writeFile", "stringify", "sort", "moduleTypes", "modulePath", "relativeModulePath", "contextDir", "item", "readdir", "itemPath", "serverTracedFilesSorted", "Array", "from", "minimalServerTracedFilesSorted", "version", "includeExcludeSpan", "globOrig", "pattern", "reject", "cwd", "nodir", "err", "hasOwnProperty", "combinedIncludes", "combinedExcludes", "curGlob", "include", "traceFile", "pageDir", "traceContent", "resolvedTraceIncludes", "includeGlob", "results", "resolvedInclude", "combined", "resolvedGlobs", "delete"], "mappings": ";;;;+BA0EsBA;;;eAAAA;;;uBA1ED;4CAOd;6DAEU;iEACF;6BACa;gEACG;8DACL;kEACJ;6BACW;qBACH;mCACI;0BACD;gEACb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIpB,MAAMC,QAAQC,IAAAA,cAAa,EAAC;AAE5B,SAASC,aACPC,IAAY,EACZC,cAAyC,EACzCC,OAA6B,EAC7BC,iBAAuC,EACvCC,WAAwB,IAAIC,KAAK;IAEjC,IAAIF,kBAAkBG,GAAG,CAACN,OAAO;QAC/B,OAAOG,kBAAkBI,GAAG,CAACP;IAC/B;IAEA,IAAIC,eAAeD,OAAO;QACxBG,kBAAkBK,GAAG,CAACR,MAAM;QAC5B,OAAO;IACT;IACAI,SAASK,GAAG,CAACT;IAEb,MAAMU,SAASR,QAAQK,GAAG,CAACP;IAC3B,IAAI,CAACU,UAAUA,OAAOC,OAAO,CAACC,IAAI,KAAK,KAAKF,OAAOG,IAAI,CAACC,QAAQ,CAAC,YAAY;QAC3EX,kBAAkBK,GAAG,CAACR,MAAM;QAC5B,OAAO;IACT;IAEA,4CAA4C;IAC5C,4BAA4B;IAC5B,IAAIe,oBAAoB;IAExB,KAAK,MAAMC,UAAUN,OAAOC,OAAO,CAACM,MAAM,GAAI;QAC5C,IAAI,CAACb,SAASE,GAAG,CAACU,SAAS;YACzBZ,SAASK,GAAG,CAACO;YACb,IACE,CAACjB,aACCiB,QACAf,gBACAC,SACAC,mBACAC,WAEF;gBACAW,oBAAoB;gBACpB;YACF;QACF;IACF;IAEAZ,kBAAkBK,GAAG,CAACR,MAAMe;IAC5B,OAAOA;AACT;AAEO,eAAenB,mBAAmB,EACvCsB,GAAG,EACHC,MAAM,EACNC,OAAO,EACPC,iBAAiB,EACjBC,WAAW,EACXC,gBAAgB,IAAIC,WAAI,CAAC;IAAEC,MAAM;AAAQ,EAAE,EAC3CC,cAAc,EACdC,iBAAiB,EACjBC,qBAAqB,EACrBC,WAAW,EAaZ;IACC,MAAMC,YAAYC,KAAKC,GAAG;IAC1BnC,MAAM;IAEN,MAAM,EAAEoC,4BAA4B,CAAC,CAAC,EAAEC,4BAA4B,CAAC,CAAC,EAAE,GACtEf;IACF,MAAMgB,kBAAkBC,OAAOC,IAAI,CAACH;IACpC,MAAMI,kBAAkBF,OAAOC,IAAI,CAACJ;IAEpC,MAAMV,cACHgB,UAAU,CAAC,yBAAyB;QACnCC,cAAc;IAChB,GACCC,YAAY,CAAC;QACZ,MAAMC,wBAAwBC,aAAI,CAACC,IAAI,CACrCxB,SACA;QAEF,MAAMyB,yBAAyBF,aAAI,CAACC,IAAI,CACtCxB,SACA;QAEF,MAAM0B,OAAOlB;QAEb,mEAAmE;QACnE,gBAAgB;QAChB,MAAMmB,eAAe5B,OAAO6B,MAAM,KAAK;QACvC,MAAMC,mBAAmBb,OAAOC,IAAI,CAACa,6BAAgB,EAAEC,GAAG,CAAC,CAACC,QAC1DC,QAAQC,OAAO,CAACF,OAAO;gBACrBG,OAAO;oBAACF,QAAQC,OAAO,CAAC;iBAAiC;YAC3D;QAGF,MAAM,EAAEE,YAAY,EAAE,GAAGrC;QACzB,MAAM,EAAEsC,aAAa,EAAE,GAAGtC,OAAOuC,YAAY;QAE7C,qDAAqD;QACrD,4BAA4B;QAC5B,IAAIF,cAAc;YAChBP,iBAAiBU,IAAI,CACnBN,QAAQC,OAAO,CACbX,aAAI,CAACiB,UAAU,CAACJ,gBACZA,eACAb,aAAI,CAACC,IAAI,CAAC1B,KAAKsC;QAGzB;QAEA,IAAIC,eAAe;YACjB,KAAK,MAAMI,eAAezB,OAAOnB,MAAM,CAACwC,eAAgB;gBACtD,IAAII,aAAa;oBACfZ,iBAAiBU,IAAI,CACnBN,QAAQC,OAAO,CACbX,aAAI,CAACiB,UAAU,CAACC,eACZA,cACAlB,aAAI,CAACC,IAAI,CAAC1B,KAAK2C;gBAGzB;YACF;QACF;QAEA,MAAMC,gBAAgB;eACjBb;eACCF,eACA;gBACEM,QAAQC,OAAO,CAAC;gBAChBD,QAAQC,OAAO,CAAC;gBAChBD,QAAQC,OAAO,CAAC;aACjB,GACD,EAAE;YACND,QAAQC,OAAO,CAAC;SACjB,CAACS,MAAM,CAACC;QAET,MAAMC,uBAAuB;eACxBhB;YACHI,QAAQC,OAAO,CAAC;SACjB,CAACS,MAAM,CAACC;QAET,MAAME,oBAAoB,IAAI7D;QAE9B,KAAK,MAAM8D,QAAQhC,gBAAiB;YAClC,IAAIiC,IAAAA,kBAAS,EAACD,MAAM,gBAAgB;gBAClCjC,yBAAyB,CAACiC,KAAK,CAACE,OAAO,CAAC,CAACC;oBACvCJ,kBAAkBzD,GAAG,CAAC6D;gBACxB;YACF;QACF;QAEA,MAAMC,eAAe,CAACC;YACpB,+BAA+B;YAC/B,MAAMC,UAAUL,IAAAA,kBAAS,EAACI,SAAS;gBACjCE,UAAU;gBACVC,KAAK;YACP;YAEA,OAAO,CAACC;gBACN,IAAIjC,aAAI,CAACiB,UAAU,CAACgB,aAAa,CAACA,SAASC,UAAU,CAAC/B,OAAO;oBAC3D,OAAO;gBACT;gBAEA,OAAO2B,QAAQG;YACjB;QACF;QAEA,MAAME,gBAAgB;YACpB;eACI/B,eAAe,EAAE,GAAG;gBAAC;aAAyC;YAClE;YACA;YACA;YACA;eAEIgC,QAAcC,cAAc,GAC5B;gBACE,wCAAwC;gBACxC,+CAA+C;gBAC/C;aACD,GACD,EAAE;eAEF,CAACtD,iBACD;gBAAC;aAA2D,GAC5D,EAAE;eAEFqB,eAAe,EAAE,GAAGkC,yCAAa;eAClCf;SACJ;QAED,MAAMgB,kBAAkBX,aAAaO;QAErC,MAAMK,gBAAgB;eACjBL;YACH;YACA;YACA;YACA;eACIC,QAAcC,cAAc,GAC5B;gBAAC;gBAA8B;aAA8B,GAC7D,EAAE;SACP,CAACjB,MAAM,CAACqB,wBAAW;QACpB,MAAMnF,iBAAiBsE,aAAaY;QAEpC,MAAME,uBAAuB;eACxBF;YACH;YACA;YACA;SACD;QACD,MAAMG,wBAAwBf,aAAac;QAE3C,MAAME,gBAAgB;eACjBT;YACH,sEAAsE;YACtE,qEAAqE;YACrE,iCAAiC;YACjC;YACA;YACA;SACD,CAACf,MAAM,CAACqB,wBAAW;QAEpB,MAAMI,gBAAgBjB,aAAagB;QAEnC,MAAME,oBAAoB,IAAIpF;QAC9B,MAAMqF,2BAA2B,IAAIrF;QAErC,SAASsF,iBAAiBC,IAAY,EAAE5F,IAAY,EAAE6F,IAAiB;YACrEA,KAAKpF,GAAG,CACNkC,aAAI,CAACmD,QAAQ,CAAC1E,SAASuB,aAAI,CAACC,IAAI,CAACgD,MAAM5F,OAAO+F,OAAO,CAAC,OAAO;QAEjE;QAEA,IAAIhD,cAAc;YAChB4C,iBACE,IACAtC,QAAQC,OAAO,CAAC,gDAChBmC;YAEFE,iBACE,IACAtC,QAAQC,OAAO,CAAC,+CAChBmC;QAEJ;QAEA,IAAI5D,aAAa;YACf8D,iBAAiBvE,SAAS,kBAAkBqE;YAC5CE,iBAAiBvE,SAAS,kBAAkBsE;QAC9C;QAEA;gBAEQ/D;YADN,MAAMqE,gBAA0B;mBAC1BrE,CAAAA,sCAAAA,iCAAAA,kBAAmBsE,WAAW,qBAA9BtE,+BAAgCuE,MAAM,CAACC,KAAK,KAAI,EAAE;mBACnDrC;mBACAG;aACJ;YACD,MAAMmC,SAAS,MAAMC,IAAAA,kBAAa,EAACL,eAAe;gBAChDJ,MAAMhE;gBACN0E,YAAYpF;gBACZqF,cAAc;gBACd,MAAMC,UAASC,CAAC;oBACd,IAAI;wBACF,OAAO,MAAMC,iBAAE,CAACF,QAAQ,CAACC,GAAG;oBAC9B,EAAE,OAAOE,GAAG;wBACV,IAAIC,IAAAA,gBAAO,EAACD,MAAOA,CAAAA,EAAEE,IAAI,KAAK,YAAYF,EAAEE,IAAI,KAAK,QAAO,GAAI;4BAC9D,+DAA+D;4BAC/D,2DAA2D;4BAC3D,oBAAoB;4BACpB,OAAO;wBACT;wBACA,MAAMF;oBACR;gBACF;gBACA,MAAMG,UAASL,CAAC;oBACd,IAAI;wBACF,OAAO,MAAMC,iBAAE,CAACI,QAAQ,CAACL;oBAC3B,EAAE,OAAOE,GAAG;wBACV,IACEC,IAAAA,gBAAO,EAACD,MACPA,CAAAA,EAAEE,IAAI,KAAK,YACVF,EAAEE,IAAI,KAAK,YACXF,EAAEE,IAAI,KAAK,SAAQ,GACrB;4BACA,OAAO;wBACT;wBACA,MAAMF;oBACR;gBACF;gBACA,MAAMI,MAAKN,CAAC;oBACV,IAAI;wBACF,OAAO,MAAMC,iBAAE,CAACK,IAAI,CAACN;oBACvB,EAAE,OAAOE,GAAG;wBACV,IAAIC,IAAAA,gBAAO,EAACD,MAAOA,CAAAA,EAAEE,IAAI,KAAK,YAAYF,EAAEE,IAAI,KAAK,SAAQ,GAAI;4BAC/D,OAAO;wBACT;wBACA,MAAMF;oBACR;gBACF;gBACA,2CAA2C;gBAC3C,4CAA4C;gBAC5C,iCAAiC;gBACjCK,QAAOP,CAAC;oBACN,IAAIvB,gBAAgBuB,IAAI;wBACtB,OAAO;oBACT;oBAEA,mDAAmD;oBACnD,sDAAsD;oBACtD,oDAAoD;oBACpD,2DAA2D;oBAC3D,IACEA,EAAE3F,QAAQ,CAAC,0BACX,CAACkF,cAAclF,QAAQ,CAAC6B,aAAI,CAACC,IAAI,CAAChB,uBAAuB6E,KACzD;wBACA,OAAO;oBACT;oBACA,OAAO;gBACT;YACF;YACA,MAAMvG,UAAUkG,OAAOlG,OAAO;YAC9B,MAAM+G,WAAWb,OAAOa,QAAQ;YAChC,KAAK,MAAMjH,QAAQoG,OAAOc,WAAW,CAAE;gBACrCD,SAASxG,GAAG,CAACT;YACf;YAEA,MAAMmH,iBAAiBC,IAAAA,kDAAsB,EAACH,UAAU/G;YACxD,MAAMmH,qBAAqB,IAAIC;YAC/B,MAAMC,4BAA4B,IAAID;YAEtC,KAAK,MAAM,CAACE,SAASC,YAAY,IAAI;gBACnC;oBAAC3D;oBAAe2B;iBAAkB;gBAClC;oBAACxB;oBAAsByB;iBAAyB;aACjD,CAAoC;gBACnC,KAAK,MAAM1F,QAAQwH,QAAS;wBAEpBL;oBADN,MAAMO,WAAW;2BACXP,EAAAA,sBAAAA,eACD5G,GAAG,CAACoC,aAAI,CAACmD,QAAQ,CAAClE,uBAAuB5B,2BADxCmH,oBAEA9E,IAAI,OAAM,EAAE;qBACjB;oBACDoF,YAAYhH,GAAG,CAACkC,aAAI,CAACmD,QAAQ,CAAC1E,SAASpB,MAAM+F,OAAO,CAAC,OAAO;oBAE5D,KAAK,MAAM4B,WAAWD,YAAY,EAAE,CAAE;wBACpC,MAAME,WAAWjF,aAAI,CAACC,IAAI,CAAChB,uBAAuB+F;wBAElD,IACE,CAAC5H,aACC4H,SACAF,gBAAgB/B,2BACZJ,wBACArF,gBACJC,SACAuH,gBAAgB/B,2BACZ6B,4BACAF,qBAEN;4BACAI,YAAYhH,GAAG,CACbkC,aAAI,CAACmD,QAAQ,CAAC1E,SAASwG,UAAU7B,OAAO,CAAC,OAAO;wBAEpD;oBACF;gBACF;YACF;YAEA,MAAM,EAAE8B,iBAAiB,EAAE,GAAGlG,CAAAA,qCAAAA,kBAAmBsE,WAAW,KAAI,CAAC;YAEjE,MAAM6B,2BAA2B,IAAIR;YAErC,MAAMS,QAAQC,GAAG,CACf;mBACMH,oBACAzF,OAAOoF,OAAO,CAACK,qBACf,IAAIP;aACT,CAACnE,GAAG,CAAC,OAAO,CAAC8E,WAAWC,eAAe;gBACtC,MAAMC,QAAQF,UAAUpD,UAAU,CAAC;gBACnC,MAAMuD,UAAUH,UAAUpD,UAAU,CAAC;gBACrC,IAAIwD,QAAQJ;gBACZ,IAAIE,OAAO;oBACTE,QAAQC,IAAAA,0BAAgB,EAACD,MAAME,SAAS,CAAC,MAAMC,MAAM;gBACvD;gBACA,IAAIJ,SAAS;oBACXC,QAAQI,IAAAA,oCAAiB,EAACJ,MAAME,SAAS,CAAC,QAAQC,MAAM;gBAC1D;gBAEA,gEAAgE;gBAChE,yDAAyD;gBACzD,2DAA2D;gBAC3D,4BAA4B;gBAC5B,IAAIlH,YAAYR,QAAQ,CAACuH,QAAQ;oBAC/B;gBACF;gBACA,MAAMK,kBAAkB/F,aAAI,CAACC,IAAI,CAC/BxB,SACA,UACA,GAAG6G,UAAU,GAAG,CAAC;gBAEnB,MAAMU,kBAAkB,GAAGD,gBAAgB,SAAS,CAAC;gBACrD,MAAME,gBAAgBC,KAAKC,KAAK,CAC9B,MAAMpC,iBAAE,CAACF,QAAQ,CAACmC,iBAAiB;gBAMrC,MAAMI,iBAAiBpG,aAAI,CAACqG,OAAO,CAACL;gBACpC,MAAMM,iBAAiB,IAAI5I;gBAE3B,KAAK,MAAML,QAAQ;uBAAIkI;oBAAgBQ;iBAAgB,CAAE;wBAEjDvB;oBADN,MAAMO,WAAW;2BACXP,EAAAA,sBAAAA,eACD5G,GAAG,CAACoC,aAAI,CAACmD,QAAQ,CAAClE,uBAAuB5B,2BADxCmH,oBAEA9E,IAAI,OAAM,EAAE;qBACjB;oBACD,KAAK,MAAMsF,WAAWD,YAAY,EAAE,CAAE;wBACpC,IACE,CAAC3H,aACC4H,SACAnC,eACAtF,SACA4H,2BAEF;4BACA,MAAMF,WAAWjF,aAAI,CAACC,IAAI,CAAChB,uBAAuB+F;4BAClD,MAAMuB,aAAavG,aAAI,CACpBmD,QAAQ,CAACiD,gBAAgBnB,UACzB7B,OAAO,CAAC,OAAO;4BAClBkD,eAAexI,GAAG,CAACyI;wBACrB;oBACF;gBACF;gBAEA,KAAK,MAAMlJ,QAAQ4I,cAAcO,KAAK,IAAI,EAAE,CAAE;oBAC5CF,eAAexI,GAAG,CAACT;gBACrB;gBAEA,MAAM0G,iBAAE,CAAC0C,SAAS,CAChBT,iBACAE,KAAKQ,SAAS,CAAC;oBACb,GAAGT,aAAa;oBAChBO,OAAO;2BAAIF;qBAAe,CAACK,IAAI;gBACjC;YAEJ;QAEJ;QAEA,MAAMC,cAAc;YAAC;YAAY;SAAQ;QAEzC,KAAK,MAAM1I,QAAQ0I,YAAa;YAC9B,MAAMC,aAAanG,QAAQC,OAAO,CAChC,CAAC,+BAA+B,EAAEzC,KAAK,gBAAgB,CAAC;YAE1D,MAAM4I,qBAAqB9G,aAAI,CAACmD,QAAQ,CAAChD,MAAM0G;YAE/C,MAAME,aAAa/G,aAAI,CAACC,IAAI,CAC1BD,aAAI,CAACqG,OAAO,CAACQ,aACb,YACA;YAGF,KAAK,MAAMG,QAAQ,CAAA,MAAMjD,iBAAE,CAACkD,OAAO,CAACF,WAAU,EAAG;gBAC/C,MAAMG,WAAWlH,aAAI,CAACmD,QAAQ,CAAChD,MAAMH,aAAI,CAACC,IAAI,CAAC8G,YAAYC;gBAC3D,IAAI,CAAC1J,eAAe4J,WAAW;oBAC7BlE,iBAAiB7C,MAAM+G,UAAUpE;oBACjCE,iBAAiB7C,MAAM+G,UAAUnE;gBACnC;YACF;YACAC,iBAAiB7C,MAAM2G,oBAAoBhE;YAC3CE,iBAAiB7C,MAAM2G,oBAAoB/D;QAC7C;QAEA,MAAMoE,0BAA0BC,MAAMC,IAAI,CAACvE;QAC3CqE,wBAAwBR,IAAI;QAC5B,MAAMW,iCAAiCF,MAAMC,IAAI,CAC/CtE;QAEFuE,+BAA+BX,IAAI;QAEnC,MAAMvB,QAAQC,GAAG,CAAC;YAChBtB,iBAAE,CAAC0C,SAAS,CACV1G,uBACAmG,KAAKQ,SAAS,CAAC;gBACba,SAAS;gBACTf,OAAOW;YACT;YAKFpD,iBAAE,CAAC0C,SAAS,CACVvG,wBACAgG,KAAKQ,SAAS,CAAC;gBACba,SAAS;gBACTf,OAAOc;YACT;SAKH;IACH;IAEF,gFAAgF;IAChF,MAAME,qBAAqB5I,cAAcgB,UAAU,CAAC;IACpD,MAAM4H,mBAAmB1H,YAAY,CAAC;QACpC,MAAM2H,WACJ/G,QAAQ;QACV,MAAMc,OAAO,CAACkG;YACZ,OAAO,IAAItC,QAAQ,CAACzE,SAASgH;gBAC3BF,SACEC,SACA;oBAAEE,KAAKrJ;oBAAKsJ,OAAO;oBAAM7F,KAAK;gBAAK,GACnC,CAAC8F,KAAKtB;oBACJ,IAAIsB,KAAK;wBACP,OAAOH,OAAOG;oBAChB;oBACAnH,QAAQ6F;gBACV;YAEJ;QACF;QAEA,MAAM,EAAEtB,iBAAiB,EAAE,GAAGlG,CAAAA,qCAAAA,kBAAmBsE,WAAW,KAAI,CAAC;QAEjE,MAAM8B,QAAQC,GAAG,CACf;eACMH,oBAAoBzF,OAAOoF,OAAO,CAACK,qBAAqB,IAAIP;SACjE,CAACnE,GAAG,CAAC,OAAO,CAAC8E,UAAU;YACtB,MAAME,QAAQF,UAAUpD,UAAU,CAAC;YACnC,MAAMuD,UAAUH,UAAUpD,UAAU,CAAC;YACrC,IAAIwD,QAAQJ;YACZ,IAAIE,OAAO;gBACTE,QAAQC,IAAAA,0BAAgB,EAACL;YAC3B;YACA,IAAIG,SAAS;gBACXC,QAAQI,IAAAA,oCAAiB,EAACR;YAC5B;YAEA,IAAI3G,YAAYR,QAAQ,CAACuH,QAAQ;gBAC/B;YACF;YAEA,kCAAkC;YAClC,IAAIhH,kBAAkBqJ,cAAc,CAACrC,QAAQ;gBAC3C;YACF;YAEA,MAAMsC,mBAAmB,IAAItK;YAC7B,MAAMuK,mBAAmB,IAAIvK;YAC7B,KAAK,MAAMwK,WAAWvI,gBAAiB;gBACrC,MAAMmC,UAAUL,IAAAA,kBAAS,EAACyG,SAAS;oBAAElG,KAAK;oBAAMD,UAAU;gBAAK;gBAC/D,IAAID,QAAQ4D,QAAQ;oBAClB,KAAK,MAAMyC,WAAW7I,yBAAyB,CAAC4I,QAAQ,CAAE;wBACxDF,iBAAiBlK,GAAG,CAACqK,QAAQ/E,OAAO,CAAC,OAAO;oBAC9C;gBACF;YACF;YAEA,KAAK,MAAM8E,WAAW1I,gBAAiB;gBACrC,MAAMsC,UAAUL,IAAAA,kBAAS,EAACyG,SAAS;oBAAElG,KAAK;oBAAMD,UAAU;gBAAK;gBAC/D,IAAID,QAAQ4D,QAAQ;oBAClB,KAAK,MAAM/D,WAAWpC,yBAAyB,CAAC2I,QAAQ,CAAE;wBACxDD,iBAAiBnK,GAAG,CAAC6D;oBACvB;gBACF;YACF;YAEA,IAAI,EAACqG,oCAAAA,iBAAkB/J,IAAI,KAAI,EAACgK,oCAAAA,iBAAkBhK,IAAI,GAAE;gBACtD;YACF;YAEA,MAAMmK,YAAYpI,aAAI,CAACC,IAAI,CACzBxB,SACA,CAAC,MAAM,CAAC,EACR,GAAG6G,UAAU,YAAY,CAAC;YAE5B,MAAM+C,UAAUrI,aAAI,CAACqG,OAAO,CAAC+B;YAC7B,MAAME,eAAepC,KAAKC,KAAK,CAAC,MAAMpC,iBAAE,CAACF,QAAQ,CAACuE,WAAW;YAC7D,MAAMjK,WAAqB,EAAE;YAC7B,MAAMoK,wBAAwB,IAAI5D;YAElC,IAAIqD,oCAAAA,iBAAkB/J,IAAI,EAAE;gBAC1B,MAAMmH,QAAQC,GAAG,CACf;uBAAI2C;iBAAiB,CAACxH,GAAG,CAAC,OAAOgI;oBAC/B,MAAMC,UAAU,MAAMjH,KAAKgH;oBAC3B,MAAME,kBAAkBH,sBAAsB3K,GAAG,CAC/C4K,gBACG;2BACAC,QAAQjI,GAAG,CAAC,CAACnD;4BACd,OAAO2C,aAAI,CAACmD,QAAQ,CAACkF,SAASrI,aAAI,CAACC,IAAI,CAAC1B,KAAKlB;wBAC/C;qBACD;oBACDc,SAAS6C,IAAI,IAAI0H;oBACjBH,sBAAsB1K,GAAG,CAAC2K,aAAaE;gBACzC;YAEJ;YACA,MAAMC,WAAW,IAAIjL,IAAI;mBAAI4K,aAAa9B,KAAK;mBAAKrI;aAAS;YAE7D,IAAI8J,oCAAAA,iBAAkBhK,IAAI,EAAE;gBAC1B,MAAM2K,gBAAgB;uBAAIX;iBAAiB,CAACzH,GAAG,CAAC,CAACmB,UAC/C3B,aAAI,CAACC,IAAI,CAAC1B,KAAKoD;gBAGjB,6BAA6B;gBAC7B,MAAMG,UAAUL,IAAAA,kBAAS,EAACmH,eAAe;oBACvC5G,KAAK;oBACLD,UAAU;gBACZ;gBAEA4G,SAASjH,OAAO,CAAC,CAACrE;oBAChB,IAAIyE,QAAQ9B,aAAI,CAACC,IAAI,CAACoI,SAAShL,QAAQ;wBACrCsL,SAASE,MAAM,CAACxL;oBAClB;gBACF;YACF;YAEA,qDAAqD;YACrD,MAAM0G,iBAAE,CAAC0C,SAAS,CAChB2B,WACAlC,KAAKQ,SAAS,CAAC;gBACba,SAASe,aAAaf,OAAO;gBAC7Bf,OAAO;uBAAImC;iBAAS;YACtB;QAEJ;IAEJ;IAEAzL,MAAM,CAAC,uBAAuB,EAAEkC,KAAKC,GAAG,KAAKF,UAAU,EAAE,CAAC;AAC5D", "ignoreList": [0]}