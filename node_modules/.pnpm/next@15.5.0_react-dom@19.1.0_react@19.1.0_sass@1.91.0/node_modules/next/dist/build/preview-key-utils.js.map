{"version": 3, "sources": ["../../src/build/preview-key-utils.ts"], "sourcesContent": ["import path from 'path'\nimport fs from 'fs'\nimport crypto from 'crypto'\nimport type { __ApiPreviewProps } from '../server/api-utils'\nimport { getStorageDirectory } from '../server/cache-dir'\n\nconst CONFIG_FILE = '.previewinfo'\nconst PREVIEW_ID = 'previewModeId'\nconst PREVIEW_SIGNING_KEY = 'previewModeSigningKey'\nconst PREVIEW_ENCRYPTION_KEY = 'previewModeEncryptionKey'\nconst PREVIEW_EXPIRE_AT = 'expireAt'\nconst EXPIRATION = 1000 * 60 * 60 * 24 * 14 // 14 days\n\nasync function writeCache(distDir: string, config: __ApiPreviewProps) {\n  const cacheBaseDir = getStorageDirectory(distDir)\n  if (!cacheBaseDir) return\n\n  const configPath = path.join(cacheBaseDir, CONFIG_FILE)\n  if (!fs.existsSync(cacheBaseDir)) {\n    await fs.promises.mkdir(cacheBaseDir, { recursive: true })\n  }\n  await fs.promises.writeFile(\n    configPath,\n    JSON.stringify({\n      [PREVIEW_ID]: config.previewModeId,\n      [PREVIEW_SIGNING_KEY]: config.previewModeSigningKey,\n      [PREVIEW_ENCRYPTION_KEY]: config.previewModeEncryptionKey,\n      [PREVIEW_EXPIRE_AT]: Date.now() + EXPIRATION,\n    })\n  )\n}\n\nfunction generateConfig() {\n  return {\n    previewModeId: crypto.randomBytes(16).toString('hex'),\n    previewModeSigningKey: crypto.randomBytes(32).toString('hex'),\n    previewModeEncryptionKey: crypto.randomBytes(32).toString('hex'),\n  }\n}\n\n// This utility is used to get a key for the cache directory. If the\n// key is not present, it will generate a new one and store it in the\n// cache directory inside dist.\n// The key will also expire after a certain amount of time. Once it\n// expires, a new one will be generated.\nexport async function generatePreviewKeys({\n  distDir,\n  isBuild,\n}: {\n  distDir: string\n  isBuild: boolean\n}): Promise<__ApiPreviewProps> {\n  const cacheBaseDir = getStorageDirectory(distDir)\n\n  if (!cacheBaseDir) {\n    // There's no persistent storage available. We generate a new config.\n    // This also covers development time.\n    return generateConfig()\n  }\n\n  const configPath = path.join(cacheBaseDir, CONFIG_FILE)\n  async function tryReadCachedConfig(): Promise<false | __ApiPreviewProps> {\n    if (!fs.existsSync(configPath)) return false\n    try {\n      const config = JSON.parse(await fs.promises.readFile(configPath, 'utf8'))\n      if (!config) return false\n      if (\n        typeof config[PREVIEW_ID] !== 'string' ||\n        typeof config[PREVIEW_ENCRYPTION_KEY] !== 'string' ||\n        typeof config[PREVIEW_SIGNING_KEY] !== 'string' ||\n        typeof config[PREVIEW_EXPIRE_AT] !== 'number'\n      ) {\n        return false\n      }\n      // For build time, we need to rotate the key if it's expired. Otherwise\n      // (next start) we have to keep the key as it is so the runtime key matches\n      // the build time key.\n      if (isBuild && config[PREVIEW_EXPIRE_AT] < Date.now()) {\n        return false\n      }\n\n      return {\n        previewModeId: config[PREVIEW_ID],\n        previewModeSigningKey: config[PREVIEW_SIGNING_KEY],\n        previewModeEncryptionKey: config[PREVIEW_ENCRYPTION_KEY],\n      }\n    } catch (e) {\n      // Broken config file. We should generate a new key and overwrite it.\n      return false\n    }\n  }\n  const maybeValidConfig = await tryReadCachedConfig()\n  if (maybeValidConfig !== false) {\n    return maybeValidConfig\n  }\n  const config = generateConfig()\n  await writeCache(distDir, config)\n\n  return config\n}\n"], "names": ["generatePreviewKeys", "CONFIG_FILE", "PREVIEW_ID", "PREVIEW_SIGNING_KEY", "PREVIEW_ENCRYPTION_KEY", "PREVIEW_EXPIRE_AT", "EXPIRATION", "writeCache", "distDir", "config", "cacheBaseDir", "getStorageDirectory", "config<PERSON><PERSON>", "path", "join", "fs", "existsSync", "promises", "mkdir", "recursive", "writeFile", "JSON", "stringify", "previewModeId", "previewModeSigningKey", "previewModeEncryptionKey", "Date", "now", "generateConfig", "crypto", "randomBytes", "toString", "isBuild", "tryReadCachedConfig", "parse", "readFile", "e", "maybeValidConfig"], "mappings": ";;;;+BA6<PERSON><PERSON>;;;eAAAA;;;6DA7CL;2DACF;+DAC<PERSON>;0BAEiB;;;;;;AAEpC,MAAMC,cAAc;AACpB,MAAMC,aAAa;AACnB,MAAMC,sBAAsB;AAC5B,MAAMC,yBAAyB;AAC/B,MAAMC,oBAAoB;AAC1B,MAAMC,aAAa,OAAO,KAAK,KAAK,KAAK,GAAG,UAAU;;AAEtD,eAAeC,WAAWC,OAAe,EAAEC,MAAyB;IAClE,MAAMC,eAAeC,IAAAA,6BAAmB,EAACH;IACzC,IAAI,CAACE,cAAc;IAEnB,MAAME,aAAaC,aAAI,CAACC,IAAI,CAACJ,cAAcT;IAC3C,IAAI,CAACc,WAAE,CAACC,UAAU,CAACN,eAAe;QAChC,MAAMK,WAAE,CAACE,QAAQ,CAACC,KAAK,CAACR,cAAc;YAAES,WAAW;QAAK;IAC1D;IACA,MAAMJ,WAAE,CAACE,QAAQ,CAACG,SAAS,CACzBR,YACAS,KAAKC,SAAS,CAAC;QACb,CAACpB,WAAW,EAAEO,OAAOc,aAAa;QAClC,CAACpB,oBAAoB,EAAEM,OAAOe,qBAAqB;QACnD,CAACpB,uBAAuB,EAAEK,OAAOgB,wBAAwB;QACzD,CAACpB,kBAAkB,EAAEqB,KAAKC,GAAG,KAAKrB;IACpC;AAEJ;AAEA,SAASsB;IACP,OAAO;QACLL,eAAeM,eAAM,CAACC,WAAW,CAAC,IAAIC,QAAQ,CAAC;QAC/CP,uBAAuBK,eAAM,CAACC,WAAW,CAAC,IAAIC,QAAQ,CAAC;QACvDN,0BAA0BI,eAAM,CAACC,WAAW,CAAC,IAAIC,QAAQ,CAAC;IAC5D;AACF;AAOO,eAAe/B,oBAAoB,EACxCQ,OAAO,EACPwB,OAAO,EAIR;IACC,MAAMtB,eAAeC,IAAAA,6BAAmB,EAACH;IAEzC,IAAI,CAACE,cAAc;QACjB,qEAAqE;QACrE,qCAAqC;QACrC,OAAOkB;IACT;IAEA,MAAMhB,aAAaC,aAAI,CAACC,IAAI,CAACJ,cAAcT;IAC3C,eAAegC;QACb,IAAI,CAAClB,WAAE,CAACC,UAAU,CAACJ,aAAa,OAAO;QACvC,IAAI;YACF,MAAMH,SAASY,KAAKa,KAAK,CAAC,MAAMnB,WAAE,CAACE,QAAQ,CAACkB,QAAQ,CAACvB,YAAY;YACjE,IAAI,CAACH,QAAQ,OAAO;YACpB,IACE,OAAOA,MAAM,CAACP,WAAW,KAAK,YAC9B,OAAOO,MAAM,CAACL,uBAAuB,KAAK,YAC1C,OAAOK,MAAM,CAACN,oBAAoB,KAAK,YACvC,OAAOM,MAAM,CAACJ,kBAAkB,KAAK,UACrC;gBACA,OAAO;YACT;YACA,uEAAuE;YACvE,2EAA2E;YAC3E,sBAAsB;YACtB,IAAI2B,WAAWvB,MAAM,CAACJ,kBAAkB,GAAGqB,KAAKC,GAAG,IAAI;gBACrD,OAAO;YACT;YAEA,OAAO;gBACLJ,eAAed,MAAM,CAACP,WAAW;gBACjCsB,uBAAuBf,MAAM,CAACN,oBAAoB;gBAClDsB,0BAA0BhB,MAAM,CAACL,uBAAuB;YAC1D;QACF,EAAE,OAAOgC,GAAG;YACV,qEAAqE;YACrE,OAAO;QACT;IACF;IACA,MAAMC,mBAAmB,MAAMJ;IAC/B,IAAII,qBAAqB,OAAO;QAC9B,OAAOA;IACT;IACA,MAAM5B,SAASmB;IACf,MAAMrB,WAAWC,SAASC;IAE1B,OAAOA;AACT", "ignoreList": [0]}