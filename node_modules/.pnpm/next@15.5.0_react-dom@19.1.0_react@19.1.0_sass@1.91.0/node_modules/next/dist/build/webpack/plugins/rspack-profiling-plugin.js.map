{"version": 3, "sources": ["../../../../src/build/webpack/plugins/rspack-profiling-plugin.ts"], "sourcesContent": ["// A basic implementation to allow loaders access to loaderContext.currentTraceSpan\n\nimport type { Span } from '../../../trace'\n\nimport { getRspackCore } from '../../../shared/lib/get-rspack'\n\nconst pluginName = 'RspackProfilingPlugin'\nconst moduleSpansByCompilation = new WeakMap()\nexport const compilationSpans: WeakMap<any, Span> = new WeakMap()\n\nexport class RspackProfilingPlugin {\n  runWebpackSpan: Span\n\n  constructor({ runWebpackSpan }: { runWebpackSpan: Span }) {\n    this.runWebpackSpan = runWebpackSpan\n  }\n\n  apply(compiler: any) {\n    compiler.hooks.thisCompilation.tap(\n      { name: pluginName, stage: -Infinity },\n      (compilation: any) => {\n        const rspack = getRspackCore()\n\n        moduleSpansByCompilation.set(compilation, new WeakMap())\n        compilationSpans.set(\n          compilation,\n          this.runWebpackSpan.traceChild('compilation-' + compilation.name)\n        )\n\n        const compilationSpan = this.runWebpackSpan.traceChild(\n          `compilation-${compilation.name}`\n        )\n\n        const moduleHooks = rspack.NormalModule.getCompilationHooks(compilation)\n        moduleHooks.loader.tap(\n          pluginName,\n          (loaderContext: any, module: any) => {\n            const moduleSpan = moduleSpansByCompilation\n              .get(compilation)\n              ?.get(module)\n            loaderContext.currentTraceSpan = moduleSpan\n          }\n        )\n\n        compilation.hooks.buildModule.tap(pluginName, (module: any) => {\n          const span = compilationSpan.traceChild('build-module')\n          span.setAttribute('name', module.userRequest)\n          span.setAttribute('layer', module.layer)\n\n          moduleSpansByCompilation?.get(compilation)?.set(module, span)\n        })\n\n        compilation.hooks.succeedModule.tap(pluginName, (module: any) => {\n          moduleSpansByCompilation?.get(compilation)?.get(module)?.stop()\n        })\n      }\n    )\n  }\n}\n"], "names": ["RspackProfilingPlugin", "compilationSpans", "pluginName", "moduleSpansByCompilation", "WeakMap", "constructor", "runWebpackSpan", "apply", "compiler", "hooks", "thisCompilation", "tap", "name", "stage", "Infinity", "compilation", "rspack", "getRspackCore", "set", "<PERSON><PERSON><PERSON><PERSON>", "compilationSpan", "moduleHooks", "NormalModule", "getCompilationHooks", "loader", "loaderContext", "module", "moduleSpan", "get", "currentTraceSpan", "buildModule", "span", "setAttribute", "userRequest", "layer", "succeedModule", "stop"], "mappings": "AAAA,mFAAmF;;;;;;;;;;;;;;;;IAUtEA,qBAAqB;eAArBA;;IAFAC,gBAAgB;eAAhBA;;;2BAJiB;AAE9B,MAAMC,aAAa;AACnB,MAAMC,2BAA2B,IAAIC;AAC9B,MAAMH,mBAAuC,IAAIG;AAEjD,MAAMJ;IAGXK,YAAY,EAAEC,cAAc,EAA4B,CAAE;QACxD,IAAI,CAACA,cAAc,GAAGA;IACxB;IAEAC,MAAMC,QAAa,EAAE;QACnBA,SAASC,KAAK,CAACC,eAAe,CAACC,GAAG,CAChC;YAAEC,MAAMV;YAAYW,OAAO,CAACC;QAAS,GACrC,CAACC;YACC,MAAMC,SAASC,IAAAA,wBAAa;YAE5Bd,yBAAyBe,GAAG,CAACH,aAAa,IAAIX;YAC9CH,iBAAiBiB,GAAG,CAClBH,aACA,IAAI,CAACT,cAAc,CAACa,UAAU,CAAC,iBAAiBJ,YAAYH,IAAI;YAGlE,MAAMQ,kBAAkB,IAAI,CAACd,cAAc,CAACa,UAAU,CACpD,CAAC,YAAY,EAAEJ,YAAYH,IAAI,EAAE;YAGnC,MAAMS,cAAcL,OAAOM,YAAY,CAACC,mBAAmB,CAACR;YAC5DM,YAAYG,MAAM,CAACb,GAAG,CACpBT,YACA,CAACuB,eAAoBC;oBACAvB;gBAAnB,MAAMwB,cAAaxB,gCAAAA,yBAChByB,GAAG,CAACb,iCADYZ,8BAEfyB,GAAG,CAACF;gBACRD,cAAcI,gBAAgB,GAAGF;YACnC;YAGFZ,YAAYN,KAAK,CAACqB,WAAW,CAACnB,GAAG,CAACT,YAAY,CAACwB;oBAK7CvB;gBAJA,MAAM4B,OAAOX,gBAAgBD,UAAU,CAAC;gBACxCY,KAAKC,YAAY,CAAC,QAAQN,QAAOO,WAAW;gBAC5CF,KAAKC,YAAY,CAAC,SAASN,QAAOQ,KAAK;gBAEvC/B,6CAAAA,gCAAAA,yBAA0ByB,GAAG,CAACb,iCAA9BZ,8BAA4Ce,GAAG,CAACQ,SAAQK;YAC1D;YAEAhB,YAAYN,KAAK,CAAC0B,aAAa,CAACxB,GAAG,CAACT,YAAY,CAACwB;oBAC/CvB,mCAAAA;gBAAAA,6CAAAA,gCAAAA,yBAA0ByB,GAAG,CAACb,kCAA9BZ,oCAAAA,8BAA4CyB,GAAG,CAACF,6BAAhDvB,kCAAyDiC,IAAI;YAC/D;QACF;IAEJ;AACF", "ignoreList": [0]}